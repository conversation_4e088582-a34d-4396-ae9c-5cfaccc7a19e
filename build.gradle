buildscript {
    dependencies {
        classpath 'com.google.gms:google-services:4.4.3'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.4'
//        classpath "com.applovin.quality:AppLovinQualityServiceGradlePlugin:+"
    }
}// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '8.11.0' apply false
    id 'com.android.library' version '8.11.0' apply false
    id 'org.jetbrains.kotlin.android' version '2.2.0' apply false
    id 'org.jetbrains.kotlin.plugin.compose' version '2.2.0' apply false
    id 'com.google.devtools.ksp' version '2.2.0-2.0.2' apply false
    id 'org.jetbrains.kotlin.plugin.serialization' version '2.2.0' apply false
}