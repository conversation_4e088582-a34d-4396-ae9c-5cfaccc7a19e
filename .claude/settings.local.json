{"permissions": {"allow": ["Bash(git commit:*)", "Bash(git add:*)", "WebFetch(domain:docs.tradplusad.com)", "WebFetch(domain:mvnrepository.com)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(rg:*)", "Bash(rm:*)", "Bash(git rebase:*)", "Bash(git reset:*)", "Bash(git checkout:*)", "Bash(git cherry-pick:*)", "Bash(GIT_SEQUENCE_EDITOR=\"sed -i ''s/pick/reword/g''\" git rebase -i HEAD~3)", "Bash(EDITOR=true git rebase -i HEAD~3 --exec 'echo \"\"skip\"\"')", "Bash(git filter-branch:*)", "Bash(grep:*)", "Bash(for file in )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/screen/withdraw/WithdrawScreen.kt\" )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/screen/redeempicture/RedeemPicture.kt\" )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/screen/redeemedcoupon/RedeemedCouponScreen.kt\" )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/screen/redeemedcash/RedeemedCashScreen.kt\" )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/screen/permissionsmanager/PermissionsManagerScreen.kt\")", "Bash(do)", "Bash(echo \"=== $file ===\")", "Bash(done)", "Bash(for file in )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/dialog/rewardedloading/RewardedLoadingDialog.kt\" )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/common/Dialog.kt\" )", "Bash(\"/Users/<USER>/Documents/dev/step2/app/src/main/java/dev/step/app/ui/common/BigBadgeDialog.kt\")"], "deny": []}}