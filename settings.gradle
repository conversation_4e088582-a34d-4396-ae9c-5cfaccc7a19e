pluginManagement {
    repositories {
        google()
        mavenCentral()
//        maven { url 'https://repo1.maven.org/maven2' }
//        maven { url 'https://artifacts.applovin.com/android' }
//        maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
//        maven { url 'https://oss.sonatype.org/content/repositories/snapshots' }
        maven { url "https://android-sdk.is.com" }
        maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }
        maven { url "https://artifact.bytedance.com/repository/pangle" }
        maven { url "https://verve.jfrog.io/artifactory/verve-gradle-release" }
        maven { url "https://bitbucket.org/sdkcenter/sdkcenter/raw/release" }
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
//        maven { url 'https://repo1.maven.org/maven2' }
        maven { url "https://jitpack.io" }
//        maven { url 'https://artifacts.applovin.com/android' }
        maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
//        maven { url 'https://oss.sonatype.org/content/repositories/snapshots' }
        maven { url "https://android-sdk.is.com" }
        maven { url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea" }
        maven { url "https://artifact.bytedance.com/repository/pangle" }
        maven { url "https://verve.jfrog.io/artifactory/verve-gradle-release" }
        maven { url "https://bitbucket.org/sdkcenter/sdkcenter/raw/release" }
    }
}

rootProject.name = "StepApp2"
include ':app'
