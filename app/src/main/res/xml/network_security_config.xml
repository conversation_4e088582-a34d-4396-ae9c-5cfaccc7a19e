<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!--    <base-config cleartextTrafficPermitted="true">-->
    <!--        <trust-anchors>-->
    <!--            <certificates overridePins="true" src="system"/>-->
    <!--            <certificates overridePins="true" src="user"/>-->
    <!--        </trust-anchors>-->
    <!--    </base-config>-->

    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">127.0.0.1</domain>
    </domain-config>


    <domain-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
        <domain includeSubdomains="true">cdn-creatives-akamai-prd.unityads.unity3d.com/</domain>
        <domain includeSubdomains="true">cdn-creatives-akamaistls-prd.unityads.unity3d.com/</domain>
        <domain includeSubdomains="true">cdn-creatives-akamaistls-re-prd.unityads.unity3d.com/
        </domain>
        <domain includeSubdomains="true">cdn-creatives-geocdn-prd.unityads.unity3d.com/</domain>
        <domain includeSubdomains="true">cdn-creatives-prd.unityads.unity3d.com/</domain>
        <domain includeSubdomains="true">cdn-store-icons-akamai-prd.unityads.unity3d.com/</domain>
        <domain includeSubdomains="true">cdn-creatives-highwinds-prd.unityads.unity3d.com/</domain>
        <domain includeSubdomains="true">cdn-creatives-tencent-prd.unityads.unitychina.cn/</domain>
        <domain includeSubdomains="true">ccdn-store-icons-akamai-prd.unityads.unity3d.com/</domain>
        <domain includeSubdomains="true">cdn-store-icons-highwinds-prd.unityads.unity3d.com/
        </domain>
        <domain includeSubdomains="true">cdn-store-icons-tencent-prd.unityads.unitychina.cn/
        </domain>
        <domain includeSubdomains="true">
            cdn-creatives-akamaistls-prd.acquire.unity3dusercontent.com/
        </domain>
    </domain-config>


    <!--max mate-->
    <!--    <base-config cleartextTrafficPermitted="true">-->
    <!--        <trust-anchors>-->
    <!--            <certificates src="system"/>-->
    <!--        </trust-anchors>-->
    <!--    </base-config>-->


</network-security-config>
