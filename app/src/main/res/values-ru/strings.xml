<resources>
    <string name="app_name">E-Pedometer</string>

    <string name="profile_title">Profile</string>
    <string name="cd_profile_title" translatable="false">Profile</string>

    <string name="steps_title">Steps</string>
    <string name="cd_steps_title" translatable="false">Steps</string>

    <string name="reports_title">Report</string>
    <string name="cd_reports_title" translatable="false">Reports</string>

    <string name="wallet_title">Wallet</string>
    <string name="cd_wallet_title" translatable="false">Wallet</string>

    <string name="steps_today">steps</string>

    <string name="text_weight">Weight</string>
    <string name="text_seven_day_avg">7-Day average</string>
    <string name="text_day">Day</string>
    <string name="text_week">Week</string>
    <string name="text_month">Month</string>
    <string name="text_total">Total</string>
    <string name="text_avg">Avg</string>
    <string name="text_step">Step</string>
    <string name="text_distance">Distance</string>
    <string name="text_time">Time</string>
    <string name="text_calories">Calories</string>
    <string name="text_report">Report</string>
    <string name="text_version">Version</string>
    <string name="text_steps">Steps</string>
    <string name="text_next">Next</string>
    <string name="text_skip">Skip</string>
    <string name="text_my_profile">My Profile</string>
    <string name="text_cancel">Cancel</string>
    <string name="text_save">Save</string>
    <string name="text_gender">Gender</string>
    <string name="text_sensitivity">Sensitivity</string>
    <string name="text_low">Low</string>
    <string name="text_medium">Medium</string>
    <string name="text_high">High</string>
    <string name="text_step_goal">Step Goal</string>
    <string name="text_metric_and_imperial_unit">Metric &amp; Imperial Unit</string>
    <string name="text_height">Height</string>
    <string name="text_step_length">Step Length</string>
    <string name="text_feedback">Feedback</string>
    <string name="text_privacy_policy">Privacy Policy</string>
    <string name="text_male">Male</string>
    <string name="text_female">Female</string>
    <string name="text_others">Others</string>
    <string name="text_watch">Watch</string>

    <string name="text_ok">OK</string>
    <string name="text_get">Get</string>
    <string name="text_coins">Coins</string>
    <string name="text_withdraw">Withdraw</string>
    <string name="text_exchange_coins">Exchange coins</string>
    <string name="text_get_coins">Get coins</string>
    <string name="text_confirm">Confirm</string>
    <string name="text_reward_">Reward!</string>
    <string name="text_1000_steps">1000 steps</string>
    <string name="text_5000_steps">5000 steps</string>
    <string name="text_new_user_bonus">New user bonus</string>
    <string name="text_congratulations">Congratulations</string>
    <string name="text_sign_in">Sign in</string>
    <string name="text_choose_a_coupon">Redeem coupons with coins</string>
    <string name="text_choose_an_amount">Choose an amount</string>
    <string name="text_get_it">Got it!</string>
    <string name="text_missed">Missed</string>
    <string name="text_allow">Allow</string>
    <string name="text_loading_ad">Loading ad…</string>


    <string name="title_get_coins">Get Coins</string>
    <string name="title_completing_task_bonus">Completing Task Bonus!</string>
    <string name="title_today_remaining_times">"Today Remaining Times: "</string>
    <string name="title_today_remaining_cards">"Today Remaining Cards: "</string>
    <string name="title_redeem_now">Redeem Now</string>
    <string name="title_my_coins">My Coins</string>
    <string name="title_maybe_later">Maybe Later</string>
    <string name="title_exchange_coins">Exchange Coins</string>


    <string name="sensitivity_setting_tips">The higher the sensitivity, the smaller the movement will be counted as steps.</string>

    <string name="gender_setting_tips_0">Please Select Your Gender \n</string>
    <string name="gender_setting_tips_1">Calories &amp; stride length calculation needs it.</string>

    <string name="measure_unit_setting_tips">To ensure the accuracy, please input your accurate information.\nWe never share them with third parties.</string>

    <string name="splash_content_0">Track your steps, calories, distance, and workout time.</string>


    <string name="my_coins_chip_text_my_coins">"My coins: "</string>

    <string name="super_wheel_text_coins_can_be_redeemed">Coins can be redeemed for Cash</string>
    <string name="text_play">Play</string>

    <string name="no_enough_coins_title">No enough coins</string>
    <string name="no_enough_coins_tips">1. Watch more videos to earn coins\n                    |\n                    |2. Survive by completing daily tasks to get more coins</string>

    <string name="coupon_redeemed_successfully_title">Redeemed successfully</string>
    <string name="coupon_redeemed_successfully_tips">You can expect to receive the Coupon in 7 days. Hope you enjoy this Coupon!</string>

    <string name="daily_tasks_and_earn_title">Complete daily tasks and Earn</string>


    <string name="permissions_required_text_permission_required">Permission Required</string>
    <string name="permissions_required_tips">E-Pedometer needs access to additional permissions in order to provide the best experience possible.</string>
    <string name="permissions_required_text_physical_activity">Physical Activity</string>
    <string name="permissions_required_text_track_your_fitness_movements">Track your fitness movements</string>

    <string name="cash_redeemed_successfully_title">Transaction in Progress</string>

    <string name="rate_on_feedback_title">Don\'t like it?</string>
    <string name="rate_on_feedback_tips">Please tell us what you think?</string>

    <string name="rate_on_gp_text_rate_on_google_play">Rate on Google Play</string>

    <string name="cash_redeemed_successfully_content_you_are_sending">"You are sending "</string>
    <string name="cash_redeemed_successfully_content_to">" to "</string>
    <string name="cash_redeemed_successfully_content_you_can_expect_to_receive_the_money_in_7_days">"You can expect to receive the money in 7 days."</string>

    <string name="game_no_remaining_title">Oh no…</string>
    <string name="game_no_remaining_tips">The count has been used up, let\'s continue tomorrow.</string>

    <string name="rate_on_gp_title">Great to hear that!</string>
    <string name="rate_on_gp_tips">Could you write a review on Google Play</string>

    <string name="rating_title">Enjoy E-Pedometer?</string>
    <string name="rating_tips">Tap a star to rate it on Google Play</string>

    <string name="check_in_text_check_in_bonus">Check In Bonus!</string>

    <string name="new_user_rewarded_title">New User Bonus!</string>

    <string name="game_banner_tips">Play the following instant games to earn more coins</string>

    <string name="no_enough_steps_title">No enough steps</string>
    <string name="no_enough_steps_tips">1. Take more steps to exchange coins.  Every 100 steps are worth 100 coins\n                    |\n                    |2. You can also earn coins by playing instant games and completing daily tasks</string>

    <string name="redeemed_cash_text_transfer_money_to">Transfer money to:</string>
    <string name="redeemed_cash_text_enter_your_paypal_balance_here">Enter your Paypal account here</string>
    <string name="redeemed_cash_tips_please_check_info_">Please check the information before transferring money.</string>
    <string name="redeemed_cash_title_transfer_money">Transfer Money</string>
    <string name="redeemed_cash_text_please_enter_your_paypal_balance_details">Please enter your PayPal account details:</string>
    <string name="redeemed_cash_title_transfer_money_to_paypal">Transfer Money to PayPal</string>

    <string name="redeemed_coupon_text">Coupon</string>
    <string name="redeemed_coupon_text_to">To:</string>
    <string name="redeemed_coupon_text_enter_your_email_here">Enter your email here</string>
    <string name="redeemed_coupon_text_message">Message:</string>
    <string name="redeemed_coupon_text_add_a_message_optional">Add a message (optional)</string>
    <string name="redeemed_coupon_text_redeem_your_gift_card">Redeem your coupon</string>

    <string name="lucky_scratch_tips_0_0">"GET A "</string>
    <string name="lucky_scratch_tips_1">·Reward according to the number of the same pattern</string>
    <string name="lucky_scratch_tips_0_1">MAXIMUM OF 30000 COINS</string>

    <string name="please_enter_your_gift_card_details">Please enter your coupon details:</string>

    <string name="wvt_title">Watch Videos Task</string>
    <string name="wvt_up_to_coins_per_day">Win up to %d coins</string>
    <string name="wvt_guide_tips_1">Watch the video ad and click it!\n</string>
    <string name="wvt_guide_tips_0">Click here to watch video ad</string>
    <string name="wvt_step1"> · Click button to watch video ad</string>
    <string name="wvt_step2"> · Click the video ad</string>
    <string name="wvt_video_pre_day">%1$d Videos per day (%2$d/%1$d)</string>

    <string name="rewarded_ad_tips_content_1">Watch ad for 30s.</string>
    <string name="rewarded_ad_tips_content_2">Watch full ad to get coins.</string>
    <string name="rewarded_ad_tips_content_3">If you leave before the ad end, you will not be rewarded.</string>
    <string name="rewarded_ad_tips_error_content_1">Ad error… Please try again.</string>
    <string name="rewarded_ad_tips_error_btn_text">Try Again</string>

    <string name="text_notification">Notifications</string>
    <string name="notify_when_your_coin_amount_changes">Alert for coin quantity changes</string>
    <string name="noti_permission_req_title">Permission Required for Notifications</string>
    <string name="noti_permission_req_content">We need permission to keep you in the loop about your money reward and step goals. Grant us access to level up your experience.</string>
    <string name="permission_req_button_grant">GRANT NOW</string>

    <string name="guide_activity_recognition_permission">We needs access to additional permissions in order to provide the best experience possible. Track your fitness movements</string>
    <string name="your_entire_purchase">Your entire purchase</string>
    <string name="exchange_steps_to_coins_text">100 steps = 100 coins</string>

    <string name="text_additional_permissions_required">Additional Permissions Required</string>
    <string name="text_we_require_additional_permissions_tips">We require additional permissions to enhance your app experience</string>
    <string name="text_protected_app">Protected App</string>
    <string name="text_ignore_battery_optimization">Get fitness progress in real-time</string>

    <string name="text_reward">Reward</string>
    <string name="text_saved_successfully">Saved successfully</string>
    <string name="text_download">Download</string>

    <string name="text_manage_permissions">Manage Permissions</string>
    <string name="you_can_disable_it_anytime">You can disable it anytime.</string>
    <string name="allow_notification">Allow Notification</string>
    <string name="allow_protected_app">Allow Protected App</string>
    <string name="allow_physical_activity">Allow Physical Activity</string>

    <string name="text_this_action_might_contain_ad">This action might contain AD</string>

    <string name="text_unlocked_successfully">Unlocked successfully!</string>
    <string name="text_click_to_preview_and_download_">Click to preview and download, you can also share it with your friends!</string>
    <string name="text_unlock_atlas_with_coins">Unlock atlas with coins</string>
    <string name="text_add_to_gallery">Add to gallery</string>

</resources>
