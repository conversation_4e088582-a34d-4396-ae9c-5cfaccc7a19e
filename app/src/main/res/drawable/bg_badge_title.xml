<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="670dp"
    android:height="110dp"
    android:viewportWidth="670"
    android:viewportHeight="110">
  <path
      android:pathData="M646.27,103.66L526,103.66C521.58,103.66 518,100.08 518,95.66L518,45.15C518,40.74 521.58,37.15 526,37.15L646.27,37.15C650.69,37.15 654.27,40.74 654.27,45.15C654.27,47.65 653.11,50.01 651.12,51.52L626.29,70.41L626.29,70.41L651.12,89.3C654.63,91.97 655.32,96.99 652.64,100.51C651.13,102.5 648.77,103.66 646.27,103.66Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="586.66"
          android:startY="37.15"
          android:endX="586.66"
          android:endY="103.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFF75927"/>
        <item android:offset="1" android:color="#FFFC924F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M23.73,103.66L144,103.66C148.42,103.66 152,100.08 152,95.66L152,45.15C152,40.74 148.42,37.15 144,37.15L23.73,37.15C19.31,37.15 15.73,40.74 15.73,45.15C15.73,47.65 16.89,50.01 18.88,51.52L43.71,70.41L43.71,70.41L18.88,89.3C15.37,91.97 14.68,96.99 17.36,100.51C18.87,102.5 21.23,103.66 23.73,103.66Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="83.34"
          android:startY="37.15"
          android:endX="83.34"
          android:endY="103.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFF75927"/>
        <item android:offset="1" android:color="#FFFC924F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M101.04,10.42L569.04,10.42C575.67,10.42 581.04,15.79 581.04,22.42L581.04,78.42C581.04,85.05 575.67,90.42 569.04,90.42L101.04,90.42C94.42,90.42 89.04,85.05 89.04,78.42L89.04,22.42C89.04,15.79 94.42,10.42 101.04,10.42Z"
      android:strokeWidth="1"
      android:fillColor="#000000"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M101.04,10.42L569.04,10.42C575.67,10.42 581.04,15.79 581.04,22.42L581.04,78.42C581.04,85.05 575.67,90.42 569.04,90.42L101.04,90.42C94.42,90.42 89.04,85.05 89.04,78.42L89.04,22.42C89.04,15.79 94.42,10.42 101.04,10.42Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="335.04"
          android:startY="10.42"
          android:endX="335.04"
          android:endY="90.42"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
