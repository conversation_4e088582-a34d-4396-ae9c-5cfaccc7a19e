<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="220dp"
    android:viewportWidth="200"
    android:viewportHeight="220">
  <path
      android:pathData="M132.84,214.16L48.64,214.16C42.5,214.16 37.53,209.31 37.53,203.33L37.53,18C37.53,12.01 42.5,7.16 48.64,7.16L132.84,7.16C138.97,7.16 143.94,12.01 143.94,18L143.94,203.33C143.94,209.31 138.97,214.16 132.84,214.16"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M83,13.5L93.74,13.5"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#F75927"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98,13.5L98.74,13.5"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#F75927"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M132.84,5.16C136.45,5.16 139.72,6.59 142.09,8.9C144.47,11.23 145.94,14.45 145.94,18L145.94,18L145.94,203.33C145.94,206.88 144.47,210.09 142.09,212.42C139.72,214.73 136.45,216.16 132.84,216.16L132.84,216.16L48.64,216.16C45.02,216.16 41.75,214.73 39.39,212.42C37,210.09 35.53,206.88 35.53,203.33L35.53,203.33L35.53,18C35.53,14.45 37,11.23 39.39,8.9C41.75,6.59 45.02,5.16 48.64,5.16L48.64,5.16Z"
      android:strokeWidth="4"
      android:fillColor="#00000000"
      android:fillType="evenOdd">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="35.53"
          android:startY="110.66"
          android:endX="145.94"
          android:endY="110.66"
          android:type="linear">
        <item android:offset="0" android:color="#FF363A4A"/>
        <item android:offset="1" android:color="#FF1D2029"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M101.27,208.84L80.21,208.84C78.62,208.84 77.32,207.55 77.32,205.96C77.32,204.37 78.62,203.08 80.21,203.08L101.27,203.08C102.86,203.08 104.15,204.37 104.15,205.96C104.15,207.55 102.86,208.84 101.27,208.84"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="90.74"
          android:startY="203.08"
          android:endX="90.74"
          android:endY="208.69"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF844D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M154.82,77.49L26.65,77.49C19.52,77.49 13.74,72.44 13.74,66.22L13.74,38.54C13.74,32.32 19.52,27.27 26.65,27.27L154.82,27.27C161.95,27.27 167.74,32.32 167.74,38.54L167.74,66.22C167.74,72.44 161.95,77.49 154.82,77.49"
      android:strokeWidth="1"
      android:fillColor="#FFF1E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M73.55,46.86L74.85,49.5C75.01,49.81 75.3,50.03 75.65,50.08L78.55,50.5C79.42,50.63 79.77,51.69 79.14,52.31L77.04,54.36C76.79,54.6 76.68,54.95 76.73,55.3L77.23,58.19C77.38,59.06 76.47,59.72 75.69,59.31L73.09,57.94C72.78,57.78 72.42,57.78 72.11,57.94L69.51,59.31C68.73,59.72 67.82,59.06 67.97,58.19L68.47,55.3C68.53,54.95 68.41,54.6 68.16,54.36L66.06,52.31C65.43,51.7 65.78,50.63 66.65,50.5L69.55,50.08C69.9,50.03 70.19,49.81 70.35,49.5L71.65,46.86C72.04,46.08 73.16,46.08 73.55,46.86"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M91.81,46.86L93.11,49.5C93.27,49.81 93.57,50.03 93.91,50.08L96.82,50.5C97.69,50.63 98.03,51.69 97.4,52.31L95.3,54.36C95.05,54.6 94.94,54.95 95,55.3L95.49,58.19C95.64,59.06 94.73,59.72 93.96,59.31L91.36,57.94C91.05,57.78 90.68,57.78 90.37,57.94L87.77,59.31C86.99,59.72 86.08,59.06 86.23,58.19L86.73,55.3C86.79,54.95 86.67,54.6 86.42,54.36L84.32,52.31C83.69,51.7 84.04,50.63 84.91,50.5L87.81,50.08C88.16,50.03 88.46,49.81 88.61,49.5L89.91,46.86C90.3,46.08 91.42,46.08 91.81,46.86"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M110.81,46.86L112.11,49.5C112.27,49.81 112.57,50.03 112.91,50.08L115.82,50.5C116.69,50.63 117.03,51.69 116.4,52.31L114.3,54.36C114.05,54.6 113.94,54.95 114,55.3L114.49,58.19C114.64,59.06 113.73,59.72 112.96,59.31L110.36,57.94C110.05,57.78 109.68,57.78 109.37,57.94L106.77,59.31C105.99,59.72 105.08,59.06 105.23,58.19L105.73,55.3C105.79,54.95 105.67,54.6 105.42,54.36L103.32,52.31C102.69,51.7 103.04,50.63 103.91,50.5L106.81,50.08C107.16,50.03 107.46,49.81 107.61,49.5L108.91,46.86C109.3,46.08 110.42,46.08 110.81,46.86"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M128.81,46.86L130.11,49.5C130.27,49.81 130.57,50.03 130.91,50.08L133.82,50.5C134.69,50.63 135.03,51.69 134.4,52.31L132.3,54.36C132.05,54.6 131.94,54.95 132,55.3L132.49,58.19C132.64,59.06 131.73,59.72 130.96,59.31L128.36,57.94C128.05,57.78 127.68,57.78 127.37,57.94L124.77,59.31C123.99,59.72 123.08,59.06 123.23,58.19L123.73,55.3C123.79,54.95 123.67,54.6 123.42,54.36L121.32,52.31C120.69,51.7 121.04,50.63 121.91,50.5L124.81,50.08C125.16,50.03 125.46,49.81 125.61,49.5L126.91,46.86C127.3,46.08 128.42,46.08 128.81,46.86"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M147.81,46.86L149.11,49.5C149.27,49.81 149.57,50.03 149.91,50.08L152.82,50.5C153.69,50.63 154.03,51.69 153.4,52.31L151.3,54.36C151.05,54.6 150.94,54.95 151,55.3L151.49,58.19C151.64,59.06 150.73,59.72 149.96,59.31L147.36,57.94C147.05,57.78 146.68,57.78 146.37,57.94L143.77,59.31C142.99,59.72 142.08,59.06 142.23,58.19L142.73,55.3C142.79,54.95 142.67,54.6 142.42,54.36L140.32,52.31C139.69,51.7 140.04,50.63 140.91,50.5L143.81,50.08C144.16,50.03 144.46,49.81 144.61,49.5L145.91,46.86C146.3,46.08 147.42,46.08 147.81,46.86"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M56.01,51.91C56.01,58.89 50.35,64.55 43.37,64.55C36.4,64.55 30.74,58.89 30.74,51.91C30.74,44.93 36.4,39.27 43.37,39.27C50.35,39.27 56.01,44.93 56.01,51.91"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="43.37"
          android:startY="39.27"
          android:endX="43.37"
          android:endY="63.9"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF844D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M129.02,118.54L52.45,118.54C48.19,118.54 44.74,115.53 44.74,111.81L44.74,95.27C44.74,91.55 48.19,88.54 52.45,88.54L129.02,88.54C133.28,88.54 136.74,91.55 136.74,95.27L136.74,111.81C136.74,115.53 133.28,118.54 129.02,118.54"
      android:strokeWidth="1"
      android:fillColor="#FFF1E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M80.29,99.9L81.05,101.5C81.14,101.69 81.31,101.82 81.51,101.85L83.21,102.11C83.71,102.19 83.92,102.83 83.55,103.21L82.32,104.45C82.18,104.6 82.11,104.82 82.15,105.02L82.44,106.78C82.52,107.31 81.99,107.71 81.54,107.46L80.02,106.63C79.84,106.53 79.63,106.53 79.45,106.63L77.93,107.46C77.48,107.71 76.95,107.31 77.04,106.78L77.33,105.02C77.36,104.82 77.3,104.6 77.15,104.45L75.92,103.21C75.56,102.84 75.76,102.19 76.27,102.11L77.96,101.85C78.16,101.82 78.33,101.69 78.43,101.5L79.18,99.9C79.41,99.42 80.06,99.42 80.29,99.9"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M90.29,99.9L91.05,101.5C91.14,101.69 91.31,101.82 91.51,101.85L93.21,102.11C93.71,102.19 93.92,102.83 93.55,103.21L92.32,104.45C92.18,104.6 92.11,104.82 92.15,105.02L92.44,106.78C92.52,107.31 91.99,107.71 91.54,107.46L90.02,106.63C89.84,106.53 89.63,106.53 89.45,106.63L87.93,107.46C87.48,107.71 86.95,107.31 87.04,106.78L87.33,105.02C87.36,104.82 87.3,104.6 87.15,104.45L85.92,103.21C85.56,102.84 85.76,102.19 86.27,102.11L87.96,101.85C88.16,101.82 88.34,101.69 88.42,101.5L89.18,99.9C89.41,99.42 90.06,99.42 90.29,99.9"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M100.29,99.9L101.05,101.5C101.14,101.69 101.31,101.82 101.51,101.85L103.21,102.11C103.71,102.19 103.92,102.83 103.55,103.21L102.32,104.45C102.18,104.6 102.11,104.82 102.15,105.02L102.44,106.78C102.52,107.31 101.99,107.71 101.54,107.46L100.02,106.63C99.84,106.53 99.63,106.53 99.45,106.63L97.93,107.46C97.48,107.71 96.95,107.31 97.04,106.78L97.33,105.02C97.36,104.82 97.3,104.6 97.15,104.45L95.92,103.21C95.56,102.84 95.76,102.19 96.27,102.11L97.96,101.85C98.16,101.82 98.33,101.69 98.43,101.5L99.18,99.9C99.41,99.42 100.06,99.42 100.29,99.9"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M111.29,99.9L112.05,101.5C112.14,101.69 112.31,101.82 112.51,101.85L114.21,102.11C114.71,102.19 114.92,102.83 114.55,103.21L113.32,104.45C113.18,104.6 113.11,104.82 113.15,105.02L113.44,106.78C113.52,107.31 112.99,107.71 112.54,107.46L111.02,106.63C110.84,106.53 110.63,106.53 110.45,106.63L108.93,107.46C108.48,107.71 107.95,107.31 108.04,106.78L108.33,105.02C108.36,104.82 108.3,104.6 108.15,104.45L106.92,103.21C106.56,102.84 106.76,102.19 107.27,102.11L108.96,101.85C109.16,101.82 109.33,101.69 109.42,101.5L110.18,99.9C110.41,99.42 111.06,99.42 111.29,99.9"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M121.29,99.9L122.05,101.5C122.14,101.69 122.31,101.82 122.51,101.85L124.21,102.11C124.71,102.19 124.92,102.83 124.55,103.21L123.32,104.45C123.18,104.6 123.11,104.82 123.15,105.02L123.44,106.78C123.52,107.31 122.99,107.71 122.54,107.46L121.02,106.63C120.84,106.53 120.63,106.53 120.45,106.63L118.93,107.46C118.48,107.71 117.95,107.31 118.04,106.78L118.33,105.02C118.36,104.82 118.3,104.6 118.15,104.45L116.92,103.21C116.56,102.84 116.76,102.19 117.27,102.11L118.96,101.85C119.16,101.82 119.34,101.69 119.42,101.5L120.18,99.9C120.41,99.42 121.06,99.42 121.29,99.9"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M70.74,104.04C70.74,108.18 67.38,111.54 63.24,111.54C59.1,111.54 55.74,108.18 55.74,104.04C55.74,99.9 59.1,96.54 63.24,96.54C67.38,96.54 70.74,99.9 70.74,104.04"
      android:strokeWidth="1"
      android:fillColor="#F68B00"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M129.02,156.54L52.45,156.54C48.19,156.54 44.74,153.53 44.74,149.81L44.74,133.27C44.74,129.55 48.19,126.54 52.45,126.54L129.02,126.54C133.28,126.54 136.74,129.55 136.74,133.27L136.74,149.81C136.74,153.53 133.28,156.54 129.02,156.54"
      android:strokeAlpha="0.50248426"
      android:strokeWidth="1"
      android:fillColor="#FFF1E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.50248426"/>
  <path
      android:pathData="M80.29,137.9L81.05,139.5C81.14,139.69 81.31,139.82 81.51,139.85L83.21,140.11C83.71,140.19 83.92,140.83 83.55,141.21L82.32,142.45C82.18,142.6 82.11,142.82 82.15,143.02L82.44,144.78C82.52,145.31 81.99,145.71 81.54,145.46L80.02,144.63C79.84,144.53 79.63,144.53 79.45,144.63L77.93,145.46C77.48,145.71 76.95,145.31 77.04,144.78L77.33,143.02C77.36,142.82 77.3,142.6 77.15,142.45L75.92,141.21C75.56,140.84 75.76,140.19 76.27,140.11L77.96,139.85C78.16,139.82 78.33,139.69 78.43,139.5L79.18,137.9C79.41,137.42 80.06,137.42 80.29,137.9"
      android:strokeAlpha="0.50248426"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.50248426"/>
  <path
      android:pathData="M90.29,137.9L91.05,139.5C91.14,139.69 91.31,139.82 91.51,139.85L93.21,140.11C93.71,140.19 93.92,140.83 93.55,141.21L92.32,142.45C92.18,142.6 92.11,142.82 92.15,143.02L92.44,144.78C92.52,145.31 91.99,145.71 91.54,145.46L90.02,144.63C89.84,144.53 89.63,144.53 89.45,144.63L87.93,145.46C87.48,145.71 86.95,145.31 87.04,144.78L87.33,143.02C87.36,142.82 87.3,142.6 87.15,142.45L85.92,141.21C85.56,140.84 85.76,140.19 86.27,140.11L87.96,139.85C88.16,139.82 88.34,139.69 88.42,139.5L89.18,137.9C89.41,137.42 90.06,137.42 90.29,137.9"
      android:strokeAlpha="0.50248426"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.50248426"/>
  <path
      android:pathData="M100.29,137.9L101.05,139.5C101.14,139.69 101.31,139.82 101.51,139.85L103.21,140.11C103.71,140.19 103.92,140.83 103.55,141.21L102.32,142.45C102.18,142.6 102.11,142.82 102.15,143.02L102.44,144.78C102.52,145.31 101.99,145.71 101.54,145.46L100.02,144.63C99.84,144.53 99.63,144.53 99.45,144.63L97.93,145.46C97.48,145.71 96.95,145.31 97.04,144.78L97.33,143.02C97.36,142.82 97.3,142.6 97.15,142.45L95.92,141.21C95.56,140.84 95.76,140.19 96.27,140.11L97.96,139.85C98.16,139.82 98.33,139.69 98.43,139.5L99.18,137.9C99.41,137.42 100.06,137.42 100.29,137.9"
      android:strokeAlpha="0.50248426"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.50248426"/>
  <path
      android:pathData="M111.29,137.9L112.05,139.5C112.14,139.69 112.31,139.82 112.51,139.85L114.21,140.11C114.71,140.19 114.92,140.83 114.55,141.21L113.32,142.45C113.18,142.6 113.11,142.82 113.15,143.02L113.44,144.78C113.52,145.31 112.99,145.71 112.54,145.46L111.02,144.63C110.84,144.53 110.63,144.53 110.45,144.63L108.93,145.46C108.48,145.71 107.95,145.31 108.04,144.78L108.33,143.02C108.36,142.82 108.3,142.6 108.15,142.45L106.92,141.21C106.56,140.84 106.76,140.19 107.27,140.11L108.96,139.85C109.16,139.82 109.33,139.69 109.42,139.5L110.18,137.9C110.41,137.42 111.06,137.42 111.29,137.9"
      android:strokeAlpha="0.50248426"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.50248426"/>
  <path
      android:pathData="M121.29,137.9L122.05,139.5C122.14,139.69 122.31,139.82 122.51,139.85L124.21,140.11C124.71,140.19 124.92,140.83 124.55,141.21L123.32,142.45C123.18,142.6 123.11,142.82 123.15,143.02L123.44,144.78C123.52,145.31 122.99,145.71 122.54,145.46L121.02,144.63C120.84,144.53 120.63,144.53 120.45,144.63L118.93,145.46C118.48,145.71 117.95,145.31 118.04,144.78L118.33,143.02C118.36,142.82 118.3,142.6 118.15,142.45L116.92,141.21C116.56,140.84 116.76,140.19 117.27,140.11L118.96,139.85C119.16,139.82 119.34,139.69 119.42,139.5L120.18,137.9C120.41,137.42 121.06,137.42 121.29,137.9"
      android:strokeAlpha="0.50248426"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.50248426"/>
  <path
      android:pathData="M70.74,142.04C70.74,146.18 67.38,149.54 63.24,149.54C59.1,149.54 55.74,146.18 55.74,142.04C55.74,137.9 59.1,134.54 63.24,134.54C67.38,134.54 70.74,137.9 70.74,142.04"
      android:strokeAlpha="0.50248426"
      android:strokeWidth="1"
      android:fillColor="#F68B00"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.50248426"/>
  <path
      android:pathData="M129.02,194.54L52.45,194.54C48.19,194.54 44.74,191.53 44.74,187.81L44.74,171.27C44.74,167.55 48.19,164.54 52.45,164.54L129.02,164.54C133.28,164.54 136.74,167.55 136.74,171.27L136.74,187.81C136.74,191.53 133.28,194.54 129.02,194.54"
      android:strokeAlpha="0.20435442"
      android:strokeWidth="1"
      android:fillColor="#FFF1E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20435442"/>
  <path
      android:pathData="M80.29,175.9L81.05,177.5C81.14,177.69 81.31,177.82 81.51,177.85L83.21,178.11C83.71,178.19 83.92,178.83 83.55,179.21L82.32,180.45C82.18,180.6 82.11,180.82 82.15,181.02L82.44,182.78C82.52,183.31 81.99,183.71 81.54,183.46L80.02,182.63C79.84,182.53 79.63,182.53 79.45,182.63L77.93,183.46C77.48,183.71 76.95,183.31 77.04,182.78L77.33,181.02C77.36,180.82 77.3,180.6 77.15,180.45L75.92,179.21C75.56,178.84 75.76,178.19 76.27,178.11L77.96,177.85C78.16,177.82 78.33,177.69 78.43,177.5L79.18,175.9C79.41,175.42 80.06,175.42 80.29,175.9"
      android:strokeAlpha="0.20435442"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20435442"/>
  <path
      android:pathData="M90.29,175.9L91.05,177.5C91.14,177.69 91.31,177.82 91.51,177.85L93.21,178.11C93.71,178.19 93.92,178.83 93.55,179.21L92.32,180.45C92.18,180.6 92.11,180.82 92.15,181.02L92.44,182.78C92.52,183.31 91.99,183.71 91.54,183.46L90.02,182.63C89.84,182.53 89.63,182.53 89.45,182.63L87.93,183.46C87.48,183.71 86.95,183.31 87.04,182.78L87.33,181.02C87.36,180.82 87.3,180.6 87.15,180.45L85.92,179.21C85.56,178.84 85.76,178.19 86.27,178.11L87.96,177.85C88.16,177.82 88.34,177.69 88.42,177.5L89.18,175.9C89.41,175.42 90.06,175.42 90.29,175.9"
      android:strokeAlpha="0.20435442"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20435442"/>
  <path
      android:pathData="M100.29,175.9L101.05,177.5C101.14,177.69 101.31,177.82 101.51,177.85L103.21,178.11C103.71,178.19 103.92,178.83 103.55,179.21L102.32,180.45C102.18,180.6 102.11,180.82 102.15,181.02L102.44,182.78C102.52,183.31 101.99,183.71 101.54,183.46L100.02,182.63C99.84,182.53 99.63,182.53 99.45,182.63L97.93,183.46C97.48,183.71 96.95,183.31 97.04,182.78L97.33,181.02C97.36,180.82 97.3,180.6 97.15,180.45L95.92,179.21C95.56,178.84 95.76,178.19 96.27,178.11L97.96,177.85C98.16,177.82 98.33,177.69 98.43,177.5L99.18,175.9C99.41,175.42 100.06,175.42 100.29,175.9"
      android:strokeAlpha="0.20435442"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20435442"/>
  <path
      android:pathData="M111.29,175.9L112.05,177.5C112.14,177.69 112.31,177.82 112.51,177.85L114.21,178.11C114.71,178.19 114.92,178.83 114.55,179.21L113.32,180.45C113.18,180.6 113.11,180.82 113.15,181.02L113.44,182.78C113.52,183.31 112.99,183.71 112.54,183.46L111.02,182.63C110.84,182.53 110.63,182.53 110.45,182.63L108.93,183.46C108.48,183.71 107.95,183.31 108.04,182.78L108.33,181.02C108.36,180.82 108.3,180.6 108.15,180.45L106.92,179.21C106.56,178.84 106.76,178.19 107.27,178.11L108.96,177.85C109.16,177.82 109.33,177.69 109.42,177.5L110.18,175.9C110.41,175.42 111.06,175.42 111.29,175.9"
      android:strokeAlpha="0.20435442"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20435442"/>
  <path
      android:pathData="M121.29,175.9L122.05,177.5C122.14,177.69 122.31,177.82 122.51,177.85L124.21,178.11C124.71,178.19 124.92,178.83 124.55,179.21L123.32,180.45C123.18,180.6 123.11,180.82 123.15,181.02L123.44,182.78C123.52,183.31 122.99,183.71 122.54,183.46L121.02,182.63C120.84,182.53 120.63,182.53 120.45,182.63L118.93,183.46C118.48,183.71 117.95,183.31 118.04,182.78L118.33,181.02C118.36,180.82 118.3,180.6 118.15,180.45L116.92,179.21C116.56,178.84 116.76,178.19 117.27,178.11L118.96,177.85C119.16,177.82 119.34,177.69 119.42,177.5L120.18,175.9C120.41,175.42 121.06,175.42 121.29,175.9"
      android:strokeAlpha="0.20435442"
      android:strokeWidth="1"
      android:fillColor="#FDAD23"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20435442"/>
  <path
      android:pathData="M70.74,180.04C70.74,184.18 67.38,187.54 63.24,187.54C59.1,187.54 55.74,184.18 55.74,180.04C55.74,175.9 59.1,172.54 63.24,172.54C67.38,172.54 70.74,175.9 70.74,180.04"
      android:strokeAlpha="0.20435442"
      android:strokeWidth="1"
      android:fillColor="#F68B00"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.20435442"/>
  <path
      android:pathData="M28.69,156.81C28.69,156.81 31.79,152.88 37.68,154.31L45.99,171.72C45.99,171.72 61.73,174.31 62.75,176.91C63.77,179.51 61.47,182.76 58.91,185.74C56.35,188.73 50.34,196.13 50.34,196.13L49.96,215.75C49.96,215.75 48.55,219.64 43.31,217.18C38.06,214.71 30.51,210.42 30.51,210.42L14.65,217.99C14.65,217.99 9.02,218.39 8,212.39"
      android:strokeWidth="1"
      android:fillColor="#F68B00"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M32.01,155.74C32.01,155.74 30.19,155.35 28.87,156.81C27.59,158.21 25.72,160.36 23.6,164.26L19.46,171.75L5.1,174.97C5.1,174.97 3.16,175.36 2.25,177.43C1.35,179.49 3.16,182.07 3.16,182.07L10.27,192.27C10.27,192.27 10.92,195.89 9.76,199.89C8.59,203.89 6.26,213.18 9.24,213.57C12.21,213.96 14.28,212.41 14.28,212.41C14.28,212.41 24.37,206.99 25.54,206.99C26.7,206.99 38.47,213.44 39.51,213.63C40.54,213.83 41.45,213.57 42.61,212.67C43.78,211.76 43.78,209.96 43.78,209.96L43.78,194.21L54.77,181.3C54.77,181.3 56.58,179.1 55.81,176.91C55.03,174.72 52.31,174.46 52.31,174.46L39.9,171.75L34.46,157.68C34.46,157.68 34.07,156.13 32.01,155.74"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="28.97"
          android:startY="155.35"
          android:endX="28.97"
          android:endY="212.46"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFAD4D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M176.37,97.01C177.09,97.07 177.93,97.18 178.74,97.4C180.48,97.87 181.39,99.61 181.5,101.43C181.64,103.72 181.56,106.05 181.44,107.77C184.15,107.53 186.83,107.33 189.25,107.22C191.8,107.09 193.93,109.04 193.99,111.62C194.07,115.88 193.8,122.35 191.87,127.6C191.11,129.66 189.1,130.88 186.93,130.95C176.77,131.28 167,129.92 167,129.92L167,109.63C168.13,109.42 169.45,109.2 170.89,108.99C172.17,105.36 173.38,101.11 174.03,98.76C174.33,97.68 175.27,96.91 176.37,97.01Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="180.54"
          android:startY="96.91"
          android:endX="180.54"
          android:endY="130.4"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF844D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M161.11,129.69C161.14,130.59 161.61,131.41 162.43,131.65C163.07,131.83 163.94,132 165,132C166.06,132 166.93,131.83 167.57,131.65C168.39,131.41 168.86,130.59 168.89,129.69C168.96,126.46 169,123.23 169,120C169,115.83 168.93,112.23 168.89,110.31C168.86,109.41 168.39,108.59 167.57,108.35C166.93,108.17 166.06,108 165,108C163.94,108 163.07,108.17 162.43,108.35C161.61,108.59 161.14,109.41 161.11,110.31C161.04,113.54 161,116.77 161,120C161,124.17 161.07,127.77 161.11,129.69L161.11,129.69Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="161"
          android:startY="120"
          android:endX="169"
          android:endY="120"
          android:type="linear">
        <item android:offset="0" android:color="#FF363A4A"/>
        <item android:offset="1" android:color="#FF1D2029"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
