<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="130dp"
    android:height="130dp"
    android:viewportWidth="130"
    android:viewportHeight="130">
  <path
      android:pathData="M65.97,10.62C35.6,10.62 10.97,35.25 10.97,65.62L10.97,65.62C10.97,96 35.6,120.62 65.97,120.62L65.97,120.62C96.35,120.62 120.97,96 120.97,65.62L120.97,65.62C120.97,35.25 96.35,10.62 65.97,10.62Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="65.97"
          android:startY="10.62"
          android:endX="65.97"
          android:endY="120.62"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFDB5F"/>
        <item android:offset="1" android:color="#FFFDB436"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M35,71.02C35,71.02 62.86,66.47 96,71.02C96,71.02 97.05,98 65.5,98C33.95,98 35,71.02 35,71.02"
      android:strokeWidth="1"
      android:fillColor="#6A0100"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M35,71.02C35,71.02 62.85,66.46 95.99,71.02C95.99,71.02 97.05,98 65.5,98C33.95,98 35,71.02 35,71.02"
      android:strokeWidth="1"
      android:fillColor="#6A0100"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M35,71.02C35,71.02 62.85,66.46 95.99,71.02C95.99,71.02 97.05,98 65.5,98C33.95,98 35,71.02 35,71.02"/>
    <path
        android:pathData="M91.68,98.41C91.68,105.75 80,111.71 65.6,111.71C51.2,111.71 39.52,105.75 39.52,98.41C39.52,91.06 51.2,85.1 65.6,85.1C80,85.1 91.68,91.06 91.68,98.41"
        android:strokeWidth="1"
        android:fillColor="#A6090A"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M47.25,32.28C44.26,33.06 42.06,35.72 41.66,38.56L41.66,38.56C39.96,36.26 36.77,34.99 33.77,35.77L33.77,35.77C30.02,36.73 27.76,40.38 28.75,44.31L28.75,44.31C30.1,49.6 36.87,52.36 46.65,58.2L46.65,58.2C52.45,48.33 57.07,42.63 55.73,37.34L55.73,37.34C54.89,34.05 52.14,32.05 49.08,32.05L49.08,32.05C48.48,32.05 47.87,32.12 47.25,32.28"/>
    <path
        android:pathData="M47.25,32.28C44.26,33.06 42.06,35.72 41.66,38.56L41.66,38.56C39.96,36.26 36.77,34.99 33.77,35.77L33.77,35.77C30.02,36.73 27.76,40.38 28.75,44.31L28.75,44.31C30.1,49.6 36.87,52.36 46.65,58.2L46.65,58.2C52.45,48.33 57.07,42.63 55.73,37.34L55.73,37.34C54.89,34.05 52.14,32.05 49.08,32.05L49.08,32.05C48.48,32.05 47.87,32.12 47.25,32.28"
        android:strokeWidth="1"
        android:fillColor="#EF3E58"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M75.78,37.34C74.44,42.63 79.06,48.33 84.85,58.2L84.85,58.2C94.64,52.36 101.41,49.6 102.75,44.31L102.75,44.31C103.75,40.38 101.48,36.73 97.73,35.77L97.73,35.77C94.74,34.99 91.54,36.26 89.84,38.56L89.84,38.56C89.45,35.72 87.25,33.06 84.25,32.28L84.25,32.28C83.64,32.12 83.03,32.05 82.43,32.05L82.43,32.05C79.36,32.05 76.62,34.05 75.78,37.34"/>
    <path
        android:pathData="M75.78,37.34C74.44,42.63 79.06,48.33 84.85,58.2L84.85,58.2C94.64,52.36 101.41,49.6 102.75,44.31L102.75,44.31C103.75,40.38 101.48,36.73 97.73,35.77L97.73,35.77C94.74,34.99 91.54,36.26 89.84,38.56L89.84,38.56C89.45,35.72 87.25,33.06 84.25,32.28L84.25,32.28C83.64,32.12 83.03,32.05 82.43,32.05L82.43,32.05C79.36,32.05 76.62,34.05 75.78,37.34"
        android:strokeWidth="1"
        android:fillColor="#EF3E58"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M86,64.95a13,4 0,1 0,26 0a13,4 0,1 0,-26 0z"
      android:strokeAlpha="0.5005195"
      android:strokeWidth="1"
      android:fillColor="#FF5D75"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.5005195"/>
  <path
      android:pathData="M17,64.95a13,4 0,1 0,26 0a13,4 0,1 0,-26 0z"
      android:strokeAlpha="0.5005195"
      android:strokeWidth="1"
      android:fillColor="#FF5D75"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.5005195"/>
</vector>
