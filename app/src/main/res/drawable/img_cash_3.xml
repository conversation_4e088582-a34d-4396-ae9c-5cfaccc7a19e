<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="150dp"
    android:height="150dp"
    android:viewportWidth="150"
    android:viewportHeight="150">
  <path
      android:pathData="M132.39,55.89l-3.66,-6.46l-15.25,18.25l4.9,3.01z"
      android:strokeWidth="1"
      android:fillColor="#081C31"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M21.52,103.39l2.43,-1.25l18.66,12.37z"/>
    <path
        android:pathData="M21.52,103.39l2.43,-1.25l18.66,12.37z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="21.52"
            android:startY="108.32"
            android:endX="42.61"
            android:endY="108.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF11601D"/>
          <item android:offset="1" android:color="#FF144421"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M21.02,100.08l2.44,-1.25l18.66,12.37z"/>
    <path
        android:pathData="M21.02,100.08l2.44,-1.25l18.66,12.37z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="21.02"
            android:startY="105.02"
            android:endX="42.12"
            android:endY="105.02"
            android:type="linear">
          <item android:offset="0" android:color="#FF11601D"/>
          <item android:offset="1" android:color="#FF144421"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M113.49,70.71C119.34,64.58 125.01,58.29 129.79,51.28L129.79,51.28L126.72,50.14L113.49,70.71Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M121.82,66.82C125.51,63.05 129.13,59.26 131.99,55.17L131.99,55.17L128.92,54.03L121.82,66.82Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M20.44,96.22L20.84,98.91L39.09,109.17L21.02,100.08L21.38,102.45L29.98,107.4L21.52,103.39L21.69,104.5L60.25,125.18C86.69,105.9 110.61,82.59 132.39,55.89L132.39,55.89L131.99,55.17C129.22,58.43 126.3,61.53 123.34,64.6L123.34,64.6C126.07,61.18 128.77,57.69 131.33,54.01L131.33,54.01L129.79,51.28C125.26,57.07 120.37,62.57 115.18,67.82L115.18,67.82C120.04,62.14 124.71,56.26 129.15,50.15L129.15,50.15L128.74,49.43L118.95,45.8L20.44,96.22Z"/>
    <path
        android:pathData="M20.44,96.22L20.84,98.91L39.09,109.16L21.02,100.08L21.38,102.45L29.98,107.4L21.52,103.38L21.69,104.49L60.25,125.18C86.69,105.9 110.61,82.59 132.39,55.89L132.39,55.89L131.99,55.17C129.22,58.43 126.3,61.53 123.34,64.59L123.34,64.59C126.07,61.17 128.77,57.69 131.33,54.01L131.33,54.01L129.79,51.28C125.26,57.07 120.37,62.57 115.18,67.82L115.18,67.82C120.04,62.13 124.71,56.25 129.15,50.15L129.15,50.15L128.74,49.42L118.95,45.79L20.44,96.22Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="20.44"
            android:startY="85.48"
            android:endX="132.39"
            android:endY="85.48"
            android:type="linear">
          <item android:offset="0" android:color="#FF24752B"/>
          <item android:offset="1" android:color="#FF1C5B24"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M58.76,116.43L60.25,125.18C86.69,105.9 110.6,82.59 132.39,55.89L132.39,55.89L131.99,55.17C129.22,58.43 126.3,61.53 123.34,64.59L123.34,64.59C126.07,61.18 128.77,57.69 131.33,54.01L131.33,54.01L129.79,51.28C125.26,57.07 120.37,62.57 115.18,67.82L115.18,67.82C120.04,62.14 124.71,56.26 129.15,50.15L129.15,50.15L128.74,49.43C108.6,75.62 84.82,97.4 58.76,116.43"/>
    <path
        android:pathData="M58.76,116.43L60.25,125.18C86.69,105.9 110.6,82.59 132.39,55.89L132.39,55.89L131.99,55.17C129.22,58.43 126.3,61.53 123.34,64.59L123.34,64.59C126.07,61.17 128.77,57.69 131.33,54.01L131.33,54.01L129.79,51.28C125.26,57.07 120.37,62.57 115.18,67.82L115.18,67.82C120.04,62.13 124.71,56.25 129.15,50.15L129.15,50.15L128.74,49.42C108.6,75.62 84.82,97.4 58.76,116.43"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="58.76"
            android:startY="87.3"
            android:endX="132.39"
            android:endY="87.3"
            android:type="linear">
          <item android:offset="0" android:color="#FF26792D"/>
          <item android:offset="1" android:color="#FF216E26"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M20.84,98.91l18.24,10.26l-18.07,-9.09l0.36,2.37l8.6,4.95l-8.46,-4.02l0.17,1.11l38.56,20.68l-1.49,-8.75l-38.33,-20.21z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M18.41,94.31L57.26,114.87C86.41,97.17 110.02,73.87 128.99,45.85L128.99,45.85L122.71,43.66L18.41,94.31Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M87.76,25C68.41,58.29 45.66,81.48 18,90.12L18,90.12C30.99,96.91 43.98,103.7 56.96,110.5L56.96,110.5C85.79,99.8 109.18,75.6 126.94,43.47L126.94,43.47L87.76,25Z"/>
    <path
        android:pathData="M87.76,25C68.41,58.29 45.65,81.48 18,90.12L18,90.12C30.99,96.91 43.97,103.7 56.96,110.49L56.96,110.49C85.79,99.79 109.18,75.6 126.94,43.47L126.94,43.47L87.76,25Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="18"
            android:startY="67.75"
            android:endX="126.94"
            android:endY="67.75"
            android:type="linear">
          <item android:offset="0" android:color="#FF2F9531"/>
          <item android:offset="1" android:color="#FF50B850"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M86.24,34.47C79.8,44.46 73.05,54.5 64.67,63.45L64.67,63.45C56.07,72.63 45.34,80.37 32.99,87.44L32.99,87.44C35.65,88.81 34.79,91.27 30.9,92.81L30.9,92.81C34.66,94.76 38.42,96.72 42.17,98.67L42.17,98.67C45.92,100.62 49.69,102.59 53.43,104.55L53.43,104.55C57.18,102.89 62.31,102.61 64.96,103.99L64.96,103.99C85.08,94.17 105.22,73.05 119.07,50.12L119.07,50.12C116.34,48.82 115.48,45.55 117.03,42.8L117.03,42.8C113.21,40.99 109.38,39.19 105.55,37.37L105.55,37.37C101.74,35.57 97.9,33.74 94.08,31.93L94.08,31.93C92.53,34.64 88.96,35.77 86.24,34.47M81.75,67.28C80.91,66.87 80.08,66.45 79.23,66.04L79.23,66.04C78.83,65.84 78.78,65.42 79.09,65.05L79.09,65.05C82.28,61.28 85.35,57.48 88.22,53.55L88.22,53.55C91.07,49.65 93.69,45.61 96.25,41.56L96.25,41.56C96.5,41.17 97.02,41 97.42,41.19L97.42,41.19C98.27,41.59 99.1,42.01 99.95,42.41L99.95,42.41C100.36,42.6 100.48,43.08 100.24,43.47L100.24,43.47C97.67,47.52 95.05,51.57 92.19,55.49L92.19,55.49C89.32,59.42 86.23,63.23 83.04,67L83.04,67C82.83,67.24 82.49,67.37 82.18,67.37L82.18,67.37C82.03,67.37 81.88,67.34 81.75,67.28M88.51,70.63C87.94,70.36 87.37,70.08 86.79,69.79L86.79,69.79C86.54,69.66 86.5,69.36 86.71,69.11L86.71,69.11C91.04,63.98 95.1,58.71 98.94,53.34L98.94,53.34C99.12,53.08 99.49,52.98 99.76,53.1L99.76,53.1C100.33,53.38 100.91,53.66 101.49,53.93L101.49,53.93C101.76,54.06 101.82,54.39 101.64,54.64L101.64,54.64C97.8,60.02 93.72,65.3 89.39,70.43L89.39,70.43C89.24,70.6 89.02,70.69 88.8,70.69L88.8,70.69C88.7,70.69 88.6,70.67 88.51,70.63M55.2,92.37C51.81,90.63 50.3,88.17 50.41,85.53L50.41,85.53C50.52,82.88 52.21,80.02 55.35,77.5L55.35,77.5C58.5,74.99 62.45,73.29 66.56,72.66L66.56,72.66C70.63,72.03 74.83,72.44 78.26,74.15L78.26,74.15C81.69,75.86 83.72,78.55 84.09,81.44L84.09,81.44C84.46,84.33 83.16,87.45 79.99,89.99L79.99,89.99C76.81,92.54 72.28,94.03 67.77,94.42L67.77,94.42C66.94,94.5 66.11,94.53 65.28,94.53L65.28,94.53C61.55,94.53 57.98,93.8 55.2,92.37"/>
    <path
        android:pathData="M86.24,34.46C79.8,44.46 73.05,54.5 64.67,63.45L64.67,63.45C56.07,72.62 45.34,80.37 32.99,87.44L32.99,87.44C35.65,88.81 34.79,91.27 30.9,92.81L30.9,92.81C34.66,94.76 38.42,96.72 42.17,98.67L42.17,98.67C45.92,100.62 49.69,102.59 53.43,104.54L53.43,104.54C57.18,102.89 62.31,102.61 64.96,103.99L64.96,103.99C85.08,94.17 105.22,73.05 119.07,50.12L119.07,50.12C116.34,48.82 115.48,45.54 117.03,42.8L117.03,42.8C113.21,40.99 109.38,39.18 105.55,37.37L105.55,37.37C101.74,35.57 97.9,33.74 94.08,31.92L94.08,31.92C92.53,34.63 88.96,35.76 86.24,34.46M81.75,67.28C80.91,66.86 80.08,66.45 79.23,66.04L79.23,66.04C78.83,65.84 78.78,65.42 79.09,65.05L79.09,65.05C82.28,61.28 85.35,57.48 88.22,53.55L88.22,53.55C91.07,49.64 93.69,45.61 96.25,41.56L96.25,41.56C96.5,41.17 97.02,41 97.42,41.18L97.42,41.18C98.27,41.59 99.1,42.01 99.95,42.41L99.95,42.41C100.36,42.6 100.48,43.08 100.24,43.47L100.24,43.47C97.67,47.52 95.05,51.57 92.19,55.49L92.19,55.49C89.32,59.42 86.23,63.23 83.04,67L83.04,67C82.83,67.24 82.49,67.37 82.18,67.37L82.18,67.37C82.03,67.37 81.88,67.34 81.75,67.28M88.51,70.63C87.94,70.35 87.37,70.08 86.79,69.79L86.79,69.79C86.54,69.66 86.5,69.35 86.71,69.11L86.71,69.11C91.04,63.98 95.1,58.71 98.94,53.33L98.94,53.33C99.12,53.08 99.49,52.97 99.76,53.1L99.76,53.1C100.33,53.38 100.91,53.66 101.49,53.93L101.49,53.93C101.76,54.06 101.82,54.39 101.64,54.64L101.64,54.64C97.8,60.02 93.72,65.29 89.39,70.43L89.39,70.43C89.24,70.6 89.02,70.69 88.8,70.69L88.8,70.69C88.7,70.69 88.6,70.67 88.51,70.63M55.2,92.37C51.81,90.63 50.3,88.17 50.41,85.53L50.41,85.53C50.52,82.88 52.21,80.02 55.35,77.5L55.35,77.5C58.5,74.98 62.45,73.29 66.56,72.65L66.56,72.65C70.63,72.03 74.83,72.44 78.26,74.15L78.26,74.15C81.69,75.86 83.72,78.54 84.09,81.43L84.09,81.43C84.46,84.32 83.16,87.45 79.99,89.99L79.99,89.99C76.81,92.54 72.28,94.03 67.77,94.42L67.77,94.42C66.94,94.49 66.11,94.53 65.28,94.53L65.28,94.53C61.55,94.53 57.98,93.8 55.2,92.37"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="51.35"
            android:startY="95.45"
            android:endX="96.24"
            android:endY="46.64"
            android:type="linear">
          <item android:offset="0" android:color="#FF64CC50"/>
          <item android:offset="1" android:color="#FF95EB87"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M34.74,105.31L58.95,117.56C71.29,108.87 82.85,99.41 93.93,89.47C83.09,100.22 71.5,109.98 59.15,118.72L34.74,105.31Z"
      android:strokeWidth="1"
      android:fillColor="#1F3F37"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M47.2,115.04L59.55,121.08C68.66,114.87 77.22,108.12 85.45,101.03C77.48,108.66 68.93,115.77 59.76,122.33L47.2,115.04Z"
      android:strokeWidth="1"
      android:fillColor="#1F3F37"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M59.88,62.94C68.1,68.41 87.31,78.13 98.31,82.46L98.85,82L59.88,62.94Z"
      android:strokeWidth="1"
      android:fillColor="#1F3F37"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M72.37,78.01C76.57,80.13 83,83.3 91.64,87.52C91.88,87.63 92.08,87.88 92.23,88.29C92.63,89.75 93.52,92.39 94.9,96.22C95.63,96.01 102.9,89.86 102.65,88.79C102.07,86.25 101.63,84.32 100.9,81.93C100.8,81.58 100.63,81.25 100.4,80.95C100.14,80.63 99.83,80.37 99.45,80.2C93.38,77.78 86.23,74.59 79.94,71.41C73.6,68.21 66.5,64.36 61.7,61.07C61.62,61.06 61.55,61.06 61.47,61.06C59.95,61.06 53,67.68 53.49,68.15C57.42,70.42 63.71,73.7 72.37,78.01Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="77.95"
          android:startY="61.06"
          android:endX="77.95"
          android:endY="96.22"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M100.28,82.47C99.85,82.93 99.12,83.12 98.72,82.92L98.72,82.92C98.69,82.9 98.67,82.89 98.65,82.88L98.65,82.88C98.59,82.84 98.54,82.79 98.51,82.73L98.51,82.73C98.53,82.78 98.55,82.83 98.57,82.9L98.57,82.9C98.58,82.94 98.59,82.98 98.61,83.02L98.61,83.02C98.61,83.04 98.62,83.06 98.62,83.08L98.62,83.08C98.98,84.37 99.34,85.85 99.69,87.3L99.69,87.3C99.91,88.22 100.12,89.13 100.32,89.98L100.32,89.98C100.33,90.06 100.35,90.15 100.37,90.23L100.37,90.23C100.43,90.47 100.49,90.67 100.58,90.82L100.58,90.82C100.72,91.03 100.93,91.11 101.29,91.01L101.29,91.01C102,90.8 102.84,89.91 102.67,88.88L102.67,88.88C102.67,88.85 102.66,88.82 102.65,88.79L102.65,88.79C102.44,87.86 102.24,87.01 102.05,86.2L102.05,86.2C101.73,84.89 101.42,83.65 101.01,82.29L101.01,82.29C100.98,82.17 100.94,82.05 100.9,81.93L100.9,81.93C100.82,81.66 100.7,81.4 100.54,81.15L100.54,81.15C100.75,81.69 100.59,82.15 100.28,82.47"/>
  </group>
</vector>
