<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="240dp"
    android:viewportWidth="200"
    android:viewportHeight="240">
  <path
      android:pathData="M150.84,226.16L66.64,226.16C60.5,226.16 55.53,221.19 55.53,215.06L55.53,25.11C55.53,18.97 60.5,14 66.64,14L150.84,14C156.97,14 161.94,18.97 161.94,25.11L161.94,215.06C161.94,221.19 156.97,226.16 150.84,226.16"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M150.84,12C154.46,12 157.73,13.47 160.1,15.84C162.48,18.21 163.94,21.49 163.94,25.11L163.94,25.11L163.94,215.06C163.94,218.67 162.48,221.95 160.1,224.32C157.73,226.69 154.46,228.16 150.84,228.16L150.84,228.16L66.64,228.16C63.02,228.16 59.74,226.69 57.37,224.32C55,221.95 53.53,218.67 53.53,215.06L53.53,215.06L53.53,25.11C53.53,21.49 55,18.21 57.37,15.84C59.74,13.47 63.02,12 66.64,12L66.64,12Z"
      android:strokeWidth="4"
      android:fillColor="#00000000"
      android:fillType="evenOdd">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="53.53"
          android:startY="120.08"
          android:endX="163.94"
          android:endY="120.08"
          android:type="linear">
        <item android:offset="0" android:color="#FF363A4A"/>
        <item android:offset="1" android:color="#FF1D2029"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M119.27,220.84L98.21,220.84C96.62,220.84 95.32,219.55 95.32,217.96C95.32,216.37 96.62,215.08 98.21,215.08L119.27,215.08C120.86,215.08 122.15,216.37 122.15,217.96C122.15,219.55 120.86,220.84 119.27,220.84"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="108.74"
          android:startY="215.08"
          android:endX="108.74"
          android:endY="220.69"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF844D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M172.82,82.49L44.65,82.49C37.52,82.49 31.74,77.44 31.74,71.22L31.74,43.54C31.74,37.32 37.52,32.27 44.65,32.27L172.82,32.27C179.95,32.27 185.74,37.32 185.74,43.54L185.74,71.22C185.74,77.44 179.95,82.49 172.82,82.49"
      android:strokeWidth="1"
      android:fillColor="#FFF1E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M74.01,56.91C74.01,63.89 68.35,69.55 61.37,69.55C54.4,69.55 48.74,63.89 48.74,56.91C48.74,49.93 54.4,44.27 61.37,44.27C68.35,44.27 74.01,49.93 74.01,56.91"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="61.38"
          android:startY="44.27"
          android:endX="61.38"
          android:endY="68.9"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF844D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M91,46L118,46A4,4 0,0 1,122 50L122,50A4,4 0,0 1,118 54L91,54A4,4 0,0 1,87 50L87,50A4,4 0,0 1,91 46z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="104.5"
          android:startY="46"
          android:endX="104.5"
          android:endY="54"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M91,59L153,59A4,4 0,0 1,157 63L157,63A4,4 0,0 1,153 67L91,67A4,4 0,0 1,87 63L87,63A4,4 0,0 1,91 59z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="122"
          android:startY="59"
          android:endX="122"
          android:endY="67"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M158.02,138L58.98,138C53.47,138 49,134.08 49,129.25L49,107.75C49,102.92 53.47,99 58.98,99L158.02,99C163.53,99 168,102.92 168,107.75L168,129.25C168,134.08 163.53,138 158.02,138"
      android:strokeWidth="1"
      android:fillColor="#FFF1E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M81,118.5C81,123.75 76.75,128 71.5,128C66.25,128 62,123.75 62,118.5C62,113.25 66.25,109 71.5,109C76.75,109 81,113.25 81,118.5"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="71.5"
          android:startY="109"
          android:endX="71.5"
          android:endY="127.51"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF844D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M99.5,111L121.5,111A2.5,2.5 0,0 1,124 113.5L124,113.5A2.5,2.5 0,0 1,121.5 116L99.5,116A2.5,2.5 0,0 1,97 113.5L97,113.5A2.5,2.5 0,0 1,99.5 111z"
      android:strokeAlpha="0.21455166"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.21455166">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="110.5"
          android:startY="111"
          android:endX="110.5"
          android:endY="116"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M99.5,121L148.5,121A2.5,2.5 0,0 1,151 123.5L151,123.5A2.5,2.5 0,0 1,148.5 126L99.5,126A2.5,2.5 0,0 1,97 123.5L97,123.5A2.5,2.5 0,0 1,99.5 121z"
      android:strokeAlpha="0.21455166"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.21455166">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="124"
          android:startY="121"
          android:endX="124"
          android:endY="126"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M144.87,182L74.13,182C70.19,182 67,179.19 67,175.72L67,160.28C67,156.81 70.19,154 74.13,154L144.87,154C148.81,154 152,156.81 152,160.28L152,175.72C152,179.19 148.81,182 144.87,182"
      android:strokeWidth="1"
      android:fillColor="#FFF1E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M89,168C89,171.87 85.87,175 82,175C78.13,175 75,171.87 75,168C75,164.13 78.13,161 82,161C85.87,161 89,164.13 89,168"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="82"
          android:startY="161"
          android:endX="82"
          android:endY="174.64"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF844D"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M98,163L113,163A2,2 0,0 1,115 165L115,165A2,2 0,0 1,113 167L98,167A2,2 0,0 1,96 165L96,165A2,2 0,0 1,98 163z"
      android:strokeAlpha="0.105836414"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.105836414">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="105.5"
          android:startY="163"
          android:endX="105.5"
          android:endY="167"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M98,170L133,170A2,2 0,0 1,135 172L135,172A2,2 0,0 1,133 174L98,174A2,2 0,0 1,96 172L96,172A2,2 0,0 1,98 170z"
      android:strokeAlpha="0.105836414"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"
      android:fillAlpha="0.105836414">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="115.5"
          android:startY="170"
          android:endX="115.5"
          android:endY="174"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M11,145C11,160.36 19.2,174.56 32.5,182.24C45.8,189.92 62.2,189.92 75.5,182.24C88.8,174.56 97,160.36 97,145C97,121.25 77.75,102 54,102C30.25,102 11,121.25 11,145Z"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M11,145C11,160.36 19.2,174.56 32.5,182.24C45.8,189.92 62.2,189.92 75.5,182.24C88.8,174.56 97,160.36 97,145C97,121.25 77.75,102 54,102C30.25,102 11,121.25 11,145Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="54"
          android:startY="102"
          android:endX="54"
          android:endY="189.92"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M57.83,137.34L57.9,137.42L59.15,139.03C60.91,141.3 63.49,142.94 66.45,143.67L66.73,143.73L67.56,143.92C68.55,144.13 69.18,145.02 68.96,145.92C68.74,146.82 67.76,147.39 66.75,147.21L66.65,147.19L65.82,147C62.89,146.34 60.21,144.99 58.06,143.09L57.21,148.56C57.19,148.69 57.16,148.82 57.12,148.94L59.6,152.08C61.44,154.4 62.55,157.13 62.82,159.97L62.85,160.27L63.05,162.9C63.13,163.96 62.25,164.89 61.07,164.99C59.89,165.09 58.84,164.33 58.69,163.27L58.68,163.16L58.48,160.54C58.32,158.4 57.53,156.34 56.2,154.57L56.03,154.35L53.74,151.46C53.65,151.46 53.57,151.46 53.48,151.46L52.95,154.25C52.44,156.89 50.44,159.09 47.66,160.05L47.46,160.12L41.93,161.89C40.81,162.25 39.58,161.75 39.15,160.75C38.72,159.75 39.25,158.63 40.35,158.22L40.46,158.18L45.99,156.41C47.32,155.98 48.31,154.97 48.6,153.73L48.63,153.59L49.76,147.7L49.77,147.66L50.86,140.67C48.39,141.56 46.19,142.96 44.43,144.76C43.75,145.46 42.57,145.53 41.79,144.92C41.01,144.31 40.93,143.24 41.61,142.54C43.92,140.16 46.88,138.35 50.2,137.29L50.54,137.18L52.14,136.71C53.83,137.56 55.83,137.74 57.67,137.19C57.73,137.24 57.78,137.29 57.82,137.34L57.83,137.34Z"
      android:strokeWidth="2"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#FFFFFF"/>
  <path
      android:pathData="M54.95,132.76C56.42,132.97 57.89,132.41 58.81,131.31C59.72,130.21 59.94,128.73 59.38,127.43C58.82,126.13 57.57,125.22 56.09,125.03C53.84,124.75 51.77,126.24 51.45,128.36C51.14,130.48 52.7,132.45 54.95,132.76Z"
      android:strokeWidth="2"
      android:fillColor="#FFFFFF"
      android:fillType="nonZero"
      android:strokeColor="#FFFFFF"/>
</vector>
