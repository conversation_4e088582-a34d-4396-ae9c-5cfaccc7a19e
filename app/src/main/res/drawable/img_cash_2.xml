<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="150dp"
    android:height="150dp"
    android:viewportWidth="150"
    android:viewportHeight="150">
  <group>
    <clip-path
        android:pathData="M23.14,87.98L48.68,119.22L48.68,119.22C80.95,109.12 111.02,93.66 139.34,73.93L139.34,73.93L112.2,44.52C85.18,63.67 55.31,77.83 23.14,87.98"/>
    <path
        android:pathData="M23.14,87.98L48.68,119.22L48.69,119.22C80.95,109.12 111.02,93.67 139.34,73.93L139.34,73.93L112.19,44.53C85.18,63.67 55.31,77.83 23.14,87.98"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="48.75"
            android:startY="100.6"
            android:endX="142.82"
            android:endY="44.86"
            android:type="linear">
          <item android:offset="0" android:color="#FF2F9531"/>
          <item android:offset="1" android:color="#FF50B850"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M106.09,52.46C95.21,59.25 84.07,65.9 72.32,71.66L72.32,71.66C60.44,77.46 47.99,82.37 35.3,87.07L35.3,87.07C37.09,89.26 35.78,92 32.34,93.15L32.34,93.15C34.85,96.21 37.36,99.27 39.87,102.31L39.87,102.31C42.38,105.37 44.89,108.42 47.4,111.48L47.4,111.48C50.9,110.33 55.21,111.13 57.01,113.29L57.01,113.29C69.93,108.46 82.56,103.34 94.56,97.25L94.56,97.25C106.42,91.23 117.69,84.31 128.73,77.29L128.73,77.29C126.86,75.21 127.65,71.99 130.48,70.07L130.48,70.07C127.84,67.19 125.2,64.31 122.55,61.44L122.55,61.44C119.9,58.56 117.25,55.67 114.61,52.8L114.61,52.8C111.82,54.68 107.97,54.52 106.09,52.46M86.18,80.77C85.6,80.12 85.02,79.47 84.43,78.8L84.43,78.8C84.16,78.5 84.31,78.06 84.77,77.82L84.77,77.82C89.43,75.36 94.05,72.87 98.57,70.25L98.57,70.25C103.08,67.63 107.49,64.89 111.86,62.11L111.86,62.11C112.28,61.84 112.84,61.86 113.11,62.16L113.11,62.16C113.7,62.8 114.27,63.44 114.87,64.08L114.87,64.08C115.14,64.38 115.03,64.87 114.62,65.13L114.62,65.13C110.23,67.94 105.81,70.67 101.28,73.3L101.28,73.3C96.77,75.94 92.13,78.45 87.47,80.92L87.47,80.92C87.23,81.05 86.93,81.09 86.69,81.05L86.69,81.05C86.48,81.01 86.29,80.92 86.18,80.77M90.83,86.11C90.44,85.66 90.05,85.21 89.65,84.76L89.65,84.76C89.47,84.55 89.55,84.25 89.87,84.1L89.87,84.1C96.2,80.69 102.42,77.2 108.54,73.57L108.54,73.57C108.82,73.39 109.23,73.41 109.41,73.62L109.41,73.62C109.8,74.05 110.2,74.52 110.59,74.94L110.59,74.94C110.78,75.16 110.69,75.47 110.41,75.63L110.41,75.63C104.29,79.28 98.05,82.78 91.7,86.2L91.7,86.2C91.54,86.29 91.33,86.31 91.16,86.28L91.16,86.28C91.03,86.26 90.91,86.2 90.83,86.11M62.49,103.47C58.42,102.66 54.71,100.72 52.38,97.94L52.38,97.94C50.07,95.16 49.56,92.04 50.55,89.26L50.55,89.26C51.57,86.45 54.11,83.93 57.91,82.34L57.91,82.34C61.69,80.74 65.99,80.35 69.98,81.03L69.98,81.03C73.92,81.7 77.56,83.44 79.93,86.17L79.93,86.17C82.29,88.9 82.93,92.1 82.04,95.06L82.04,95.06C81.16,97.99 78.71,100.68 74.88,102.33L74.88,102.33C71.22,103.91 66.96,104.27 63.02,103.58L63.02,103.58C62.84,103.55 62.66,103.52 62.49,103.47"/>
    <path
        android:pathData="M106.09,52.46C95.22,59.25 84.07,65.9 72.32,71.65L72.32,71.65C60.44,77.47 47.99,82.37 35.3,87.08L35.3,87.08C37.09,89.26 35.78,92.01 32.34,93.15L32.34,93.15C34.85,96.21 37.37,99.27 39.87,102.31L39.87,102.31C42.38,105.37 44.89,108.42 47.4,111.48L47.4,111.48C50.9,110.33 55.2,111.13 57,113.3L57,113.3C69.93,108.47 82.56,103.34 94.57,97.25L94.57,97.25C106.42,91.23 117.69,84.31 128.73,77.29L128.73,77.29C126.86,75.21 127.65,72 130.48,70.07L130.48,70.07C127.84,67.19 125.2,64.31 122.54,61.43L122.54,61.43C119.9,58.56 117.25,55.67 114.61,52.79L114.61,52.79C111.82,54.68 107.97,54.52 106.09,52.46M86.18,80.78C85.6,80.12 85.02,79.48 84.43,78.8L84.43,78.8C84.17,78.5 84.31,78.06 84.76,77.82L84.76,77.82C89.43,75.36 94.05,72.87 98.58,70.24L98.58,70.24C103.08,67.63 107.49,64.9 111.86,62.11L111.86,62.11C112.28,61.84 112.83,61.85 113.11,62.16L113.11,62.16C113.7,62.8 114.28,63.44 114.87,64.08L114.87,64.08C115.15,64.38 115.02,64.88 114.61,65.14L114.61,65.14C110.23,67.93 105.81,70.67 101.29,73.31L101.29,73.31C96.77,75.94 92.13,78.45 87.47,80.92L87.47,80.92C87.23,81.05 86.94,81.09 86.69,81.04L86.69,81.04C86.48,81.01 86.3,80.92 86.18,80.78M90.83,86.11C90.44,85.65 90.05,85.21 89.65,84.76L89.65,84.76C89.47,84.55 89.56,84.26 89.87,84.09L89.87,84.09C96.2,80.7 102.43,77.2 108.54,73.57L108.54,73.57C108.83,73.4 109.23,73.41 109.41,73.62L109.41,73.62C109.81,74.05 110.2,74.51 110.59,74.95L110.59,74.95C110.78,75.16 110.7,75.47 110.41,75.64L110.41,75.64C104.29,79.27 98.05,82.78 91.7,86.19L91.7,86.19C91.54,86.28 91.34,86.31 91.16,86.28L91.16,86.28C91.03,86.25 90.91,86.2 90.83,86.11M62.49,103.48C58.41,102.66 54.71,100.73 52.39,97.94L52.39,97.94C50.07,95.16 49.55,92.04 50.55,89.25L50.55,89.25C51.56,86.45 54.11,83.93 57.9,82.34L57.9,82.34C61.69,80.74 66,80.36 69.97,81.03L69.97,81.03C73.93,81.7 77.56,83.44 79.93,86.17L79.93,86.17C82.29,88.9 82.93,92.1 82.04,95.06L82.04,95.06C81.16,97.99 78.7,100.68 74.88,102.33L74.88,102.33C71.22,103.91 66.96,104.27 63.01,103.58L63.01,103.58C62.84,103.54 62.66,103.51 62.49,103.48"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="48.63"
            android:startY="100.1"
            android:endX="195.73"
            android:endY="24.11"
            android:type="linear">
          <item android:offset="0" android:color="#FF64CC50"/>
          <item android:offset="1" android:color="#FF95EB87"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M76.23,17.2L78.6,17.87C64.27,40.84 48.21,62.41 29.87,82.15L29.87,82.15L17.81,75.49C41.6,59.93 60.92,40.36 76.23,17.2L76.23,17.2Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M105.25,39.76L108.44,42.2C93.73,62.76 75.59,80.54 53.86,95.4L53.86,95.4L41.82,88.75C65.12,75.01 86.39,58.83 105.25,39.76L105.25,39.76Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M69.16,19C53.21,46.82 32.05,70.05 5,88.1L5,88.1L41.23,107.68C68.66,88.56 90.26,64.79 106.34,36.62L106.34,36.62L69.16,19Z"/>
    <path
        android:pathData="M69.16,19C53.21,46.82 32.05,70.05 5,88.09L5,88.09L41.23,107.68C68.66,88.56 90.26,64.79 106.34,36.62L106.34,36.62L69.16,19Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="32.08"
            android:startY="93.91"
            android:endX="76.77"
            android:endY="34.58"
            android:type="linear">
          <item android:offset="0" android:color="#FF2F9531"/>
          <item android:offset="1" android:color="#FF50B850"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M67.35,28.47C51.66,51.94 38.22,66.25 16.38,83.51L16.38,83.51C18.93,84.87 18.7,87.71 15.8,89.79L15.8,89.79C19.36,91.71 22.93,93.63 26.48,95.54L26.48,95.54C30.04,97.45 33.61,99.35 37.17,101.27L37.17,101.27C40.11,99.17 44.57,98.54 47.12,99.9L47.12,99.9C69.35,83 82.73,67.11 98.41,43.41L98.41,43.41C95.83,42.17 95.13,38.92 96.77,36.16L96.77,36.16C93.15,34.43 89.51,32.71 85.89,30.98L85.89,30.98C82.27,29.26 78.64,27.52 75.02,25.79L75.02,25.79C73.41,28.52 69.92,29.72 67.35,28.47M61.52,61.22C60.72,60.82 59.92,60.41 59.13,60.01L59.13,60.01C58.76,59.82 58.69,59.37 59.01,59.01L59.01,59.01C62.24,55.2 65.41,51.37 68.36,47.4L68.36,47.4C71.28,43.46 74.03,39.42 76.72,35.35L76.72,35.35C76.98,34.95 77.47,34.77 77.86,34.96L77.86,34.96C78.67,35.34 79.46,35.73 80.26,36.12L80.26,36.12C80.64,36.3 80.76,36.77 80.5,37.16L80.5,37.16C77.81,41.25 75.05,45.3 72.11,49.26L72.11,49.26C69.16,53.23 66,57.09 62.77,60.89L62.77,60.89C62.54,61.16 62.2,61.3 61.89,61.3L61.89,61.3C61.76,61.3 61.63,61.28 61.52,61.22M67.96,64.47C67.42,64.2 66.87,63.92 66.32,63.64L66.32,63.64C66.07,63.52 66.04,63.2 66.25,62.95L66.25,62.95C70.61,57.74 74.78,52.41 78.74,46.99L78.74,46.99C78.92,46.74 79.3,46.62 79.55,46.74L79.55,46.74C80.09,47.01 80.62,47.28 81.17,47.55L81.17,47.55C81.42,47.67 81.47,47.99 81.28,48.25L81.28,48.25C77.32,53.67 73.17,59.01 68.81,64.23L68.81,64.23C68.65,64.41 68.41,64.52 68.2,64.52L68.2,64.52C68.11,64.52 68.03,64.5 67.96,64.47M36.86,87.73C33.58,86 31.9,83.39 31.77,80.57L31.77,80.57C31.65,77.76 33.07,74.7 35.98,72.03L35.98,72.03C38.9,69.37 42.74,67.58 46.71,66.86L46.71,66.86C50.63,66.16 54.69,66.51 57.97,68.18L57.97,68.18C61.26,69.86 63.16,72.53 63.53,75.48L63.53,75.48C63.89,78.41 62.69,81.66 59.77,84.39L59.77,84.39C56.85,87.12 52.74,88.84 48.57,89.42L48.57,89.42C47.5,89.57 46.42,89.65 45.36,89.65L45.36,89.65C42.27,89.65 39.31,89.02 36.86,87.73"/>
    <path
        android:pathData="M67.35,28.47C51.66,51.93 38.22,66.25 16.38,83.51L16.38,83.51C18.93,84.87 18.7,87.71 15.8,89.79L15.8,89.79C19.36,91.71 22.93,93.63 26.49,95.54L26.49,95.54C30.04,97.45 33.61,99.35 37.17,101.27L37.17,101.27C40.11,99.17 44.57,98.54 47.12,99.9L47.12,99.9C69.35,82.99 82.73,67.1 98.41,43.4L98.41,43.4C95.83,42.16 95.13,38.92 96.77,36.16L96.77,36.16C93.15,34.43 89.51,32.7 85.89,30.98L85.89,30.98C82.27,29.25 78.64,27.52 75.02,25.78L75.02,25.78C73.41,28.52 69.92,29.72 67.35,28.47M61.52,61.22C60.72,60.82 59.92,60.41 59.13,60.01L59.13,60.01C58.76,59.82 58.69,59.37 59.01,59L59.01,59C62.24,55.2 65.41,51.36 68.36,47.4L68.36,47.4C71.28,43.46 74.03,39.42 76.72,35.34L76.72,35.34C76.98,34.95 77.47,34.77 77.86,34.95L77.86,34.95C78.67,35.34 79.46,35.73 80.26,36.11L80.26,36.11C80.64,36.3 80.76,36.76 80.5,37.16L80.5,37.16C77.81,41.25 75.05,45.3 72.11,49.26L72.11,49.26C69.16,53.23 66,57.08 62.77,60.89L62.77,60.89C62.54,61.16 62.2,61.3 61.89,61.3L61.89,61.3C61.76,61.3 61.63,61.28 61.52,61.22M67.96,64.47C67.42,64.19 66.87,63.91 66.32,63.64L66.32,63.64C66.07,63.52 66.04,63.2 66.25,62.95L66.25,62.95C70.61,57.74 74.78,52.4 78.74,46.99L78.74,46.99C78.92,46.74 79.3,46.61 79.55,46.74L79.55,46.74C80.09,47 80.62,47.28 81.17,47.55L81.17,47.55C81.42,47.67 81.47,47.99 81.28,48.25L81.28,48.25C77.32,53.67 73.17,59.01 68.81,64.23L68.81,64.23C68.65,64.41 68.41,64.52 68.2,64.52L68.2,64.52C68.11,64.52 68.03,64.5 67.96,64.47M36.86,87.73C33.58,86 31.9,83.39 31.77,80.57L31.77,80.57C31.65,77.76 33.07,74.7 35.98,72.03L35.98,72.03C38.9,69.37 42.74,67.57 46.71,66.86L46.71,66.86C50.63,66.16 54.69,66.51 57.97,68.18L57.97,68.18C61.26,69.85 63.16,72.52 63.53,75.48L63.53,75.48C63.89,78.41 62.69,81.65 59.77,84.38L59.77,84.38C56.85,87.12 52.74,88.84 48.57,89.42L48.57,89.42C47.5,89.57 46.42,89.64 45.36,89.64L45.36,89.64C42.28,89.64 39.31,89.01 36.86,87.73"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29.56"
            android:startY="103.82"
            android:endX="74.66"
            android:endY="38.42"
            android:type="linear">
          <item android:offset="0" android:color="#FF64CC50"/>
          <item android:offset="1" android:color="#FF95EB87"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
