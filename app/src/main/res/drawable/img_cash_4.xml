<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="150dp"
    android:height="150dp"
    android:viewportWidth="150"
    android:viewportHeight="150">
  <path
      android:pathData="M127.47,84.78C134.78,79.71 142.02,74.58 148.47,68.79L148.47,68.79L147.05,67.4L127.47,84.78Z"
      android:strokeWidth="1"
      android:fillColor="#081C31"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M126.25,83.11C133.56,78.03 140.8,72.91 147.25,67.12L147.25,67.12L145.83,65.73L126.25,83.11Z"
      android:strokeWidth="1"
      android:fillColor="#081C31"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M125.23,81.7C132.54,76.63 139.78,71.51 146.23,65.72L146.23,65.72L144.81,64.33L125.23,81.7Z"
      android:strokeWidth="1"
      android:fillColor="#081C31"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M29.29,89.07L29.87,95.7L55.42,128.95C89.15,113.14 120.39,93.38 149.19,69.77L149.19,69.77L148.47,68.79C142.49,73.53 136.33,78.1 129.93,82.43L129.93,82.43C136.04,77.8 142,72.94 147.82,67.89L147.82,67.89L147.25,67.12C144.34,69.42 141.34,71.56 138.33,73.69L138.33,73.69C141.17,71.29 143.99,68.88 146.7,66.36L146.7,66.36L146.23,65.72C140.74,70.12 135.04,74.29 129.25,78.35L129.25,78.35C134.78,73.93 140.23,69.42 145.49,64.69L145.49,64.69L144.72,63.65L141.65,61.02L29.29,89.07Z"
      android:strokeWidth="1"
      android:fillColor="#23712A"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M53.76,120.49L55.42,128.95C89.15,113.14 120.39,93.38 149.19,69.77L149.19,69.77L148.47,68.79C142.49,73.53 136.33,78.1 129.93,82.43L129.93,82.43C136.04,77.8 142,72.94 147.82,67.89L147.82,67.89L147.25,67.12C144.34,69.42 141.34,71.56 138.33,73.69L138.33,73.69C141.17,71.29 143.99,68.88 146.7,66.36L146.7,66.36L146.23,65.72C140.75,70.12 135.04,74.29 129.25,78.35L129.25,78.35C134.78,73.93 140.23,69.42 145.49,64.69L145.49,64.69L144.72,63.65C116.91,87.08 86.55,106.26 53.76,120.49"/>
    <path
        android:pathData="M53.76,120.49L55.42,128.95C89.15,113.14 120.39,93.38 149.19,69.77L149.19,69.77L148.47,68.79C142.49,73.53 136.33,78.1 129.93,82.43L129.93,82.43C136.04,77.8 142,72.94 147.82,67.89L147.82,67.89L147.25,67.12C144.34,69.42 141.34,71.56 138.33,73.69L138.33,73.69C141.17,71.29 143.99,68.88 146.7,66.36L146.7,66.36L146.23,65.72C140.74,70.12 135.04,74.29 129.25,78.35L129.25,78.35C134.78,73.93 140.23,69.42 145.49,64.69L145.49,64.69L144.72,63.65C116.91,87.08 86.55,106.26 53.76,120.49"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="53.76"
            android:startY="96.3"
            android:endX="149.19"
            android:endY="96.3"
            android:type="linear">
          <item android:offset="0" android:color="#FF26792D"/>
          <item android:offset="1" android:color="#FF216E26"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M29.87,95.7l25.55,33.25l-1.66,-8.46l-24.47,-31.42z"
      android:strokeWidth="1"
      android:fillColor="#23702A"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M29.17,85.66L26.34,86.37L51.49,119.17C87.03,106.53 118.13,86.92 144.95,60.6L144.95,60.6L140.1,55.85L29.17,85.66Z"
      android:strokeWidth="1"
      android:fillColor="#23722A"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M115.5,27C90.37,57.02 60.04,76.42 26,82.69L26,82.69L51.15,115.45C89.24,106.46 119.53,86.91 142.88,57.94L142.88,57.94L115.5,27Z"/>
    <path
        android:pathData="M115.5,27C90.37,57.02 60.04,76.42 26,82.68L26,82.68L51.15,115.44C89.24,106.46 119.53,86.91 142.88,57.94L142.88,57.94L115.5,27Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="43.63"
            android:startY="90.28"
            android:endX="157.71"
            android:endY="40.6"
            android:type="linear">
          <item android:offset="0" android:color="#FF2F9531"/>
          <item android:offset="1" android:color="#FF50B850"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M110.57,37.75C100.76,47.58 90.5,57.05 78.76,64.59L78.76,64.59C66.88,72.21 53.51,77.71 39.57,82.5L39.57,82.5C41.35,84.78 39.64,87.5 35.73,88.46L35.73,88.46C38.19,91.66 40.67,94.86 43.13,98.05L43.13,98.05C45.6,101.25 48.07,104.45 50.53,107.64L50.53,107.64C54.51,106.64 59.13,107.62 60.91,109.89L60.91,109.89C75.1,104.89 88.68,99.16 100.78,91.29L100.78,91.29C112.73,83.5 123.21,73.81 133.29,63.81L133.29,63.81C131.41,61.64 131.95,57.64 134.4,54.81L134.4,54.81C131.75,51.78 129.08,48.76 126.42,45.74L126.42,45.74C123.75,42.72 121.09,39.68 118.43,36.66L118.43,36.66C116.03,39.45 112.45,39.92 110.57,37.75M103.02,80.21C99.18,80.03 95.61,78.46 93.26,75.62L93.26,75.62C90.91,72.79 90.22,69.25 90.96,65.75L90.96,65.75C91.69,62.26 93.84,58.78 97.13,56.05L97.13,56.05C100.42,53.32 104.21,51.83 107.84,51.74L107.84,51.74C111.41,51.64 114.82,52.9 117.22,55.68L117.22,55.68C119.62,58.46 120.58,62.23 120.08,66.01L120.08,66.01C119.6,69.75 117.65,73.55 114.31,76.33L114.31,76.33C111.17,78.94 107.36,80.22 103.72,80.22L103.72,80.22C103.49,80.22 103.25,80.22 103.02,80.21M61.25,89.15C60.88,88.67 60.48,88.2 60.1,87.72L60.1,87.72C59.92,87.5 60.09,87.2 60.44,87.05L60.44,87.05C67.69,83.91 74.69,80.39 81.45,76.57L81.45,76.57C81.78,76.38 82.17,76.41 82.35,76.62L82.35,76.62C82.74,77.1 83.13,77.56 83.51,78.03L83.51,78.03C83.7,78.25 83.59,78.6 83.27,78.78L83.27,78.78C76.49,82.62 69.49,86.12 62.23,89.3L62.23,89.3C62.11,89.35 61.97,89.38 61.84,89.38L61.84,89.38C61.6,89.38 61.36,89.29 61.25,89.15M54.77,99.58C54.22,98.87 53.67,98.15 53.13,97.44L53.13,97.44C52.87,97.11 53.1,96.7 53.65,96.51L53.65,96.51C59.34,94.62 64.93,92.61 70.27,90.19L70.27,90.19C75.56,87.79 80.62,85 85.61,82.14L85.61,82.14C86.08,81.87 86.67,81.89 86.94,82.21L86.94,82.21C87.51,82.91 88.08,83.6 88.65,84.29L88.65,84.29C88.91,84.62 88.76,85.13 88.27,85.41L88.27,85.41C83.29,88.27 78.21,91.08 72.92,93.48L72.92,93.48C67.55,95.92 61.96,97.94 56.26,99.84L56.26,99.84C56.08,99.9 55.89,99.93 55.71,99.93L55.71,99.93C55.32,99.93 54.95,99.8 54.77,99.58"/>
    <path
        android:pathData="M110.57,37.75C100.76,47.58 90.5,57.05 78.76,64.59L78.76,64.59C66.88,72.21 53.51,77.71 39.57,82.5L39.57,82.5C41.35,84.78 39.64,87.5 35.72,88.46L35.72,88.46C38.19,91.66 40.67,94.86 43.13,98.05L43.13,98.05C45.6,101.24 48.07,104.45 50.53,107.64L50.53,107.64C54.51,106.64 59.13,107.61 60.91,109.89L60.91,109.89C75.1,104.89 88.68,99.16 100.78,91.29L100.78,91.29C112.73,83.5 123.21,73.81 133.29,63.81L133.29,63.81C131.41,61.64 131.95,57.64 134.4,54.81L134.4,54.81C131.75,51.78 129.08,48.76 126.42,45.74L126.42,45.74C123.75,42.72 121.09,39.68 118.43,36.66L118.43,36.66C116.03,39.45 112.45,39.92 110.57,37.75M103.02,80.21C99.18,80.03 95.61,78.45 93.26,75.62L93.26,75.62C90.91,72.79 90.22,69.25 90.96,65.75L90.96,65.75C91.69,62.25 93.84,58.78 97.13,56.05L97.13,56.05C100.42,53.32 104.21,51.83 107.84,51.74L107.84,51.74C111.41,51.64 114.82,52.9 117.22,55.68L117.22,55.68C119.62,58.46 120.58,62.23 120.08,66.01L120.08,66.01C119.6,69.75 117.65,73.55 114.31,76.33L114.31,76.33C111.17,78.94 107.36,80.22 103.72,80.22L103.72,80.22C103.49,80.22 103.25,80.22 103.02,80.21M61.25,89.15C60.88,88.67 60.48,88.2 60.1,87.72L60.1,87.72C59.92,87.5 60.09,87.2 60.44,87.05L60.44,87.05C67.69,83.9 74.69,80.39 81.45,76.57L81.45,76.57C81.78,76.38 82.17,76.41 82.35,76.62L82.35,76.62C82.74,77.1 83.13,77.56 83.51,78.03L83.51,78.03C83.7,78.25 83.59,78.6 83.27,78.78L83.27,78.78C76.49,82.62 69.49,86.12 62.23,89.3L62.23,89.3C62.11,89.35 61.97,89.38 61.84,89.38L61.84,89.38C61.6,89.38 61.36,89.29 61.25,89.15M54.78,99.57C54.22,98.87 53.67,98.15 53.13,97.44L53.13,97.44C52.87,97.11 53.1,96.7 53.65,96.51L53.65,96.51C59.34,94.62 64.93,92.61 70.27,90.19L70.27,90.19C75.56,87.79 80.62,85 85.61,82.13L85.61,82.13C86.08,81.86 86.67,81.89 86.94,82.21L86.94,82.21C87.51,82.91 88.08,83.59 88.65,84.29L88.65,84.29C88.91,84.62 88.76,85.13 88.27,85.41L88.27,85.41C83.29,88.27 78.21,91.08 72.93,93.48L72.93,93.48C67.55,95.92 61.96,97.94 56.26,99.84L56.26,99.84C56.08,99.9 55.89,99.93 55.71,99.93L55.71,99.93C55.32,99.93 54.95,99.8 54.78,99.57"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="43.54"
            android:startY="91.5"
            android:endX="140.15"
            android:endY="52.82"
            android:type="linear">
          <item android:offset="0" android:color="#FF64CC50"/>
          <item android:offset="1" android:color="#FF95EB87"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M104.05,93.38C104.09,93.46 104.11,93.54 104.12,93.61L104.12,93.61C104.24,94.56 102.72,95.43 102.04,94.94L102.04,94.94C101.98,94.9 101.94,94.85 101.89,94.8L101.89,94.8C101.93,94.85 101.96,94.91 102,94.96L102,94.96C102.14,95.19 102.27,95.45 102.4,95.72L102.4,95.72C103.34,97.69 104.24,99.69 105.07,101.7L105.07,101.7C105.94,102.04 107.03,101.63 107.1,100.41L107.1,100.41C107.1,100.4 107.1,100.4 107.1,100.39L107.1,100.39C107.15,99.63 105.22,95.6 104.75,94.61L104.75,94.61C104.58,94.24 104.39,93.9 104.19,93.57L104.19,93.57C104.15,93.5 104.11,93.44 104.07,93.38L104.07,93.38C104.04,93.35 104.02,93.31 104,93.28L104,93.28C104.02,93.32 104.04,93.35 104.05,93.38"/>
  </group>
  <path
      android:pathData="M96.93,100.26C83.28,108.33 68.89,115.4 54.02,121.82L54.36,123.55C69.13,116.71 83.45,109.15 96.93,100.26M86.71,110.58C76.44,116.31 65.78,121.43 54.88,126.21L55.15,127.59C60.23,125.1 83.01,113.43 86.71,110.58"
      android:strokeWidth="1"
      android:fillColor="#1F3F37"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M101.05,42.54L104.95,49.68C105.24,50.22 105.18,50.86 104.78,51.33C86.69,72.88 67.25,92.24 46.27,109.08L46.27,109.08L26,82.69C53.75,77.58 79.03,63.74 101.05,42.54"/>
  </group>
  <path
      android:pathData="M81.12,82.77C84.89,85.59 90.66,89.83 98.45,95.48C98.66,95.62 98.81,95.91 98.89,96.34C99.03,97.85 99.45,100.6 100.14,104.61C100.9,104.53 109.13,99.73 109.07,98.64C108.94,96.04 108.83,94.06 108.54,91.59C108.49,91.22 108.39,90.86 108.21,90.53C108.01,90.17 107.75,89.86 107.4,89.62C101.85,86.19 95.37,81.8 89.72,77.58C84.04,73.33 77.71,68.31 73.55,64.23C73.47,64.21 73.4,64.19 73.33,64.18C71.83,63.92 63.84,69.23 64.24,69.78C67.71,72.69 73.34,77.02 81.12,82.77Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="86.48"
          android:startY="63.92"
          android:endX="86.48"
          android:endY="104.61"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M115.39,51.89l-3.66,-6.46l-15.25,18.25l4.9,3.01z"
      android:strokeWidth="1"
      android:fillColor="#081C31"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M4.52,99.39l2.43,-1.25l18.66,12.37z"/>
    <path
        android:pathData="M4.52,99.39l2.43,-1.25l18.66,12.37z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="4.52"
            android:startY="104.32"
            android:endX="25.61"
            android:endY="104.32"
            android:type="linear">
          <item android:offset="0" android:color="#FF11601D"/>
          <item android:offset="1" android:color="#FF144421"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M4.02,96.08l2.44,-1.25l18.66,12.37z"/>
    <path
        android:pathData="M4.02,96.08l2.44,-1.25l18.66,12.37z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="4.02"
            android:startY="101.02"
            android:endX="25.12"
            android:endY="101.02"
            android:type="linear">
          <item android:offset="0" android:color="#FF11601D"/>
          <item android:offset="1" android:color="#FF144421"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M96.49,66.71C102.34,60.58 108.01,54.29 112.79,47.28L112.79,47.28L109.72,46.14L96.49,66.71Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M104.82,62.82C108.51,59.05 112.13,55.26 114.99,51.17L114.99,51.17L111.92,50.03L104.82,62.82Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M3.44,92.22L3.84,94.91L22.09,105.17L4.02,96.08L4.38,98.45L12.98,103.4L4.52,99.39L4.69,100.5L43.25,121.18C69.69,101.9 93.61,78.59 115.39,51.89L115.39,51.89L114.99,51.17C112.22,54.43 109.3,57.53 106.34,60.6L106.34,60.6C109.07,57.18 111.77,53.69 114.33,50.01L114.33,50.01L112.79,47.28C108.26,53.07 103.37,58.57 98.18,63.82L98.18,63.82C103.04,58.14 107.71,52.26 112.15,46.15L112.15,46.15L111.74,45.43L101.95,41.8L3.44,92.22Z"/>
    <path
        android:pathData="M3.44,92.22L3.84,94.91L22.09,105.16L4.02,96.08L4.38,98.45L12.98,103.4L4.52,99.38L4.69,100.49L43.25,121.18C69.69,101.9 93.61,78.59 115.39,51.89L115.39,51.89L114.99,51.17C112.22,54.43 109.3,57.53 106.34,60.59L106.34,60.59C109.07,57.17 111.77,53.69 114.33,50.01L114.33,50.01L112.79,47.28C108.26,53.07 103.37,58.57 98.18,63.82L98.18,63.82C103.04,58.13 107.71,52.25 112.15,46.15L112.15,46.15L111.74,45.42L101.95,41.79L3.44,92.22Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="3.44"
            android:startY="81.48"
            android:endX="115.39"
            android:endY="81.48"
            android:type="linear">
          <item android:offset="0" android:color="#FF24752B"/>
          <item android:offset="1" android:color="#FF1C5B24"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M41.76,112.43L43.25,121.18C69.69,101.9 93.6,78.59 115.39,51.89L115.39,51.89L114.99,51.17C112.22,54.43 109.3,57.53 106.34,60.59L106.34,60.59C109.07,57.18 111.77,53.69 114.33,50.01L114.33,50.01L112.79,47.28C108.26,53.07 103.37,58.57 98.18,63.82L98.18,63.82C103.04,58.14 107.71,52.26 112.15,46.15L112.15,46.15L111.74,45.43C91.6,71.62 67.82,93.4 41.76,112.43"/>
    <path
        android:pathData="M41.76,112.43L43.25,121.18C69.69,101.9 93.6,78.59 115.39,51.89L115.39,51.89L114.99,51.17C112.22,54.43 109.3,57.53 106.34,60.59L106.34,60.59C109.07,57.17 111.77,53.69 114.33,50.01L114.33,50.01L112.79,47.28C108.26,53.07 103.37,58.57 98.18,63.82L98.18,63.82C103.04,58.13 107.71,52.25 112.15,46.15L112.15,46.15L111.74,45.42C91.6,71.62 67.82,93.4 41.76,112.43"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="41.76"
            android:startY="83.3"
            android:endX="115.39"
            android:endY="83.3"
            android:type="linear">
          <item android:offset="0" android:color="#FF26792D"/>
          <item android:offset="1" android:color="#FF216E26"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M3.84,94.91l18.24,10.26l-18.07,-9.09l0.36,2.37l8.6,4.95l-8.46,-4.02l0.17,1.11l38.56,20.68l-1.49,-8.75l-38.33,-20.21z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M1.41,90.31L40.26,110.87C69.41,93.17 93.02,69.87 111.99,41.85L111.99,41.85L105.71,39.66L1.41,90.31Z"/>
  </group>
  <group>
    <clip-path
        android:pathData="M70.76,21C51.41,54.29 28.66,77.48 1,86.12L1,86.12C13.99,92.91 26.98,99.7 39.96,106.5L39.96,106.5C68.79,95.8 92.18,71.6 109.94,39.47L109.94,39.47L70.76,21Z"/>
    <path
        android:pathData="M70.76,21C51.41,54.29 28.66,77.48 1,86.12L1,86.12C13.99,92.91 26.98,99.7 39.96,106.49L39.96,106.49C68.79,95.79 92.18,71.6 109.94,39.47L109.94,39.47L70.76,21Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="1"
            android:startY="63.75"
            android:endX="109.94"
            android:endY="63.75"
            android:type="linear">
          <item android:offset="0" android:color="#FF2F9531"/>
          <item android:offset="1" android:color="#FF50B850"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M69.24,30.47C62.8,40.46 56.05,50.5 47.67,59.45L47.67,59.45C39.07,68.63 28.34,76.37 15.99,83.44L15.99,83.44C18.65,84.81 17.79,87.27 13.9,88.81L13.9,88.81C17.66,90.76 21.42,92.72 25.17,94.67L25.17,94.67C28.92,96.62 32.69,98.59 36.43,100.55L36.43,100.55C40.18,98.89 45.31,98.61 47.96,99.99L47.96,99.99C68.08,90.17 88.22,69.05 102.07,46.12L102.07,46.12C99.34,44.82 98.48,41.55 100.03,38.8L100.03,38.8C96.21,36.99 92.38,35.19 88.55,33.37L88.55,33.37C84.74,31.57 80.9,29.74 77.08,27.93L77.08,27.93C75.53,30.64 71.96,31.77 69.24,30.47M64.75,63.28C63.91,62.87 63.08,62.45 62.23,62.04L62.23,62.04C61.83,61.84 61.78,61.42 62.09,61.05L62.09,61.05C65.28,57.28 68.35,53.48 71.22,49.55L71.22,49.55C74.07,45.65 76.69,41.61 79.25,37.56L79.25,37.56C79.5,37.17 80.02,37 80.42,37.19L80.42,37.19C81.27,37.59 82.1,38.01 82.95,38.41L82.95,38.41C83.36,38.6 83.48,39.08 83.24,39.47L83.24,39.47C80.67,43.52 78.05,47.57 75.19,51.49L75.19,51.49C72.32,55.42 69.23,59.23 66.04,63L66.04,63C65.83,63.24 65.49,63.37 65.18,63.37L65.18,63.37C65.03,63.37 64.88,63.34 64.75,63.28M71.51,66.63C70.94,66.36 70.37,66.08 69.79,65.79L69.79,65.79C69.54,65.66 69.5,65.36 69.71,65.11L69.71,65.11C74.04,59.98 78.1,54.71 81.94,49.34L81.94,49.34C82.12,49.08 82.49,48.98 82.76,49.1L82.76,49.1C83.33,49.38 83.91,49.66 84.49,49.93L84.49,49.93C84.76,50.06 84.82,50.39 84.64,50.64L84.64,50.64C80.8,56.02 76.72,61.3 72.39,66.43L72.39,66.43C72.24,66.6 72.02,66.69 71.8,66.69L71.8,66.69C71.7,66.69 71.6,66.67 71.51,66.63M38.2,88.37C34.81,86.63 33.3,84.17 33.41,81.53L33.41,81.53C33.52,78.88 35.21,76.02 38.35,73.5L38.35,73.5C41.5,70.99 45.45,69.29 49.56,68.66L49.56,68.66C53.63,68.03 57.83,68.44 61.26,70.15L61.26,70.15C64.69,71.86 66.72,74.55 67.09,77.44L67.09,77.44C67.46,80.33 66.16,83.45 62.99,85.99L62.99,85.99C59.81,88.54 55.28,90.03 50.77,90.42L50.77,90.42C49.94,90.5 49.11,90.53 48.28,90.53L48.28,90.53C44.55,90.53 40.98,89.8 38.2,88.37"/>
    <path
        android:pathData="M69.24,30.46C62.8,40.46 56.05,50.5 47.67,59.45L47.67,59.45C39.07,68.62 28.34,76.37 15.99,83.44L15.99,83.44C18.65,84.81 17.79,87.27 13.9,88.81L13.9,88.81C17.66,90.76 21.42,92.72 25.17,94.67L25.17,94.67C28.92,96.62 32.69,98.59 36.43,100.54L36.43,100.54C40.18,98.89 45.31,98.61 47.96,99.99L47.96,99.99C68.08,90.17 88.22,69.05 102.07,46.12L102.07,46.12C99.34,44.82 98.48,41.54 100.03,38.8L100.03,38.8C96.21,36.99 92.38,35.18 88.55,33.37L88.55,33.37C84.74,31.57 80.9,29.74 77.08,27.92L77.08,27.92C75.53,30.64 71.96,31.76 69.24,30.46M64.75,63.28C63.91,62.86 63.08,62.45 62.23,62.04L62.23,62.04C61.83,61.84 61.78,61.42 62.09,61.05L62.09,61.05C65.28,57.28 68.35,53.48 71.22,49.55L71.22,49.55C74.07,45.64 76.69,41.61 79.25,37.56L79.25,37.56C79.5,37.17 80.02,37 80.42,37.18L80.42,37.18C81.27,37.59 82.1,38.01 82.95,38.41L82.95,38.41C83.36,38.6 83.48,39.08 83.24,39.47L83.24,39.47C80.67,43.52 78.05,47.57 75.19,51.49L75.19,51.49C72.32,55.42 69.23,59.23 66.04,63L66.04,63C65.83,63.24 65.49,63.37 65.18,63.37L65.18,63.37C65.03,63.37 64.88,63.34 64.75,63.28M71.51,66.63C70.94,66.35 70.37,66.08 69.79,65.79L69.79,65.79C69.54,65.66 69.5,65.35 69.71,65.11L69.71,65.11C74.04,59.98 78.1,54.71 81.94,49.33L81.94,49.33C82.12,49.08 82.49,48.97 82.76,49.1L82.76,49.1C83.33,49.38 83.91,49.66 84.49,49.93L84.49,49.93C84.76,50.06 84.82,50.39 84.64,50.64L84.64,50.64C80.8,56.02 76.72,61.29 72.39,66.43L72.39,66.43C72.24,66.6 72.02,66.69 71.8,66.69L71.8,66.69C71.7,66.69 71.6,66.67 71.51,66.63M38.2,88.37C34.81,86.63 33.3,84.17 33.41,81.53L33.41,81.53C33.52,78.88 35.21,76.02 38.35,73.5L38.35,73.5C41.5,70.98 45.45,69.29 49.56,68.65L49.56,68.65C53.63,68.03 57.83,68.44 61.26,70.15L61.26,70.15C64.69,71.86 66.72,74.54 67.09,77.43L67.09,77.43C67.46,80.32 66.16,83.45 62.99,85.99L62.99,85.99C59.81,88.54 55.28,90.03 50.77,90.42L50.77,90.42C49.94,90.49 49.11,90.53 48.28,90.53L48.28,90.53C44.55,90.53 40.98,89.8 38.2,88.37"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="34.35"
            android:startY="91.45"
            android:endX="79.24"
            android:endY="42.64"
            android:type="linear">
          <item android:offset="0" android:color="#FF64CC50"/>
          <item android:offset="1" android:color="#FF95EB87"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M17.74,101.31L41.95,113.56C54.29,104.87 65.85,95.41 76.93,85.47C66.09,96.22 54.5,105.98 42.15,114.72L17.74,101.31Z"
      android:strokeWidth="1"
      android:fillColor="#1F3F37"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M30.2,111.04L42.55,117.08C51.66,110.87 60.22,104.12 68.45,97.03C60.48,104.66 51.93,111.77 42.76,118.33L30.2,111.04Z"
      android:strokeWidth="1"
      android:fillColor="#1F3F37"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M42.88,58.94C51.1,64.41 70.31,74.13 81.31,78.46L81.85,78L42.88,58.94Z"
      android:strokeWidth="1"
      android:fillColor="#1F3F37"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M55.37,74.01C59.57,76.13 66,79.3 74.64,83.52C74.88,83.63 75.08,83.88 75.23,84.29C75.63,85.75 76.52,88.39 77.9,92.22C78.63,92.01 85.9,85.86 85.65,84.79C85.07,82.25 84.63,80.32 83.9,77.93C83.8,77.58 83.63,77.25 83.4,76.95C83.14,76.63 82.83,76.37 82.45,76.2C76.38,73.78 69.23,70.59 62.94,67.41C56.6,64.21 49.5,60.36 44.7,57.07C44.62,57.06 44.55,57.06 44.47,57.06C42.95,57.06 36,63.68 36.49,64.15C40.42,66.42 46.71,69.7 55.37,74.01Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="60.95"
          android:startY="57.06"
          android:endX="60.95"
          android:endY="92.22"
          android:type="linear">
        <item android:offset="0" android:color="#FFFC924F"/>
        <item android:offset="1" android:color="#FFF75927"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M83.28,78.47C82.85,78.93 82.12,79.12 81.72,78.92L81.72,78.92C81.69,78.9 81.67,78.89 81.65,78.88L81.65,78.88C81.59,78.84 81.54,78.79 81.51,78.73L81.51,78.73C81.53,78.78 81.55,78.83 81.57,78.9L81.57,78.9C81.58,78.94 81.59,78.98 81.61,79.02L81.61,79.02C81.61,79.04 81.62,79.06 81.62,79.08L81.62,79.08C81.98,80.37 82.34,81.85 82.69,83.3L82.69,83.3C82.91,84.22 83.12,85.13 83.32,85.98L83.32,85.98C83.33,86.06 83.35,86.15 83.37,86.23L83.37,86.23C83.43,86.47 83.49,86.67 83.58,86.82L83.58,86.82C83.72,87.03 83.93,87.11 84.29,87.01L84.29,87.01C85,86.8 85.84,85.91 85.67,84.88L85.67,84.88C85.67,84.85 85.66,84.82 85.65,84.79L85.65,84.79C85.44,83.86 85.24,83.01 85.05,82.2L85.05,82.2C84.73,80.89 84.42,79.65 84.01,78.29L84.01,78.29C83.98,78.17 83.94,78.05 83.9,77.93L83.9,77.93C83.82,77.66 83.7,77.4 83.54,77.15L83.54,77.15C83.75,77.69 83.59,78.15 83.28,78.47"/>
  </group>
</vector>
