<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="150dp"
    android:height="150dp"
    android:viewportWidth="150"
    android:viewportHeight="150">
  <group>
    <clip-path
        android:pathData="M83.18,26.1C64.58,55.81 42.9,83.13 18,107.94L18,107.94L66.72,124C91.39,98.46 112.68,70.18 131.47,39.88L131.47,39.88L83.18,26.1Z"/>
    <path
        android:pathData="M83.18,26.1C64.58,55.81 42.9,83.13 18,107.94L18,107.94L66.72,124C91.39,98.46 112.68,70.18 131.47,39.88L131.47,39.88L83.18,26.1Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="59.75"
            android:startY="110.19"
            android:endX="128.49"
            android:endY="-54.88"
            android:type="linear">
          <item android:offset="0" android:color="#FF2F9531"/>
          <item android:offset="1" android:color="#FF50B850"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M80.95,36.05C73.42,47.23 65.61,58.42 57.02,69.18L57.02,69.18C48.3,80.1 38.88,90.6 29.19,101.11L29.19,101.11C32.6,102.21 33.23,105.37 30.59,108.12L30.59,108.12C35.36,109.68 40.13,111.24 44.89,112.8L44.89,112.8C49.65,114.36 54.42,115.93 59.19,117.48L59.19,117.48C61.85,114.69 66.74,113.3 70.15,114.4L70.15,114.4C79.79,103.65 89.11,92.86 97.68,81.68L97.68,81.68C106.15,70.62 113.9,59.2 121.44,47.8L121.44,47.8C118.09,46.82 116.92,43.55 118.82,40.53L118.82,40.53C114.11,39.18 109.38,37.83 104.66,36.48L104.66,36.48C99.96,35.13 95.24,33.76 90.52,32.41L90.52,32.41C88.63,35.39 84.31,37.04 80.95,36.05M77.8,70.83C76.74,70.51 75.69,70.19 74.64,69.86L74.64,69.86C74.16,69.71 74.02,69.26 74.34,68.84L74.34,68.84C77.67,64.41 80.98,59.99 84.15,55.52L84.15,55.52C87.32,51.06 90.35,46.56 93.36,42.03L93.36,42.03C93.65,41.6 94.28,41.34 94.78,41.49L94.78,41.49C95.83,41.79 96.87,42.1 97.92,42.41L97.92,42.41C98.41,42.55 98.58,43.02 98.29,43.46L98.29,43.46C95.28,47.99 92.24,52.53 89.08,57L89.08,57C85.91,61.48 82.62,65.92 79.28,70.34L79.28,70.34C79.03,70.68 78.56,70.88 78.13,70.88L78.13,70.88C78.02,70.88 77.9,70.86 77.8,70.83M86.26,73.43C85.54,73.22 84.84,72.99 84.12,72.77L84.12,72.77C83.79,72.67 83.7,72.35 83.91,72.07L83.91,72.07C88.42,66.03 92.82,59.96 97.08,53.85L97.08,53.85C97.28,53.56 97.72,53.39 98.05,53.48L98.05,53.48C98.76,53.7 99.48,53.9 100.2,54.11L100.2,54.11C100.52,54.21 100.62,54.54 100.42,54.83L100.42,54.83C96.16,60.95 91.79,67.06 87.28,73.08L87.28,73.08C87.11,73.32 86.78,73.46 86.49,73.46L86.49,73.46C86.41,73.46 86.33,73.45 86.26,73.43M54.41,102.62C50.06,101.22 47.32,98.65 46.37,95.61L46.37,95.61C45.43,92.56 46.3,89.04 49.13,85.76L49.13,85.76C51.96,82.49 56.22,80.05 60.83,78.77L60.83,78.77C65.45,77.48 70.45,77.32 74.79,78.66L74.79,78.66C79.12,80 81.97,82.58 83.04,85.67L83.04,85.67C84.11,88.75 83.4,92.36 80.6,95.69L80.6,95.69C77.8,99.03 73.4,101.5 68.65,102.74L68.65,102.74C66.31,103.35 63.87,103.67 61.48,103.67L61.48,103.67C59.02,103.67 56.62,103.33 54.41,102.62"/>
    <path
        android:pathData="M80.95,36.05C73.42,47.23 65.61,58.42 57.02,69.18L57.02,69.18C48.3,80.1 38.88,90.6 29.19,101.11L29.19,101.11C32.6,102.21 33.23,105.37 30.59,108.12L30.59,108.12C35.36,109.68 40.13,111.24 44.89,112.8L44.89,112.8C49.65,114.36 54.42,115.93 59.19,117.48L59.19,117.48C61.85,114.69 66.74,113.3 70.15,114.4L70.15,114.4C79.79,103.65 89.11,92.86 97.68,81.68L97.68,81.68C106.15,70.62 113.9,59.2 121.44,47.8L121.44,47.8C118.09,46.82 116.92,43.55 118.82,40.53L118.82,40.53C114.11,39.18 109.38,37.83 104.66,36.48L104.66,36.48C99.96,35.13 95.24,33.76 90.52,32.41L90.52,32.41C88.63,35.39 84.31,37.04 80.95,36.05M77.8,70.83C76.74,70.51 75.69,70.19 74.64,69.86L74.64,69.86C74.16,69.71 74.02,69.26 74.34,68.84L74.34,68.84C77.67,64.41 80.98,59.99 84.15,55.52L84.15,55.52C87.32,51.06 90.35,46.56 93.36,42.03L93.36,42.03C93.65,41.6 94.28,41.34 94.78,41.49L94.78,41.49C95.83,41.79 96.87,42.1 97.92,42.41L97.92,42.41C98.41,42.55 98.58,43.02 98.29,43.46L98.29,43.46C95.28,47.99 92.24,52.53 89.08,57L89.08,57C85.91,61.48 82.62,65.92 79.28,70.34L79.28,70.34C79.03,70.68 78.56,70.88 78.13,70.88L78.13,70.88C78.02,70.88 77.9,70.86 77.8,70.83M86.26,73.43C85.54,73.22 84.84,72.99 84.12,72.77L84.12,72.77C83.79,72.67 83.7,72.35 83.91,72.07L83.91,72.07C88.42,66.03 92.82,59.96 97.08,53.85L97.08,53.85C97.28,53.56 97.72,53.39 98.05,53.48L98.05,53.48C98.76,53.7 99.48,53.9 100.2,54.11L100.2,54.11C100.52,54.21 100.62,54.54 100.42,54.83L100.42,54.83C96.16,60.95 91.79,67.06 87.28,73.08L87.28,73.08C87.11,73.32 86.78,73.46 86.49,73.46L86.49,73.46C86.41,73.46 86.33,73.45 86.26,73.43M54.41,102.62C50.06,101.22 47.32,98.65 46.37,95.61L46.37,95.61C45.43,92.56 46.3,89.04 49.13,85.76L49.13,85.76C51.96,82.49 56.22,80.05 60.83,78.77L60.83,78.77C65.45,77.48 70.45,77.32 74.79,78.66L74.79,78.66C79.13,80 81.97,82.58 83.04,85.67L83.04,85.67C84.11,88.75 83.4,92.36 80.6,95.69L80.6,95.69C77.8,99.03 73.4,101.5 68.65,102.74L68.65,102.74C66.31,103.35 63.87,103.67 61.48,103.67L61.48,103.67C59.02,103.67 56.62,103.33 54.41,102.62"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="56.93"
            android:startY="110.79"
            android:endX="128.03"
            android:endY="-29.43"
            android:type="linear">
          <item android:offset="0" android:color="#FF64CC50"/>
          <item android:offset="1" android:color="#FF95EB87"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
