<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="15dp"
    android:paddingTop="6dp"
    android:paddingRight="15dp"
    android:paddingBottom="6dp"
    tools:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_active_step_track_notification_expand_item"
            android:orientation="horizontal"
            android:paddingStart="10dp"
            android:paddingTop="4dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp">

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_fixed_noti_steps" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="end"
                android:orientation="vertical">


                <TextView
                    android:id="@+id/tv_steps_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="#081C31"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    tools:text="123" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="steps"
                    android:textColor="#081C31"
                    android:textSize="11sp"
                    android:textStyle="bold" />


            </LinearLayout>


        </RelativeLayout>


        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:background="@drawable/bg_active_step_track_notification_expand_item"
            android:paddingStart="10dp"
            android:paddingTop="4dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp">

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_fixed_noti_kcal" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="end"
                android:orientation="vertical">


                <TextView
                    android:id="@+id/tv_kcal_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="#081C31"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    tools:text="123" />


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="kcal"
                    android:textColor="#081C31"
                    android:textSize="11sp"
                    android:textStyle="bold" />

            </LinearLayout>


        </RelativeLayout>

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_active_step_track_notification_expand_item"
            android:paddingStart="10dp"
            android:paddingTop="4dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp">

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_fixed_noti_distance" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_distance_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="#081C31"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    tools:text="123" />

                <TextView
                    android:id="@+id/tv_distance_mus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="distance"
                    android:textColor="#081C31"
                    android:textSize="11sp"
                    android:textStyle="bold" />

            </LinearLayout>


        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/ll_pin"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:background="@drawable/bg_active_step_track_notification_expand_item"
            android:paddingStart="10dp"
            android:paddingTop="4dp"
            android:paddingEnd="10dp"
            android:paddingBottom="4dp">


            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_fixed_noti_duration" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_duration_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="#081C31"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    tools:text="123" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="h"
                    android:textColor="#081C31"
                    android:textSize="11sp"
                    android:textStyle="bold" />

            </LinearLayout>


        </RelativeLayout>


    </LinearLayout>


</LinearLayout>