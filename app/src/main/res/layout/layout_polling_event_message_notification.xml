<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="50dp"
    android:orientation="vertical"
    tools:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="10dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_noti_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/gabarito_medium"
                android:lines="1"
                android:textColor="#FF081C32"
                android:textSize="14sp"
                tools:text="🎊Congratulations to......" />

            <TextView
                android:id="@+id/tv_noti_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:fontFamily="@font/gabarito_regular"
                android:lines="1"
                android:textColor="#FF081C32"
                android:textSize="13sp"
                tools:text="You are so lucky and...... \nlalala \nlalala" />

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_noti_image"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:background="#FFF" />

        <ImageView
            android:id="@+id/iv_noti_close"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="top"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_round_close"
            android:tint="#FFB0B5BC"
            tools:ignore="UseAppTint" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_go_btn_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="6dp"
        android:background="@drawable/bg_for_layout"
        android:visibility="gone">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            android:fontFamily="@font/gabarito_medium"
            android:gravity="center"
            android:text="GO"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </FrameLayout>


</LinearLayout>