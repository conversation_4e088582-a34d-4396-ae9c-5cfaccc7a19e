<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginBottom="6dp"
    app:cardBackgroundColor="#000"
    app:cardCornerRadius="1000dp">

    <TextView
        android:id="@+id/tvContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="4dp"
        android:layout_marginVertical="2dp"
        android:fontFamily="@font/gabarito_bold"
        android:gravity="center"
        android:paddingHorizontal="7dp"
        android:paddingVertical="2.5dp"
        android:textColor="#fff"
        android:textSize="13sp"
        tools:text="9999" />
</androidx.cardview.widget.CardView>