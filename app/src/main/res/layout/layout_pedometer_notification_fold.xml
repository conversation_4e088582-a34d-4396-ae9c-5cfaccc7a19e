<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:minHeight="50dp"
    android:orientation="horizontal"
    android:paddingTop="5dp"
    android:paddingBottom="5dp"
    tools:background="@color/white">

    <LinearLayout
        android:id="@+id/ll_steps"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_steps"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_fixed_noti_steps" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_steps_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF081C32"
                android:textSize="11sp"
                android:textStyle="bold"
                tools:text="9999999" />

            <TextView
                android:id="@+id/tv_steps_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="steps"
                android:textColor="#FF081C32"
                android:textSize="9sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_kcal"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_kcal"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_fixed_noti_kcal" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_kcal_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF081C32"
                android:textSize="11sp"
                android:textStyle="bold"
                tools:text="9999999" />

            <TextView
                android:id="@+id/tv_kcal_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="kcal"
                android:textColor="#FF081C32"
                android:textSize="9sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_distance"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_distance"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_fixed_noti_distance" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_distance_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF081C32"
                android:textSize="11sp"
                android:textStyle="bold"
                tools:text="9999999" />

            <TextView
                android:id="@+id/tv_distance_mus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="dist"
                android:textColor="#FF081C32"
                android:textSize="9sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_duration"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_duration"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_fixed_noti_duration" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_duration_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF081C32"
                android:textSize="11sp"
                android:textStyle="bold"
                tools:text="9999999" />

            <TextView
                android:id="@+id/tv_duration_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="h"
                android:textColor="#FF081C32"
                android:textSize="9sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>


</LinearLayout>