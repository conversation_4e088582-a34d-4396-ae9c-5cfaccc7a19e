package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcStatStep: ImageVector
    get() {
        if (_icStatStep != null) {
            return _icStatStep!!
        }
        _icStatStep = Builder(name = "IcStatStep", defaultWidth = 30.0.dp, defaultHeight = 30.0.dp,
                viewportWidth = 30.0f, viewportHeight = 30.0f).apply {
            path(fill = SolidColor(Color(0xFFFFFFFF)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(3.2445f, 23.0881f)
                lineTo(9.1313f, 23.485f)
                curveTo(9.1313f, 23.485f, 9.785f, 29.816f, 5.2497f, 28.9116f)
                curveTo(1.9676f, 28.2576f, 3.2445f, 23.0881f, 3.2445f, 23.0881f)
                lineTo(3.2445f, 23.0881f)
                close()
                moveTo(25.7888f, 20.1178f)
                curveTo(25.7888f, 20.1178f, 26.832f, 25.424f, 23.5161f, 25.9673f)
                curveTo(18.9334f, 26.7156f, 19.875f, 20.3f, 19.875f, 20.3f)
                lineTo(19.875f, 20.3f)
                close()
                moveTo(3.187f, 10.7807f)
                curveTo(3.4715f, 9.6192f, 5.4156f, 4.2089f, 9.7375f, 5.0711f)
                curveTo(14.9164f, 6.1024f, 12.857f, 13.4875f, 12.857f, 13.4875f)
                curveTo(12.0103f, 16.9718f, 10.2287f, 20.7587f, 10.2287f, 20.7587f)
                lineTo(10.2287f, 20.7587f)
                lineTo(3.4816f, 20.1926f)
                curveTo(4.0574f, 17.8242f, 2.4824f, 13.6859f, 3.187f, 10.7807f)
                close()
                moveTo(20.5287f, 2.0617f)
                curveTo(24.9319f, 1.359f, 26.6491f, 6.8637f, 26.8829f, 8.0414f)
                curveTo(27.462f, 10.9824f, 25.6804f, 15.0816f, 26.1546f, 17.4793f)
                lineTo(26.1546f, 17.4793f)
                lineTo(19.3128f, 17.7949f)
                curveTo(19.3128f, 17.7949f, 17.687f, 13.9267f, 16.9926f, 10.3968f)
                curveTo(16.9926f, 10.3968f, 15.2483f, 2.9043f, 20.5287f, 2.0617f)
                close()
            }
        }
        .build()
        return _icStatStep!!
    }

private var _icStatStep: ImageVector? = null
