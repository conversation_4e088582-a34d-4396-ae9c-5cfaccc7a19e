package dev.step.app.ui.screen.game2

import dev.step.app.androidplatform.biz.game.GameADT


sealed interface Game2SideEffect {

    data class ToNoRemainingTimeDialog(val gameADT: GameADT) : Game2SideEffect

    data class ToRewardedDialog(
        val gameADT: GameADT,
        val coins: Int,
        val times: Int,
    ) : Game2SideEffect

    data class DoPlay(val selectionIndexes: List<Int>) : Game2SideEffect
}