package dev.step.app.ui.screen.redeemedcash

import androidx.compose.ui.text.input.TextFieldValue

data class RedeemedCashViewState(
    val amount: Int = 0,
    val accountAddress: TextFieldValue = TextFieldValue(),
    val currencyText: String = "$",
    val currencyTextPlacementIsStart: Boolean = true,
    val cashText: String = "$0"
) {
    companion object {
        val Empty = RedeemedCashViewState()
    }
}
