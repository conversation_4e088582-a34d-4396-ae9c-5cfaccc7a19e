package dev.step.app.ui.common.reportschart

import android.text.format.DateFormat
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.material.icons.rounded.KeyboardArrowRight
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalContext
import dev.step.app.androidplatform.ext.scale
import dev.step.app.androidplatform.ext.time.*
import dev.step.app.androidplatform.stepsToDistance
import dev.step.app.androidplatform.stepsToDurationSeconds
import dev.step.app.androidplatform.stepsToKcal
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.pojo.*
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.screen.reports.ReportsStatisticsMode
import dev.step.app.ui.theme.*
import kotlinx.datetime.*
import kotlin.time.DurationUnit
import kotlin.time.toDuration

object XAxisHourValueFormatter : ValueFormatter() {

    override fun getFormattedValue(value: Float): String {
        val ins = Instant.fromEpochSeconds(value.toLong())

        val ldt = ins.toLocalDateTime(TimeZone.currentSystemDefault())
        debugLog("XAxisHourValueFormatter ldt -> $ldt")

        val h = if (ldt.hour % 2 == 0) ldt.hour else ldt.hour + 1

        val is24HourFormat = DateFormat.is24HourFormat(globalContext)
        return if (is24HourFormat) {
            "$h:00"
        } else {
            if (h == 0) {
                "12 AM"
            } else if (h > 12) {
                "${h - 12} PM"
            } else {
                "$h AM"
            }
        }

    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun DayReportsChart(
    dayReportsData: DayReportsData,
    stepLengthCm: Float,
    weightKg: Float,
    rsm: ReportsStatisticsMode,
    mus: MeasurementUnit,
    onDayReportChange: (Instant) -> Unit,
    modifier: Modifier = Modifier,
) {
    val totalSteps = dayReportsData.stepsTrackRecords.sumOf { it.steps }
    val avgSteps =
        if (dayReportsData.stepsTrackRecords.isEmpty()) 0 else (totalSteps / dayReportsData.stepsTrackRecords.size)
    val yMaxSteps = ((dayReportsData.stepsTrackRecords.maxOfOrNull { it.steps }) ?: 0)
    val yOffsetSteps = yMaxSteps.toString().length * 20

    val (total, avg) = when (rsm) {
        ReportsStatisticsMode.Calories -> stepsToKcal(totalSteps, weightKg) to stepsToKcal(
            avgSteps,
            weightKg
        )

        ReportsStatisticsMode.Distance -> stepsToDistance(
            totalSteps,
            stepLengthCm,
            mus
        ) to stepsToDistance(avgSteps, stepLengthCm, mus)

        ReportsStatisticsMode.Steps -> totalSteps.toFloat() to avgSteps.toFloat()
        ReportsStatisticsMode.Time -> stepsToDurationSeconds(totalSteps) to stepsToDurationSeconds(
            avgSteps
        )
    }

    val (yMax, yOffset) = when (rsm) {
        ReportsStatisticsMode.Calories -> stepsToKcal(yMaxSteps, weightKg) to stepsToKcal(
            yOffsetSteps,
            weightKg
        ) + 0.1f

        ReportsStatisticsMode.Distance -> stepsToDistance(
            yMaxSteps,
            stepLengthCm,
            mus
        ) to stepsToDistance(yOffsetSteps, stepLengthCm, mus)

        ReportsStatisticsMode.Steps -> yMaxSteps.toFloat() to yOffsetSteps.toFloat()
        ReportsStatisticsMode.Time -> stepsToDurationSeconds(yMaxSteps) to stepsToDurationSeconds(
            yOffsetSteps
        )
    }

    val lineChartPoints = dayReportsData.stepsTrackRecords.map {
        val yValue = when (rsm) {
            ReportsStatisticsMode.Calories -> stepsToKcal(it.steps, weightKg)
            ReportsStatisticsMode.Distance -> stepsToDistance(it.steps, stepLengthCm, mus)
            ReportsStatisticsMode.Steps -> it.steps.toFloat()
            ReportsStatisticsMode.Time -> stepsToDurationSeconds(it.steps)
        }

        Entry(it.instant.epochSeconds.toFloat(), yValue).apply {
            debugLog("DRC point entry: $this")
        }
    }

    val dataSet = LineDataSet(lineChartPoints, "").apply {
        mode = LineDataSet.Mode.HORIZONTAL_BEZIER
        setDrawCircles(false)
        color = AppColor.Primary.toArgb()
        lineWidth = 2.5f
    }

    val chartData = LineData(dataSet).apply {
        setDrawValues(false)
    }

    var lineChart by remember { mutableStateOf<LineChart?>(null) }
    lineChart?.apply {
        axisLeft.apply {
            valueFormatter = when (rsm) {
                ReportsStatisticsMode.Time -> YAxisDurationValueFormatter
                ReportsStatisticsMode.Steps -> YAxisIntValueFormatter
                else -> YAxisFloatValueFormatter
            }
            axisMaximum = yMax + yOffset
            axisMinimum = if (axisMaximum >= 1) -(axisMaximum * 0.01f) else -0.001f

            debugLog("DRC axisLeft.axisMaximum -> $axisMaximum")
        }

        xAxis.apply {
            axisMinimum =
                dayReportsData.instant.todayStartInstant().epochSeconds.toFloat()
            axisMaximum = axisMinimum + 86400

            debugLog("DRC xAxis axisMinimum <- ${dayReportsData.instant.todayStartInstant().epochSeconds.toFloat()}")
            debugLog(
                "DRC xAxis axisMinimumL <- ${
                    dayReportsData.instant.todayStartInstant().epochSeconds.toFloat().toLong()
                }"
            )
            debugLog("DRC xAxis axisMinimum -> $axisMinimum")
            debugLog("DRC xAxis axisMinimumL -> ${axisMinimum.toLong()}")

            debugLog("DRC xAxis axisMaximum -> $axisMaximum")
            debugLog("DRC xAxis axisMaximumL -> ${axisMaximum.toLong()}")
        }

        data = chartData
        notifyDataSetChanged()
        invalidate()
    }

    Box(modifier = modifier) {

        Column(modifier = Modifier.bodyWidth()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.text_day),
                    modifier = Modifier.weight(1f),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                )

                val (totalText, avgText) = when (rsm) {
                    ReportsStatisticsMode.Time -> {
                        Pair(
                            total.toInt().toDuration(DurationUnit.SECONDS).toHHmm(),
                            avg.toInt().toDuration(DurationUnit.SECONDS).toHHmm()
                        )
                    }

                    ReportsStatisticsMode.Steps -> {
                        total.toInt().toString() to avg.toInt().toString()
                    }

                    else -> {
                        total.scale(3).toString() to avg.scale(3).toString()
                    }
                }

                Text(
                    text = "${stringResource(id = R.string.text_total)}: $totalText    " +
                            "${stringResource(id = R.string.text_avg)}: $avgText"
                )
            }

            Column(modifier = Modifier.background(AppColor.PrimaryLightAlpha8)) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    Surface(
                        onClick = {
                            onDayReportChange(
                                dayReportsData.instant.minus(
                                    DateTimePeriod(days = 1),
                                    TimeZone.currentSystemDefault()
                                )
                            )
                        },
                        shape = CircleShape,
                        color = Color.Black,
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.KeyboardArrowLeft,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = Color.White,
                        )
                    }


                    val ldt =
                        dayReportsData.instant.toLocalDateTime(TimeZone.currentSystemDefault())

                    val ldtText = ldt.monthAndDayDisplayName()

                    Text(
                        text = ldtText,
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Bold,
                    )

                    Surface(
                        onClick = {
                            onDayReportChange(
                                dayReportsData.instant.plus(
                                    DateTimePeriod(days = 1),
                                    TimeZone.currentSystemDefault()
                                )
                            )
                        },
                        shape = CircleShape,
                        color = Color.Black,
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.KeyboardArrowRight,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = Color.White,
                        )
                    }
                }

                AndroidView(
                    factory = {
                        LineChart(it).apply {
                            basicConfigure(rsm, yMax, yOffset)

                            xAxis.apply {
                                valueFormatter = XAxisHourValueFormatter
                                setLabelCount(7, true)

                                axisMinimum =
                                    dayReportsData.instant.todayStartInstant().epochSeconds.toFloat()
                                axisMaximum = axisMinimum + 86400
                            }

                            data = chartData

                            invalidate()
                            lineChart = this
                        }
                    }, modifier = Modifier
                        .height(200.dp)
                        .padding(horizontal = 4.dp)
                )
            }
        }
    }
}

@Preview
@Composable
fun DayReportsChartPreview() {
    val tz = TimeZone.currentSystemDefault()
    val now = nowInstant().lessMinutesAndSeconds(tz)
    DayReportsChart(
        dayReportsData = DayReportsData(
            now,
            listOf(
                StepsTrackRecord(
                    LocalDateTime(
                        year = 2022,
                        monthNumber = 10,
                        dayOfMonth = 31,
                        hour = 20,
                        minute = 0
                    ).toInstant(tz), 60
                ),
                StepsTrackRecord(
                    LocalDateTime(
                        year = 2022,
                        monthNumber = 10,
                        dayOfMonth = 31,
                        hour = 21,
                        minute = 0
                    ).toInstant(tz), 200
                ),
                StepsTrackRecord(
                    LocalDateTime(
                        year = 2022,
                        monthNumber = 10,
                        dayOfMonth = 31,
                        hour = 22,
                        minute = 0
                    ).toInstant(tz), 240
                ),
                StepsTrackRecord(
                    LocalDateTime(
                        year = 2022,
                        monthNumber = 10,
                        dayOfMonth = 31,
                        hour = 23,
                        minute = 0
                    ).toInstant(tz), 1000
                ),
            )
        ),
        2f,
        3f,
        rsm = ReportsStatisticsMode.Distance,
        mus = MeasurementUnit.Imperial,
        onDayReportChange = {})
}
