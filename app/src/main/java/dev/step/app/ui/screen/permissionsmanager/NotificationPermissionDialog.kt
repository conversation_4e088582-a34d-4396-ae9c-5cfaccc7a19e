package dev.step.app.ui.screen.permissionsmanager

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import dev.step.app.R

@Composable
internal fun NotificationPermissionDialog(
    onDismiss: () -> Unit,
    onRequest: () -> Unit,
) {
    PermissionsDialog(
        onDismiss = onDismiss,
        onRequest = onRequest,
        mainPainter = painterResource(R.drawable.img_permissions_notification),
        title = stringResource(R.string.allow_notification),
        content1 = stringResource(R.string.notify_when_your_coin_amount_changes),
        content2 = stringResource(R.string.you_can_disable_it_anytime)
    )
}