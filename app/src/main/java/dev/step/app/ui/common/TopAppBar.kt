package dev.step.app.ui.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.theme.AppColor

@Composable
fun DefTopAppBar(
    pop: () -> Unit,
    title: String,
    modifier: Modifier = Modifier,
    elevation: Dp = 0.dp
) {
    val context = LocalContext.current

    Surface(color = Color.White, elevation = elevation, modifier = modifier) {
        Column {
            BlankSpacer(height = context.statusBarHeight)
            TopAppBar(
                title = {
                    Text(
                        text = title,
                        modifier = Modifier.fillMaxWidth(),
                        color = AppColor.TextColorBlack,
                    )
                },
                navigationIcon = {
                    IconButton(onClick = pop) {
                        Icon(
                            imageVector = Icons.Rounded.KeyboardArrowLeft,
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            tint = AppColor.TextColorBlack
                        )
                    }
                },
                backgroundColor = Color.Transparent,
                elevation = 0.dp
            )
        }
    }
}