package dev.step.app.ui.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.bodyWidth
import dev.step.app.ui.theme.noRippleClickable

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun FancyFadedBgButton(
    startColor: Color,
    endColor: Color,
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        shape = CircleShape,
    ) {
        Box(
            modifier = Modifier
                .background(Brush.horizontalGradient(listOf(startColor, endColor)))
                .bodyWidth()
        ) {
            Text(
                text = text,
                modifier = Modifier.padding(10.dp),
                color = Color.White,
                fontWeight = FontWeight.Bold,
                fontSize = 16.sp
            )
        }
    }
}

@Composable
fun FancyFadedBgButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    iconPainter: Painter? = null,
    bgBrush: Brush = AppColor.FadedPrimaryBrushVertical,
    fontSize: TextUnit = 16.sp,
) {
    Surface(
        modifier = modifier,
        shape = CircleShape,
    ) {
        Row(
            modifier = Modifier
                .noRippleClickable { onClick() }
                .background(bgBrush),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (iconPainter != null) {
                Image(
                    painter = iconPainter,
                    contentDescription = null,
                    modifier = Modifier.size(26.dp)
                )
                BlankSpacer(width = 8.dp)
            }

            Text(
                text = text,
                modifier = Modifier.padding(vertical = 8.dp),
                color = Color.White,
                fontWeight = FontWeight.Bold,
                fontSize = fontSize
            )
        }
    }
}

@Preview
@Composable
private fun FancyFadedBgButtonPreview() {
    FancyFadedBgButton(
        text = "lalalala",
        onClick = {},
        iconPainter = painterResource(id = R.drawable.img_bigmoji_like)
    )
}
