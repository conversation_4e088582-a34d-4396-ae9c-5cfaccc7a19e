package dev.step.app.ui.screen.withdraw

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Lock
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import dev.step.app.R
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import dev.step.app.ui.theme.noRippleClickable

@OptIn(ExperimentalMaterialApi::class)
@Composable
internal fun RedeemedItem(
    coins: Int,
    onRedeemed: () -> Unit,
    content: @Composable BoxScope.() -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier.noRippleClickable(onRedeemed)) {
        Surface(
            color = AppColor.PrimaryLightAlpha8,
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(bottom = 22.dp)
        ) {
            Box(modifier = Modifier.fillMaxWidth()) {
                content(this)
            }
        }

        Surface(
            onClick = onRedeemed,
            shape = CircleShape,
            modifier = Modifier.align(Alignment.BottomCenter)
        ) {
            Row(
                modifier = Modifier.background(AppColor.FadedPrimaryBrushVertical),
                verticalAlignment = Alignment.CenterVertically
            ) {
                BlankSpacer(width = 10.dp)
                Image(
                    painter = painterResource(id = R.drawable.ic_video),
                    contentDescription = null,
                    modifier = Modifier.size(17.dp)
                )
                BlankSpacer(width = 4.dp)
                Image(
                    painter = painterResource(id = R.drawable.ic_coin),
                    contentDescription = null,
                    modifier = Modifier
                        .size(17.dp)
                        .padding(top = 2.dp)
                )

                BlankSpacer(width = 4.dp)

                Text(
                    text = coins.toString(),
                    modifier = Modifier.padding(vertical = 5.dp),
                    color = Color.White,
                    fontSize = 13.sp
                )

                BlankSpacer(width = 12.dp)
            }
        }
    }
}

@Composable
fun RedeemedCouponItem(
    currency: String,
    currencyPlacementIsStart: Boolean,
    coupon: RedeemedPrize.Coupon,
    onRedeemed: (coupon: RedeemedPrize.Coupon) -> Unit,
    modifier: Modifier = Modifier
) {
    RedeemedItem(
        coins = coupon.coinPrice,
        onRedeemed = { onRedeemed(coupon) },
        content = {
            Box(
                modifier = Modifier
                    .padding(top = 14.dp, bottom = 32.dp)
                    .align(Alignment.Center)
            ) {
                Image(
                    painter = painterResource(id = R.drawable.img_coupon),
                    contentDescription = null,
                    modifier = Modifier.width(134.dp)
                )

                Column(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(end = 24.dp, bottom = 4.dp)
                ) {
                    val offText = buildAnnotatedString {
                        withStyle(
                            MaterialTheme.typography.h6.copy(
                                color = AppColor.Primary
                            ).toSpanStyle()
                        ) {
                            append("${coupon.off} ")
                        }

                        withStyle(
                            MaterialTheme.typography.body2.copy(
                                color = AppColor.Primary,
                                fontSize = 14.sp
                            ).toSpanStyle()
                        ) {
                            append("%")
                        }
                    }

                    Text(text = offText)


                    Text(
                        text = stringResource(R.string.your_entire_purchase),
                        fontSize = 9.sp,
                        color = AppColor.TextColorGray.copy(alpha = .5f)
                    )
                }

            }
        },
        modifier = modifier,
    )
}

@Composable
fun RedeemedCashItem(
    sid: Int,
    currency: String,
    currencyPlacementIsStart: Boolean,
    cash: RedeemedPrize.Cash,
    onRedeemed: (cash: RedeemedPrize.Cash) -> Unit,
    modifier: Modifier = Modifier
) {
    RedeemedItem(
        coins = cash.coinPrice,
        onRedeemed = { onRedeemed(cash) },
        content = {
            Column(
                modifier = Modifier
                    .bodyWidth()
                    .padding(top = 8.dp, bottom = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val amountText = if (currencyPlacementIsStart)
                    "$currency ${cash.amount}"
                else
                    "${cash.amount} $currency"

                Text(text = amountText, fontSize = 18.sp, color = AppColor.Primary)


                val cashPainter = when (sid) {
                    1 -> painterResource(id = R.drawable.img_cash_1)
                    2 -> painterResource(id = R.drawable.img_cash_2)
                    3 -> painterResource(id = R.drawable.img_cash_3)
                    4 -> painterResource(id = R.drawable.img_cash_4)
                    else -> painterResource(id = R.drawable.img_cash_1)
                }

                Image(
                    painter = cashPainter,
                    contentDescription = null,
                    modifier = Modifier.size(56.dp),
                )
            }
        },
        modifier = modifier,
    )
}

@OptIn(ExperimentalLayoutApi::class)
@Preview
@Composable
private fun RedeemedItemPreview() {
    AppTheme {
        FlowRow(maxItemsInEachRow = 2) {
            RedeemedCouponItem(
                currency = "$",
                currencyPlacementIsStart = true,
                coupon = RedeemedPrize.Coupon(100000, 10),
                onRedeemed = {},
                modifier = Modifier
                    .weight(1f)
                    .padding(6.dp)
            )
            RedeemedCouponItem(
                currency = "元",
                currencyPlacementIsStart = false,
                coupon = RedeemedPrize.Coupon(100000, 10),
                onRedeemed = {},
                modifier = Modifier
                    .weight(1f)
                    .padding(6.dp)
            )
            RedeemedCashItem(
                sid = 1,
                currency = "¥",
                currencyPlacementIsStart = true,
                cash = RedeemedPrize.Cash(10000, 20),
                onRedeemed = {},
                modifier = Modifier
                    .weight(1f)
                    .padding(6.dp)
            )
            RedeemedCashItem(
                sid = 2,
                currency = "BTC",
                currencyPlacementIsStart = false,
                cash = RedeemedPrize.Cash(10000, 20),
                onRedeemed = {},
                modifier = Modifier
                    .weight(1f)
                    .padding(6.dp)
            )
        }
    }
}