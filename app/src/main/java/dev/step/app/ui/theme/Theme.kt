package dev.step.app.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material.MaterialTheme
import androidx.compose.material.lightColors
import androidx.compose.runtime.Composable

private val LightColorPalette = lightColors(
    primary = AppColor.Primary,
    primaryVariant = AppColor.PrimaryLight,
    secondary = AppColor.BackgroundDefault,

    background = AppColor.BackgroundDefault,
    surface = AppColor.BackgroundDefault,
    onPrimary = AppColor.TextColorBlack,
    onSecondary = AppColor.TextColorBlack,
    onBackground = AppColor.TextColorBlack,
    onSurface = AppColor.TextColorBlack,
)

@Composable
fun AppTheme(darkTheme: Boolean = isSystemInDarkTheme(), content: @Composable () -> Unit) {
    val colors = if (darkTheme) {
        LightColorPalette
    } else {
        LightColorPalette
    }

    MaterialTheme(
        colors = colors,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}
