package dev.step.app.ui.dialog.weightchange

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
//import dev.step.app.androidplatform.biz.MaxInterstitialAdHelper
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.ui.common.ProfileWeightSettingContentImperial
import dev.step.app.ui.common.ProfileWeightSettingContentMetric
import dev.step.app.ui.common.AppDefDialog
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

@Composable
fun WeightDialog(
    navUp: () -> Unit,
    userSettingsDataKv: UserSettingsDataKv = koinInject(),
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator)

    val scope = rememberCoroutineScope()

    var mus by remember {
        mutableStateOf<MeasurementUnit?>(null)
    }

    var weight by remember { mutableStateOf<Float?>(null) }

    LaunchedEffect(Unit) {
        mus = userSettingsDataKv.measurementUnit
        weight = when (mus) {
            null -> 1f
            MeasurementUnit.Metric -> userSettingsDataKv.bodyWeightKg
            MeasurementUnit.Imperial -> userSettingsDataKv.bodyWeightLb
        }
    }

    AppDefDialog(
        onDismiss = navUp,
        onCancel = navUp,
        onConfirm = {
            scope.launch {
                mus?.let {
                    when (it) {
                        MeasurementUnit.Metric -> userSettingsDataKv.changeBodyWeight(kg = weight, mus = it)
                        MeasurementUnit.Imperial -> userSettingsDataKv.changeBodyWeight(lb = weight, mus = it)
                    }
                }
//                interstitialAdHelper.tryToShowAd(
//                    "save_weight",
//                    onAdShowingOrSkip = { navUp() }
//                )
                OnBack()
            }
        },
    ) {
        when (mus) {
            null -> {}
            MeasurementUnit.Metric -> ProfileWeightSettingContentMetric(
                kg = weight ?: 1f,
                onKgChange = { weight = it },
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 20.dp)
            )
            MeasurementUnit.Imperial -> ProfileWeightSettingContentImperial(
                lb = weight ?: 1f,
                onLbChange = { weight = it },
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 20.dp)
            )

        }
    }
}
