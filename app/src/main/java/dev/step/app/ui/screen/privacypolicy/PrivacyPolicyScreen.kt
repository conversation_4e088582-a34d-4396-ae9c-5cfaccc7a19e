package dev.step.app.ui.screen.privacypolicy

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.web.WebView
import com.google.accompanist.web.rememberWebViewState
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import dev.step.app.R
import dev.step.app.ScreenDestinationNode
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.DefTopAppBar
import dev.step.app.ui.theme.AppColor
import kotlinx.parcelize.Parcelize

@Parcelize
object PrivacyPolicyScreenNode : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        PrivacyPolicyScreen(navigator = navigator)
    }
}

@Composable
fun PrivacyPolicyScreen(
    navigator: Navigator
) {

    val context = LocalContext.current

    Scaffold(
        topBar = {
            DefTopAppBar(
                pop = navigator::pop,
                title = stringResource(id = R.string.text_privacy_policy)
            )
        },
        modifier = Modifier.navigationBarsPadding()
    ) {

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
        ) {
            val webViewState =
                rememberWebViewState(url = "https://sites.google.com/view/privacy-policy-for-e-pedometer/")

            WebView(
                state = webViewState,
                modifier = Modifier.matchParentSize(),
            )

            if (webViewState.isLoading) {
                CircularProgressIndicator(
                    color = AppColor.Primary,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}
