package dev.step.app.ui.common.game.sm

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.AbsListView
import android.widget.BaseAdapter
import android.widget.ListView
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import java.util.*
import kotlin.math.abs

class SlotListView : ListView, AbsListView.OnScrollListener, OnGlobalLayoutListener {
    private var mMaxScrollSpeed = 0
    private val mAdapter = WheelAdapter(context)

    // 每一项高度
    private var itemHeightPixels = 0

    // 记录滚轮当前刻度
    private var mCurrentPosition = -1
    private var mOnWheelChangeListener: OnWheelChangeListener? = null
    private var mListener: OnScrollFinishedListener? = null
    private var mUiFinishedListener: OnScrollUiFinishedListener? = null
    private var mHandler: Handler? = null
    private var mAutoScrollListener: AutoScrollEndListener? = null

    constructor(context: Context?) : super(context) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    private var sizeRatio = 1f
    fun setSizeRatio(ratio: Float) {
        sizeRatio = ratio
    }

    private var mScrollSpeedAnimator: ValueAnimator? = null
    private var mCrtScrollSpeed = 0
    fun start(maxScrollSpeed: Int) {
        mActualDistance = 0
        mMaxScrollSpeed = maxScrollSpeed
        mCrtScrollSpeed = mMaxScrollSpeed
        mHandler?.removeMessages(WHAT_SCROLL)
        mHandler?.sendEmptyMessage(WHAT_SCROLL)
    }

    fun stop(index: Int) {
        post {
            mDistance = -1
            mFinalIndex = index
            mScrollSpeedAnimator?.cancel()
            mScrollSpeedAnimator?.removeUpdateListener(mUpdateListener)
            mScrollSpeedAnimator?.removeAllListeners()
            mScrollSpeedAnimator = null

            mScrollSpeedAnimator = ValueAnimator.ofInt(mMaxScrollSpeed, MIN_SCROLL_SPEED)
            mScrollSpeedAnimator?.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    mHandler?.removeMessages(WHAT_SCROLL)
                    mHandler?.sendEmptyMessage(WHAT_DECREASE)
                }
            })
            mScrollSpeedAnimator?.addUpdateListener(mUpdateListener)
            mScrollSpeedAnimator?.duration = DECREASE_SPEED_DURATION.toLong()
            mScrollSpeedAnimator?.start()
            mHandler?.removeMessages(WHAT_SCROLL)
            mHandler?.sendEmptyMessage(WHAT_SCROLL)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mHandler?.removeCallbacksAndMessages(null)
        mScrollSpeedAnimator?.cancel()
    }

    private val mUpdateListener =
        AnimatorUpdateListener { animation ->
            mCrtScrollSpeed = animation.animatedValue as Int

            debugLog("speed ${this.hashCode()} mCrtScrollSpeed: $mCrtScrollSpeed")
        }

    fun setItems(list: List<Int>?) {
        _setItems(list)
    }

    /**
     * 设置滚轮个数偏移量
     */
    fun setOffset(offset: Int) {
        require(!(offset < 1 || offset > 3)) { "Offset must between 1 and 3" }
        val wheelSize = offset * 2 + 1
        mAdapter.setWheelSize(wheelSize)
    }

    private fun getListViewScrollY(listView: ListView, toIndex: Int): Int {
        val child = listView.getChildAt(0) as SlotMachineItemView
        val top = child.top
        val index = child.data
        val itemHeight = dp2px(context, HEIGHT)
        val distance: Int = if (top >= 0) {
            if (toIndex >= index) {
                top + (toIndex - index) * itemHeight
            } else {
//                top + (10 - index + toIndex) * itemHeight
                top + (5 - index + toIndex) * itemHeight
            }
        } else {
            if (toIndex > index) {
                itemHeight + top + (toIndex - index - 1) * itemHeight
            } else {
//                itemHeight + top + (9 - index + toIndex - 1) * itemHeight
                itemHeight + top + (4 - index + toIndex - 1) * itemHeight
            }
        }
        return distance
    }

    private var mDistance = -1
    private var mFinalIndex = 0

    fun interface AutoScrollEndListener {
        fun onScrollEnd()
    }

    fun setAutoScrollEndListener(listener: AutoScrollEndListener?) {
        mAutoScrollListener = listener
    }

    private var mActualDistance = 0
    private fun init() {
        mHandler = object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                when (msg.what) {
                    WHAT_SCROLL -> {
                        scrollListBy(crtSpeed())
                        sendEmptyMessageDelayed(WHAT_SCROLL, 16)
                    }

                    WHAT_DECREASE -> {
                        val listViewScrollY =
                            getListViewScrollY(this@SlotListView, mFinalIndex)
                        val duration =
                            (listViewScrollY.toFloat() / MIN_SCROLL_SPEED * 16).toInt()
                        smoothScrollBy(listViewScrollY, duration)
                        sendEmptyMessageDelayed(WHAT_END, duration.toLong())
                    }

                    WHAT_END -> {
                        // 校正距离
                        setSelection(mFinalIndex)
                        mAutoScrollListener?.onScrollEnd()
                    }
                }
            }
        }
        mHandler?.sendEmptyMessage(WHAT_SCROLL)
        isVerticalScrollBarEnabled = false
        isScrollingCacheEnabled = false
        cacheColorHint = Color.TRANSPARENT
        setFadingEdgeLength(0)
        overScrollMode = OVER_SCROLL_NEVER
        dividerHeight = 0
        setOnScrollListener(this)
        isNestedScrollingEnabled = true
        if (!isInEditMode) {
            viewTreeObserver.addOnGlobalLayoutListener(this)
        }
        super.setAdapter(mAdapter)
        setOffset(1)
        setCanLoop(true)
        setItems(buildData())
    }

    private fun crtSpeed(): Int {
        return mCrtScrollSpeed
    }

    private fun _setItems(list: List<Int>?) {
        require(!list.isNullOrEmpty()) { "data are empty" }
        //        isUserScroll = false;
        mCurrentPosition = -1
        mAdapter.setData(list)
    }

    private fun setCanLoop(canLoop: Boolean) {
        mAdapter.setLoop(canLoop)
    }

    fun setSelectedIndexBySecond(second: Long) {
        val index = getIndexByMinute((second / 60).toInt())
        setSelectedIndex(index)
    }

    fun setSelectedIndex(index: Int) {
        val realPosition = getRealPosition(index)
        //        WheelListView.super.setSelection(realPosition);
//        refreshCurrentPosition();
        //延时一下以保证数据初始化完成，才定位到选中项
        postDelayed({
            <EMAIL>(realPosition)
            refreshCurrentPosition()
        }, SECTION_DELAY.toLong())
    }

    override fun getSelectedItem(): Int? {
        return mAdapter.getItem(currentPosition)
    }

    /**
     * 获得滚轮当前真实位置
     */
    private fun getRealPosition(position: Int): Int {
        val realCount: Int = mAdapter.realCount
        if (realCount == 0) {
            return 0
        }
        if (mAdapter.isLoop) {
            val d = Int.MAX_VALUE / 2 / realCount
            return position + d * realCount - mAdapter.wheelSize / 2
        }
        return position
    }

    /**
     * 获取当前滚轮位置
     */
    val currentPosition: Int
        get() = if (mCurrentPosition == -1) {
            0
        } else mCurrentPosition

    private fun onSelectedCallback() {
        val index = currentPosition
        val item = selectedItem
        mOnWheelChangeListener?.onItemSelected(index, item)
    }

    private fun obtainSmoothDistance(scrollDistance: Float): Int {
        return if (abs(scrollDistance) <= 2) {
            scrollDistance.toInt()
        } else if (abs(scrollDistance) < 12) {
            if (scrollDistance > 0) 2 else -2
        } else {
            (scrollDistance / 6).toInt() // 减缓平滑滑动速率
        }
    }

    private fun refreshCurrentPosition() {
        if (getChildAt(0) == null || itemHeightPixels == 0) {
            return
        }
        val firstPosition = firstVisiblePosition
        if (mAdapter.isLoop && firstPosition == 0) {
            return
        }
        var position: Int
        position = if (abs(getChildAt(0).y) <= itemHeightPixels / 2) {
            firstPosition
        } else {
            firstPosition + 1
        }
        //由这个逆推：int wheelSize = offset * 2 + 1;
        val offset: Int = (mAdapter.wheelSize - 1) / 2
        val curPosition = position + offset
        refreshVisibleItems(firstPosition, curPosition, offset)
        if (mAdapter.isLoop) {
            position = curPosition % mAdapter.realCount
        }
        if (position == mCurrentPosition) {
            return
        }
        mCurrentPosition = position
        onSelectedCallback()
    }

    private fun refreshVisibleItems(firstPosition: Int, curPosition: Int, offset: Int) {
        for (i in curPosition - offset..curPosition + offset) {
            val itemView = getChildAt(i - firstPosition)
            if (itemView != null && itemView is SlotMachineItemView) {
                refreshTextView(i, curPosition, itemView)
            }
        }
    }

    private fun refreshTextView(position: Int, curPosition: Int, itemView: SlotMachineItemView) {
        itemView.setItemSelected(curPosition == position)
    }

    override fun onGlobalLayout() {
        viewTreeObserver.removeOnGlobalLayoutListener(this)
        val childCount = childCount
        if (childCount > 0 && itemHeightPixels == 0) {
            itemHeightPixels = getChildAt(0).height
            if (itemHeightPixels == 0) {
                return
            }
            val wheelSize: Int = mAdapter.wheelSize
            val params = layoutParams
            params.height = itemHeightPixels * wheelSize
            refreshVisibleItems(
                firstVisiblePosition,
                currentPosition + wheelSize / 2, wheelSize / 2
            )
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val width = dp2px(context, WIDTH)
        val height = dp2px(context, HEIGHT) * 3
        super.onMeasure(
            MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
        )
    }

    override fun onScrollStateChanged(view: AbsListView, scrollState: Int) {
        if (scrollState != OnScrollListener.SCROLL_STATE_IDLE) {
            removeCallbacks(mScrollFinishRunnable)
            return
        }
        val itemView = getChildAt(0) ?: return
        val deltaY = itemView.y
        if (deltaY.toInt() == 0 || itemHeightPixels == 0) {
            return
        }
        if (abs(deltaY) < itemHeightPixels / 2) {
            val d = obtainSmoothDistance(deltaY)
            smoothScrollBy(d, SMOOTH_SCROLL_DURATION)
        } else {
            val d = obtainSmoothDistance(itemHeightPixels + deltaY)
            smoothScrollBy(d, SMOOTH_SCROLL_DURATION)
        }
        removeCallbacks(mScrollFinishRunnable)
        callScrollFinished(getMinuteByIndex(currentPosition))
        postDelayed(mScrollFinishRunnable, SMOOTH_SCROLL_DURATION.toLong())
    }

    private val mScrollFinishRunnable = Runnable { callScrollUiFinished() }
    override fun onScroll(
        view: AbsListView,
        firstVisibleItem: Int,
        visibleItemCount: Int,
        totalItemCount: Int
    ) {
        if (visibleItemCount != 0) {
            refreshCurrentPosition()
        }
    }

    private fun getIndexByMinute(minute: Int): Int {
        return minute / 5 - 1
    }

    private fun getMinuteByIndex(index: Int): Int {
        return (index + 1) * 5
    }

    private fun buildData(): List<Int> {
        return listOf(0, 1, 2, 3, 4)
    }

    fun setOnWheelChangeListener(onWheelChangeListener: OnWheelChangeListener?) {
        mOnWheelChangeListener = onWheelChangeListener
    }

    fun setOnScrollFinishListener(listener: OnScrollFinishedListener?) {
        mListener = listener
    }

    fun setOnScrollUiFinishListener(listener: OnScrollUiFinishedListener?) {
        mUiFinishedListener = listener
    }

    private fun callScrollFinished(crtMinute: Int) {
        debugLog("callScrollFinished")

        mListener?.onScrollFinished(crtMinute)
    }

    private fun callScrollUiFinished() {
        mUiFinishedListener?.onScrollUiFinished()
    }

    fun interface OnScrollFinishedListener {
        /**
         * 时间选中即回调
         * @param crtMinute
         */
        fun onScrollFinished(crtMinute: Int)
    }

    fun interface OnScrollUiFinishedListener {
        /**
         * 时间选中且选中效果消失才回调
         */
        fun onScrollUiFinished()
    }

    interface OnWheelChangeListener {
        /**
         * 滑动选择回调
         *
         * @param index        当前选择项的索引
         * @param item         当前选择项的值
         */
        fun onItemSelected(index: Int, item: Int?)
    }

    private class WheelAdapter(private val mContext: Context) : BaseAdapter() {
        private val data: MutableList<Int> = ArrayList()
        var isLoop = false
            private set
        var wheelSize = 5
            private set
        val realCount: Int
            get() = data.size

        override fun getCount(): Int {
            if (isLoop) {
                return Int.MAX_VALUE
            }
            return if (data.size > 0) data.size + wheelSize - 1 else 0
        }

        override fun getItemId(position: Int): Long {
            return if (isLoop) {
                if (data.size > 0) (position % data.size).toLong() else position.toLong()
            } else position.toLong()
        }

        @Suppress("LocalVariableName")
        override fun getItem(position: Int): Int? {
            var _position = position
            if (isLoop) {
                return if (data.size > 0) data[_position % data.size] else null
            }
            if (data.size <= _position) {
                _position = data.size - 1
            }
            return data[_position]
        }

        override fun areAllItemsEnabled(): Boolean {
            return false
        }

        override fun isEnabled(position: Int): Boolean {
            return false
        }

        @Suppress("LocalVariableName")
        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View? {
            var _position = position
            var _convertView = convertView
            _position = if (isLoop) {
                _position % data.size
            } else {
                if (_position < wheelSize / 2) {
                    -1
                } else if (_position >= wheelSize / 2 + data.size) {
                    -1
                } else {
                    _position - wheelSize / 2
                }
            }
            val holder: ViewHolder
            if (_convertView == null) {
                holder = ViewHolder()
                val slotMachineItemView = SlotMachineItemView(parent.context)
                slotMachineItemView.setItemHeight(dp2px(mContext, HEIGHT))
                holder.itemView = slotMachineItemView
                _convertView = holder.itemView
                _convertView?.tag = holder
            } else {
                holder = _convertView.tag as ViewHolder
            }
            if (!isLoop) {
                holder.itemView?.visibility = if (_position == -1) INVISIBLE else VISIBLE
            }
            if (_position == -1) {
                _position = 0
            }
            holder.itemView?.data = data[_position]
            return _convertView
        }

        fun setLoop(loop: Boolean): WheelAdapter {
            if (loop != isLoop) {
                isLoop = loop
                super.notifyDataSetChanged()
            }
            return this
        }

        fun setWheelSize(wheelSize: Int): WheelAdapter {
            require(wheelSize and 1 != 0) { "wheel size must be an odd number." }
            this.wheelSize = wheelSize
            super.notifyDataSetChanged()
            return this
        }

        fun setData(list: List<Int>?): WheelAdapter {
            data.clear()
            if (null != list) {
                data.addAll(list)
            }
            super.notifyDataSetChanged()
            return this
        }

        fun getData(): List<Int> {
            return data
        }

        private class ViewHolder {
            var itemView: SlotMachineItemView? = null
        }
    }

    companion object {
        private const val WIDTH = 53.8f
        private const val HEIGHT = 49.8f
        private const val WHAT_SCROLL = 0x101
        private const val WHAT_DECREASE = 0x102
        private const val WHAT_END = 0x103
        private const val MIN_SCROLL_SPEED = 56
        private const val MAX_STOP_SPEED = 0

        //        private const val DECREASE_SPEED_DURATION = 400
        private const val DECREASE_SPEED_DURATION = 300

        // 滚动配置
//        const val SMOOTH_SCROLL_DURATION = 1200
        const val SMOOTH_SCROLL_DURATION = 120
        const val SECTION_DELAY = 300
    }
}