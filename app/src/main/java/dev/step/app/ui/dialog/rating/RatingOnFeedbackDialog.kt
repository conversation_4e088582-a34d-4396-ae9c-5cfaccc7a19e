package dev.step.app.ui.dialog.rating

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.openFeedbackMailto
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.common.BlankSpacer

@Composable
fun RateOnFeedbackDialog(
    navUp: () -> Unit,
) {
    val context = LocalContext.current

    AppDefWithCloseDialog(
        onDismiss = navUp,
        onClose = navUp,
        onConfirm = {
            context.openFeedbackMailto(
                email = "<EMAIL>",
                subject = "Feedback & suggestion",
            )
            navUp()
        },
        confirmText = stringResource(id = R.string.text_feedback),
        topPainter = painterResource(id = R.drawable.img_bigmoji_sad),
        adPlace = null,
        adPlaceName = null,
        content = {
            Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
                BlankSpacer(height = 32.dp)

                Text(text = stringResource(R.string.rate_on_feedback_title), fontSize = 20.sp)

                BlankSpacer(height = 12.dp)

                Text(text = stringResource(R.string.rate_on_feedback_tips), fontSize = 15.sp)

                BlankSpacer(height = 20.dp)
            }
        }
    )
}

@Preview
@Composable
private fun RateOnFeedbackDialogPreview() {
    RateOnFeedbackDialog {

    }
}