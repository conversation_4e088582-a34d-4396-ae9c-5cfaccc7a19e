package dev.step.app.ui.screen.profile

import dev.step.app.data.adt.GenderSetting
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.adt.MotionSensorSensitivity

data class ProfileViewState(
    val stepGoal: Int? = null,
    val sensitivity: MotionSensorSensitivity? = null,
    val bodyWeightLb: Float? = null,
    val bodyWeightKg: Float? = null,
    val bodyHeightFtIn: Pair<Int?, Int?>? = null,
    val bodyHeightCm: Int? = null,
    val gender: GenderSetting? = null,
    val stepLengthFtIn: Pair<Int?, Int?>? = null,
    val stepLengthCm: Int? = null,
    val measureUnit: MeasurementUnit? = null
){
    companion object {
        val Empty = ProfileViewState()
    }
}
