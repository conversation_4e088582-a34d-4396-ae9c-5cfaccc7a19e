package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeCap.Companion.Round
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcProfileWeight: ImageVector
    get() {
        if (_icProfileWight != null) {
            return _icProfileWight!!
        }
        _icProfileWight = Builder(name = "IcProfileWeight", defaultWidth = 70.0.dp, defaultHeight =
                70.0.dp, viewportWidth = 70.0f, viewportHeight = 70.0f).apply {
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 1.0f)
                lineTo(35.0f, 1.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 69.0f, 35.0f)
                lineTo(69.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 69.0f)
                lineTo(35.0f, 69.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 1.0f, 35.0f)
                lineTo(1.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 1.0f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(23.5f, 18.5f)
                lineTo(46.5f, 18.5f)
                arcTo(6.0f, 6.0f, 0.0f, false, true, 52.5f, 24.5f)
                lineTo(52.5f, 45.5f)
                arcTo(6.0f, 6.0f, 0.0f, false, true, 46.5f, 51.5f)
                lineTo(23.5f, 51.5f)
                arcTo(6.0f, 6.0f, 0.0f, false, true, 17.5f, 45.5f)
                lineTo(17.5f, 24.5f)
                arcTo(6.0f, 6.0f, 0.0f, false, true, 23.5f, 18.5f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(32.6172f, 40.1886f)
                arcToRelative(2.5757f, 2.5866f, 118.0005f, true, false, 4.5677f, 2.4287f)
                arcToRelative(2.5757f, 2.5866f, 118.0005f, true, false, -4.5677f, -2.4287f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(34.8477f, 41.232f)
                lineTo(33.164f, 33.6908f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(46.2273f, 34.5714f)
                curveTo(46.2273f, 29.1275f, 41.394f, 24.7143f, 35.4318f, 24.7143f)
                curveTo(29.4697f, 24.7143f, 24.6364f, 29.1275f, 24.6364f, 34.5714f)
            }
        }
        .build()
        return _icProfileWight!!
    }

private var _icProfileWight: ImageVector? = null
