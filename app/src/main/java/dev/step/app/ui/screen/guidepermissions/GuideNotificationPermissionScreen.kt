package dev.step.app.ui.screen.guidepermissions

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.NotificationsActive
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import dev.step.app.HomeNode
import dev.step.app.R
import dev.step.app.ScreenDestinationNode
import dev.step.app.androidplatform.biz.NotificationPermissionRequester
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject

@Parcelize
data class GuideNotificationPermissionScreenNode(
    val canGoBack: Boolean = false
) : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        val userOperateDataKv: UserOperateDataKv = koinInject()

        userOperateDataKv.hasBeenGuided = true

        GuideNotificationPermissionScreen(
            onNext = {
                navigator.pop()
                navigator.replaceLast(HomeNode())
            },
            navUp = {
                navigator.pop()
            },
            canGoBack = canGoBack
        )
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun GuideNotificationPermissionScreen(
    onNext: () -> Unit,
    navUp: () -> Unit,
    canGoBack: Boolean
) {
    NavBackHandler {
        if (canGoBack) {
            navUp()
        }
    }


    val context = LocalContext.current
    val notificationPermissionRequester: NotificationPermissionRequester = koinInject()

    val permissionState = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        rememberPermissionState(permission = Manifest.permission.POST_NOTIFICATIONS)
    } else {
        null
    }

    val onGrant = {
        if (permissionState == null || permissionState.status.isGranted) {
            onNext()
        } else {
            notificationPermissionRequester.tryToRequestIfNeeded(context.findActivity(), true)
        }
    }

    LaunchedEffect(permissionState?.status?.isGranted) {
        if (permissionState == null || permissionState.status.isGranted) {
            onNext()
        }
    }

    GuidePermissionContent(
        icon = rememberVectorPainter(image = Icons.Rounded.NotificationsActive),
        title = stringResource(id = R.string.noti_permission_req_title),
        description = stringResource(id = R.string.noti_permission_req_content),
        onGrant = onGrant,
        onNext = onNext,
        modifier = Modifier.fillMaxSize()
    )
}
