package dev.step.app.ui.common.task

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import dev.step.app.R
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.noRippleClickable

data class TaskData(
    val coins: Int,
    val text: String,
    val done: Boolean,
    val onClick: () -> Unit,
)

@OptIn(ExperimentalMaterialApi::class, ExperimentalFoundationApi::class)
@Composable
fun TaskItem(
    data: TaskData,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = data.onClick,
        modifier = modifier,
        shape = RoundedCornerShape(10.dp),
        color = AppColor.PrimaryLightAlpha8
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(98.dp)
        ) {
            if (data.done) {
                Box(
                    modifier = Modifier
                        .noRippleClickable {  }
                        .zIndex(1f)
                        .matchParentSize()
                        .background(AppColor.TextColorGray.copy(.62f)),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Rounded.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier
                            .size(48.dp)
                            .padding(bottom = 8.dp),
                        tint = Color.White.copy(.97f)
                    )
                }
            }

            Surface(
                shape = RoundedCornerShape(bottomEnd = 10.dp),
                modifier = Modifier.align(Alignment.TopStart)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .background(AppColor.FadedPrimaryBrushVertical)
                        .padding(horizontal = 8.dp, vertical = 2.dp)
                ) {
                    Text(text = "+${data.coins}", fontSize = 12.sp, color = Color.White)
                    BlankSpacer(width = 3.dp)
                    Image(
                        painter = painterResource(id = R.drawable.ic_coin),
                        contentDescription = null,
                        modifier = Modifier.size(14.dp)
                    )
                }
            }

            Text(
                text = data.text,
                fontSize = 14.sp,
                modifier = Modifier
                    .basicMarquee()
                    .align(Alignment.Center)
                    .padding(bottom = 6.dp),
                maxLines = 1
            )

            TaskItemInsideButton(
                text = stringResource(id = R.string.text_get_coins),
                iconPainter = painterResource(id = R.drawable.ic_video),
                onClick = data.onClick,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 8.dp)
            )
        }
    }
}

@Composable
private fun TaskItemInsideButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    iconPainter: Painter? = null,
    bgBrush: Brush = AppColor.FadedPrimaryBrushVertical,
) {
    Surface(
        modifier = modifier.noRippleClickable { onClick() },
        shape = CircleShape,
    ) {
        Row(
            modifier = Modifier
                .background(bgBrush),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (iconPainter != null) {
                BlankSpacer(width = 8.dp)

                Image(
                    painter = iconPainter,
                    contentDescription = null,
                    modifier = Modifier.size(15.dp)
                )
                BlankSpacer(width = 4.dp)
            } else {
                BlankSpacer(width = 16.dp)
            }

            Text(
                text = text,
                modifier = Modifier.padding(vertical = 2.dp),
                color = Color.White,
                fontSize = 12.sp
            )
            BlankSpacer(width = 11.dp)
        }
    }
}

@Preview
@Composable
private fun TaskItemPreview() {
    TaskItem(
        data = TaskData(
            coins = 1000,
            text = "lalalalalala",
            done = false,
            onClick = { /*TODO*/ }
        ),
        modifier = Modifier.width(160.dp)
    )
}