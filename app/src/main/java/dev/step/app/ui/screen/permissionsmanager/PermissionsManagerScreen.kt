package dev.step.app.ui.screen.permissionsmanager

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Switch
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.BatteryChargingFull
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.material.icons.rounded.DirectionsRun
import androidx.compose.material.icons.rounded.NotificationsActive
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.rememberNavigator
import com.roudikk.guia.extensions.pop
import dev.step.app.R
import dev.step.app.ScreenDestinationNode
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.biz.ActivityRecognitionPermissionRequester
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAd
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.DefTopAppBar
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.noRippleClickable
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState

@Parcelize
object PermissionsManagerNode : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        PermissionsManagerScreen(navigator)
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PermissionsManagerScreen(
    navigator: Navigator,
) {
    val context = LocalContext.current
    val viewModel: PermissionsManagerViewModel = koinViewModel()
    val viewState by viewModel.collectAsState()

    val permissionState = ActivityRecognitionPermissionRequester.permissionState()

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_RESUME -> viewModel.onRefresh(context.findActivity())
            else -> {}
        }
    }

    if (viewState.showActivityRecognitionDialog) {
        ActivityRecognitionDialog(
            onDismiss = {
                viewModel.doShowActivityRecognitionDialog(false)
            },
            onRequest = {
                permissionState?.let {
                    viewModel.requestActivityRecognition(
                        context.findActivity(),
                        permissionState
                    )
                }
                viewModel.doShowActivityRecognitionDialog(false)
            },
            isForceOpen = true
        )
    }

    if (viewState.showIgnoreBatteryOptimizationsDialog) {
        IgnoreBatteryOptimizationDialog(
            onDismiss = {
                viewModel.doShowIgnoreBatteryOptimizationsDialog(false)
            },
            onRequest = {
                viewModel.requestIgnoringBatteryOptimization(context.findActivity())
                viewModel.doShowIgnoreBatteryOptimizationsDialog(false)
            },
            isForceOpen = true
        )
    }

    if (viewState.showNotificationDialog) {
        NotificationPermissionDialog(
            onDismiss = {
                viewModel.doShowNotificationPermissionDialog(false)
            },
            onRequest = {
                viewModel.requestNotificationPermission(context.findActivity())
                viewModel.doShowNotificationPermissionDialog(false)
            }
        )
    }


    Scaffold(
        topBar = {
            DefTopAppBar(
                pop = navigator::pop,
                title = stringResource(R.string.text_manage_permissions)
            )
        },
        modifier = Modifier.navigationBarsPadding()
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 16.dp)

            PermissionItem(
                isGranted = viewState.isActivityRecognitionGranted,
                iconPainter = rememberVectorPainter(image = Icons.Rounded.DirectionsRun),
                title = stringResource(R.string.permissions_required_text_physical_activity),
                description = stringResource(R.string.permissions_required_text_track_your_fitness_movements),
                onRequestPermission = {
                    viewModel.doShowActivityRecognitionDialog(true)
                },
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)

            PermissionItem(
                isGranted = viewState.isIgnoreBatteryOptimizationsGranted,
                iconPainter = rememberVectorPainter(image = Icons.Rounded.BatteryChargingFull),
                title = stringResource(R.string.text_protected_app),
                description = stringResource(R.string.text_ignore_battery_optimization),
                onRequestPermission = {
                    viewModel.doShowIgnoreBatteryOptimizationsDialog(true)
                },
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)

            PermissionItem(
                isGranted = viewState.isNotificationGranted,
                iconPainter = rememberVectorPainter(image = Icons.Rounded.NotificationsActive),
                title = stringResource(R.string.text_notification),
                description = stringResource(R.string.notify_when_your_coin_amount_changes),
                onRequestPermission = {
                    viewModel.doShowNotificationPermissionDialog(true)
                },
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)

//            MRecAd(placeholder = MRecAdPlaceholder.HomeScreen, placeName = "manage_permissions")
            AdmobNativeAd(
                place = NativeAdPlace.ManagePermissions,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)
        }
    }
}

@Composable
private fun PermissionItem(
    isGranted: Boolean,
    iconPainter: Painter,
    title: String,
    description: String,
    onRequestPermission: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.noRippleClickable {
            if (!isGranted) {
                onRequestPermission()
            }
        },
        shape = RoundedCornerShape(10.dp),
        color = AppColor.PrimaryLightAlpha8
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            BlankSpacer(width = 4.dp)
            Box(modifier = Modifier.padding(horizontal = 18.dp, vertical = 24.dp)) {
                Icon(
                    painter = iconPainter,
                    contentDescription = null,
                    modifier = Modifier.size(28.dp),
                    tint = AppColor.Primary
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(text = title, fontSize = 16.sp)
                BlankSpacer(height = 2.dp)
                Text(text = description, fontSize = 12.sp, fontWeight = FontWeight.Normal)
            }

            if (isGranted) {
                BlankSpacer(width = 8.dp)
                Icon(
                    imageVector = Icons.Rounded.CheckCircle,
                    contentDescription = null,
                    tint = AppColor.Primary
                )
                BlankSpacer(width = 10.dp)
            } else {
                Switch(checked = false, onCheckedChange = {
                    onRequestPermission()
                })
            }

            BlankSpacer(width = 20.dp)
        }
    }
}

@Preview
@Composable
private fun PermissionItemPreview() {
    AppTheme {
        PermissionItem(
            isGranted = true,
            iconPainter = rememberVectorPainter(image = Icons.Rounded.BatteryChargingFull),
            title = "Lalalala",
            description = "lalalala",
            onRequestPermission = {}
        )
    }
}

@Preview
@Composable
private fun PermissionsManagerScreenPreview() {
    AppTheme {
        PermissionsManagerScreen(navigator = rememberNavigator())
    }
}