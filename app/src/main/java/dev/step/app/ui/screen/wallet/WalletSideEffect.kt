package dev.step.app.ui.screen.wallet


sealed interface WalletSideEffect {
    data class ToAdRewardedDialog(
        val from: String,
        val coins: Int,
        val times: Int
    ) : WalletSideEffect
}

internal fun handleWalletSideEffect(
    it: WalletSideEffect,
    openRewardedDialog: (from: String, coins: Int, times: Int) -> Unit,
) {
    when (it) {
        is WalletSideEffect.ToAdRewardedDialog -> openRewardedDialog(it.from, it.coins, it.times)
    }
}