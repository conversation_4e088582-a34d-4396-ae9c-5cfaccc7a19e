package dev.step.app.ui.screen.permissionsmanager

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import org.koin.compose.koinInject

@Composable
internal fun IgnoreBatteryOptimizationDialog(
    onDismiss: () -> Unit,
    onRequest: () -> Unit,
    isForceOpen: Boolean,
) {
    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()

    DisposableEffect(Unit) {
        onDispose {
            if (!isForceOpen) {
                ignoringBatteryOptimizationRequester
                    .storeOpenBatteryOptimizationSettingsInstant(nowInstant())
                ignoringBatteryOptimizationRequester.openBatteryOptimizationTimes++
            }
        }
    }

    PermissionsDialog(
        onDismiss = {
            logEventRecord("battery_permission_request_fail")
            debugLog("battery_permission_request_fail")

            onDismiss()
        },
        onRequest = onRequest,
        mainPainter = painterResource(R.drawable.img_permissions_ignore_battery_optimizations),
        title = stringResource(R.string.allow_protected_app),
        content1 = stringResource(R.string.text_ignore_battery_optimization),
        content2 = stringResource(R.string.you_can_disable_it_anytime),
    )
}