package dev.step.app.ui.screen.redeemedcoupon

sealed interface RedeemedGiftCardSideEffect {
    data object Redeemed : RedeemedGiftCardSideEffect
    data object EmailError: RedeemedGiftCardSideEffect
}

fun handleRedeemedGiftCardSideEffect(
    it: RedeemedGiftCardSideEffect,
    onRedeemed: () -> Unit,
    onEmailError: () -> Unit
) {
    when (it) {
        RedeemedGiftCardSideEffect.Redeemed -> onRedeemed()
        RedeemedGiftCardSideEffect.EmailError -> onEmailError()
    }
}