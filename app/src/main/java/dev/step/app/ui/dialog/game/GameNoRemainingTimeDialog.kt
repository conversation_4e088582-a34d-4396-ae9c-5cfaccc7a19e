@file:Suppress("ObjectPropertyName", "LocalVariableName")

package dev.step.app.ui.dialog.game

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch

private val _gameNoRemainingNavUpEventFlow = MutableSharedFlow<Unit>()
val gameNoRemainingNavUpEventFlow: Flow<Unit> get() = _gameNoRemainingNavUpEventFlow

@Composable
fun GameNoRemainingTimeDialog(
    gameADT: GameADT,
    navUp: () -> Unit
) {
    val scope = rememberCoroutineScope()

    val _navUp = {
        scope.launch {
            _gameNoRemainingNavUpEventFlow.emit(Unit)
            navUp()
        }
        Unit
    }

    AppDefWithCloseDialog(
        onDismiss = _navUp,
        onClose = _navUp,
        onConfirm = _navUp,
        confirmText = stringResource(id = R.string.text_ok),
        topPainter = painterResource(id = R.drawable.img_bigmoji_sad),
        adPlace = NativeAdPlace.Dialog,
        adPlaceName = "${gameADT.gamePageName}_no_time"
    ) {
        Column(
            modifier = Modifier.bodyWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            BlankSpacer(height = 44.dp)

            Text(
                text = stringResource(R.string.game_no_remaining_title),
                fontSize = 20.sp,
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 20.dp)
            )

            BlankSpacer(height = 14.dp)

            Text(
                text = stringResource(R.string.game_no_remaining_tips),
                fontSize = 15.sp,
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 20.dp)
            )

            BlankSpacer(height = 20.dp)
        }
    }
}