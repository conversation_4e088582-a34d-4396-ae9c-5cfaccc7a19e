package dev.step.app.ui.screen.permissionsmanager

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import dev.step.app.R
import dev.step.app.androidplatform.biz.ActivityRecognitionPermissionRequester

@Composable
internal fun ActivityRecognitionDialog(
    onDismiss: () -> Unit,
    onRequest: () -> Unit,
    isForceOpen: Boolean,
) {
    DisposableEffect(Unit) {
        onDispose {
            if (!isForceOpen) {
                ActivityRecognitionPermissionRequester.storeOpenInstant()
                ActivityRecognitionPermissionRequester.requestDialogDisplayCount++
            }
        }
    }

    PermissionsDialog(
        onDismiss = onDismiss,
        onRequest = onRequest,
        mainPainter = painterResource(R.drawable.img_permissions_activity_recognition),
        title = stringResource(R.string.allow_physical_activity),
        content1 = stringResource(R.string.permissions_required_text_track_your_fitness_movements),
        content2 = stringResource(R.string.you_can_disable_it_anytime),
    )
}