package dev.step.app.ui.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowRight
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.ui.theme.*
import dev.step.app.ui.theme.appimg.IcProfileHeight

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ProfileMainSettingItem(
    title: String,
    iconPainter: Painter,
    value: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(10.dp),
        color = AppColor.PrimaryLightAlpha8
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 11.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BlankSpacer(width = 13.dp)

            Image(
                painter = iconPainter,
                contentDescription = null,
                modifier = Modifier.size(34.dp),
            )

            BlankSpacer(width = 10.dp)

            Text(text = title, modifier = Modifier.weight(1f), fontSize = 14.5.sp)

            BlankSpacer(width = 8.dp)

            Text(
                text = value,
                fontWeight = FontWeight.Bold,
                fontSize = 15.sp,
                color = AppColor.Primary
            )

            BlankSpacer(width = 6.dp)

            SettingItemRightArrowIcon()

            BlankSpacer(width = 14.dp)
        }
    }
}

@Composable
private fun SettingItemRightArrowIcon(
    modifier: Modifier = Modifier
) {
    Surface(
        shape = CircleShape,
        modifier = modifier.size(19.dp),
        color = Color.Black
    ) {
        Icon(
            imageVector = Icons.Rounded.KeyboardArrowRight,
            contentDescription = null,
            tint = Color.White
        )
    }
}

@Composable
fun ProfileOtherActionItem(
    title: String,
    iconPainter: Painter,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
) {
    Box(
        modifier = modifier.clickable(onClick = onClick),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 14.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BlankSpacer(width = 22.dp)
            Icon(
                painter = iconPainter,
                contentDescription = contentDescription,
                modifier = Modifier.size(20.dp)
            )
            BlankSpacer(width = 8.dp)
            Text(text = title, modifier = Modifier.weight(1f), fontSize = 14.sp)
            BlankSpacer(width = 22.dp)
        }
    }
}

@Preview
@Composable
fun ProfileMainSettingItemPreview() {
    ProfileMainSettingItem(
        title = "lalala",
        iconPainter = rememberVectorPainter(image = AppImg.IcProfileHeight),
        value = "1000kg",
        onClick = { }
    )
}

@Preview
@Composable
fun ProfileNormalSettingItemPreview() {
    ProfileOtherActionItem(
        title = "lalala",
        iconPainter = painterResource(id = R.drawable.ic_profile_setting_privacy),
        onClick = { }
    )
}
