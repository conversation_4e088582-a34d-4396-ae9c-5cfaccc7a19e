package dev.step.app.ui.screen.steps

sealed interface StepsSideEffect {
    data object ToSignInRewardedDialog : StepsSideEffect
    data class ToNewUserRewardedDialog(val adBizEnable: Boolean) : StepsSideEffect
    data object ToStageOneRewardedDialog : StepsSideEffect
    data object ToStageTwoRewardedDialog : StepsSideEffect
    data object ToExchangeCoinsRewardedDialog : StepsSideEffect
    data object ToNoEnoughStepsDialog : StepsSideEffect
    data class ToAdRewardedDialog(
        val from: String,
        val coins: Int,
        val times: Int
    ) : StepsSideEffect

}

internal fun handleStepsSideEffect(
    it: StepsSideEffect,
    openSignInRewardedDialog: () -> Unit,
    openExchangeCoinsRewardedDialog: () -> Unit,
    openNewUserRewardedDialog: (adBizEnable: Boolean) -> Unit,
    openNoEnoughStepsDialog: () -> Unit,
    openStageOneRewardedDialog: () -> Unit,
    openStageTwoRewardedDialog: () -> Unit,
    openRewardedDialog: (from: String, coins: Int, times: Int) -> Unit,
) {
    when (it) {
        StepsSideEffect.ToSignInRewardedDialog -> openSignInRewardedDialog()
        StepsSideEffect.ToExchangeCoinsRewardedDialog -> openExchangeCoinsRewardedDialog()
        is StepsSideEffect.ToNewUserRewardedDialog -> openNewUserRewardedDialog(it.adBizEnable)
        StepsSideEffect.ToNoEnoughStepsDialog -> openNoEnoughStepsDialog()
        StepsSideEffect.ToStageOneRewardedDialog -> openStageOneRewardedDialog()
        StepsSideEffect.ToStageTwoRewardedDialog -> openStageTwoRewardedDialog()
        is StepsSideEffect.ToAdRewardedDialog -> openRewardedDialog(
            it.from,
            it.coins,
            it.times
        )
    }
}
