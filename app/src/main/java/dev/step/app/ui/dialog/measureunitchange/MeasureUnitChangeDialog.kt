package dev.step.app.ui.dialog.measureunitchange

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
//import dev.step.app.androidplatform.biz.MaxInterstitialAdHelper
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.AppDefDialog
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.RoundedCornerShape7Dp
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun MeasureUnitChangeDialog(
    navUp: () -> Unit,
    userSettingsDataKv: UserSettingsDataKv = koinInject(),
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator)

    val scope = rememberCoroutineScope()

    var mus by remember {
        mutableStateOf<MeasurementUnit?>(null)
    }

    LaunchedEffect(Unit) {
        mus = userSettingsDataKv.measurementUnit
    }

    AppDefDialog(
        onDismiss = navUp,
        onCancel = navUp,
        onConfirm = {
            scope.launch {
                mus?.let {
                    userSettingsDataKv.changeMeasurementUnit(it)
//                    interstitialAdHelper.tryToShowAd(
//                        "save_measure_unit",
//                        onAdShowingOrSkip = { navUp() }
//                    )
                    OnBack()
                }
            }
        },
    ) {
        Column(
            modifier = Modifier
                .bodyWidth()
                .padding(bottom = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.text_metric_and_imperial_unit),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.bodyWidth()
            )

            BlankSpacer(height = 16.dp)

            val imperialUnitSelectedColor =
                if (mus == MeasurementUnit.Imperial) AppColor.Primary else AppColor.TextColorGray

            Surface(
                onClick = { mus = MeasurementUnit.Imperial }, elevation = 0.dp,
                shape = RoundedCornerShape7Dp,
                border = BorderStroke(2.dp, imperialUnitSelectedColor),
                modifier = Modifier.size(width = 108.dp, height = 38.dp),
                contentColor = AppColor.BackgroundDefault
            ) {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    Text(text = "lbs / ft", fontSize = 17.sp, color = imperialUnitSelectedColor)
                }
            }

            BlankSpacer(height = 12.dp)

            val metricUnitSelectedColor =
                if (mus == MeasurementUnit.Metric) AppColor.Primary else AppColor.TextColorGray

            Surface(
                onClick = { mus = MeasurementUnit.Metric },
                shape = RoundedCornerShape7Dp,
                border = BorderStroke(2.dp, metricUnitSelectedColor),
                modifier = Modifier.size(width = 108.dp, height = 38.dp),
                contentColor = AppColor.BackgroundDefault
            ) {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    Text(text = "kg / cm", fontSize = 17.sp, color = metricUnitSelectedColor)
                }
            }

            BlankSpacer(height = 4.dp)
        }
    }
}
