package dev.step.app.ui.screen.steps

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.R
import dev.step.app.androidplatform.*
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.sensor.StepDetectorSensor
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
//import dev.step.app.androidplatform.biz.MaxRewardedAdHelper
//import dev.step.app.androidplatform.biz.rewardedAdWithTipsDialogFinishEventFlow
import dev.step.app.androidplatform.ext.scale
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.androidplatform.memorystore.StepTrackingSession
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.data.kvstore.WalletBizKv
//import dev.step.app.data.kvstore.UserOperateDataKv
//import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.data.pojo.DayStepsData
import dev.step.app.data.repo.StepsTrackRecordRepo
import dev.step.app.globalNavigator
import dev.step.app.ui.common.task.TaskData
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import java.time.DayOfWeek
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@KoinViewModel
class StepsViewModel(
    private val stepTrackingSession: StepTrackingSession,
    private val stepsTrackRecordRepo: StepsTrackRecordRepo,
    private val userSettingsDataKv: UserSettingsDataKv,
    private val userOperateDataKv: UserOperateDataKv,
    private val walletBizKv: WalletBizKv,
//    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val rewardedAdManager: RewardedAdManager,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
) : ViewModel(), ContainerHost<StepsViewState, StepsSideEffect> {

    override val container: Container<StepsViewState, StepsSideEffect> =
        container(StepsViewState.Empty)

    private var adJob: Job? = null

    init {
        stepTrackingSession.stepEventFlow.onEach {
            it ?: return@onEach
            onStepEventUpdate()
        }.launchIn(viewModelScope)
    }

    fun stepTrackingUseStepDetectorSensor() =
        stepTrackingSession.currentUseSensor is StepDetectorSensor

    fun startTracking() {
        debugLog("stepTrackingSession.startTracking()")
        stepTrackingSession.startTracking()
    }

    fun onRefresh(context: Context) = intent {
        doReduceWithdrawEnable()

        val now = nowInstant()

        val mus = userSettingsDataKv.measurementUnit ?: MeasurementUnit.Imperial
        val stepsGoal = userSettingsDataKv.stepsGoal

        val todayStepsData = stepsTrackRecordRepo.todayStepsData(now)
        val weekStepDateList = stepsTrackRecordRepo.weekStepsData(now)

        val stepLengthCm = userSettingsDataKv.stepLengthCm

        val distanceKm = (stepLengthCm * todayStepsData.steps) / 100f / 1000f
        val distanceMi = kmToMile(distanceKm)

        val weightKg = userSettingsDataKv.bodyWeightKg

        val stepsDuration =
            (todayStepsData.steps * humanAvgSecondsPerSteps).toInt()
                .toDuration(DurationUnit.SECONDS)
        val kcal = calculateKcal(stepsDuration, weightKg)

        val isOrganic = userOperateDataKv.tenjinAttr.isOrganic()

        reduce {
            state.copy(
                walletCoins = walletBizKv.getCoinBalance(),
                mus = mus,
                stepsGoal = stepsGoal,
                todayStepsData = todayStepsData,
                daysOfWeekStepDataList = weekStepDateList,
                distanceKm = distanceKm.scale(3),
                distanceMi = distanceMi.scale(3),
                kcal = kcal.scale(3),
                duration = stepsDuration,

                rewardedBubbles = if (!isOrganic) remoteConfigHelper.getRewardBubble() else null
            )
        }

        doReduceIsNewUser()
        doReduceSignIn()
        doReduceStageOneTask()
        doReduceStageTwoTask()
        doReduceTaskList(context)
        if (!isOrganic) {
            doReduceRewardBubblesWhenNeedReset()
        }

        startTracking()
    }

    private fun doReduceWithdrawEnable() = intent {
        val tenjinAttr = userOperateDataKv.tenjinAttr
        reduce { state.copy(withdrawEnable = !tenjinAttr.isOrganic()) }
    }

    private fun doReduceTaskList(context: Context) = intent {
        val tasks = mutableListOf(
            TaskData(
                text = context.getString(R.string.text_1000_steps),
                done = state.isStageOneDone,
                coins = state.stageOneRewardCoins,
                onClick = ::onStageOneRewarded
            ),
            TaskData(
                text = context.getString(R.string.text_5000_steps),
                done = state.isStageTwoDone,
                coins = state.stageTwoRewardCoins,
                onClick = ::onStageTwoRewarded
            ),
        )

        val signInTask = TaskData(
            text = context.getString(R.string.text_sign_in),
            done = state.isSignIn,
            coins = state.signInRewardCoins,
            onClick = ::onSignIn
        )

        if (signInTask.done) {
            tasks.add(signInTask)
        } else {
            tasks.add(0, signInTask)
        }

        if (state.isNewUser) {
            val newUserTask = TaskData(
                text = context.getString(R.string.text_new_user_bonus),
                done = false,
                coins = state.newUserRewardCoins,
                onClick = {
                    logEventRecord("click_step_newuser")
                    onNewUserRewarded(false)
                }
            )

            tasks.add(0, newUserTask)
        }

        reduce {
            state.copy(tasks = tasks)
        }
    }

    private fun doReduceIsNewUser() = intent {
        reduce {
            state.copy(
                isNewUserDialogHasShow = walletBizKv.isNewUserDialogHasShow(),
                isNewUser = walletBizKv.isNewUser(),
                newUserRewardCoins = remoteConfigHelper.getDailyTask()?.new_user_rewarded_coins ?: 0
            )
        }
    }

    private fun doReduceSignIn() = intent {
        val now = nowNetTimeTodayStartInstant()
        val dayOfWeek = now.toLocalDateTime(TimeZone.currentSystemDefault()).dayOfWeek
        val signIn = walletBizKv.getSignIn(dayOfWeek)

        val isSignIn = when (dayOfWeek) {
            DayOfWeek.SUNDAY -> signIn.sun
            DayOfWeek.MONDAY -> signIn.mon
            DayOfWeek.TUESDAY -> signIn.tue
            DayOfWeek.WEDNESDAY -> signIn.wed
            DayOfWeek.THURSDAY -> signIn.thu
            DayOfWeek.FRIDAY -> signIn.fri
            DayOfWeek.SATURDAY -> signIn.sat
        }

        reduce {
            state.copy(
                isSignIn = isSignIn,
                signInRewardCoins = remoteConfigHelper.getDailyTask()?.sign_in_rewarded_coins ?: 0
            )
        }
    }

    fun tryToAutoSignIn() = intent {
        val now = nowNetTimeTodayStartInstant()
        val dayOfWeek = now.toLocalDateTime(TimeZone.currentSystemDefault()).dayOfWeek
        val signIn = walletBizKv.getSignIn(dayOfWeek)

        val isSignIn = when (dayOfWeek) {
            DayOfWeek.SUNDAY -> signIn.sun
            DayOfWeek.MONDAY -> signIn.mon
            DayOfWeek.TUESDAY -> signIn.tue
            DayOfWeek.WEDNESDAY -> signIn.wed
            DayOfWeek.THURSDAY -> signIn.thu
            DayOfWeek.FRIDAY -> signIn.fri
            DayOfWeek.SATURDAY -> signIn.sat
        }

        if (!isSignIn) {
            val firstTimeLaunchAppInstant =
                userOperateDataKv.firstTimeLaunchAppInstant

            val currentStacks = globalNavigator?.backstackKeys

            debugLog("currentStacks: $currentStacks")

            if (firstTimeLaunchAppInstant != null
                && firstTimeLaunchAppInstant
                    .todayStartInstant()
                    .plus(1.toDuration(DurationUnit.DAYS)) <= nowInstant().todayStartInstant()
            ) {
                debugLog("auto_sign in post side effect")
                postSideEffect(StepsSideEffect.ToSignInRewardedDialog)
            }
        }
    }

    private fun doReduceStageOneTask() = intent {
        val todayStartInstant = nowInstant().todayStartInstant()
        val lastDoneStageOneTaskInstant = walletBizKv.getStageOneTaskInstant()

        reduce {
            state.copy(
                isStageOneDone = todayStartInstant.epochSeconds == lastDoneStageOneTaskInstant.epochSeconds,
                stageOneRewardCoins = remoteConfigHelper.getDailyTask()?.s1_rewarded_coins ?: 0
            )
        }
    }

    private fun doReduceStageTwoTask() = intent {
        val todayStartInstant = nowInstant().todayStartInstant()
        val lastDoneStageTwoTaskInstant = walletBizKv.getStageTwoTaskInstant()

        reduce {
            state.copy(
                isStageTwoDone = todayStartInstant.epochSeconds == lastDoneStageTwoTaskInstant.epochSeconds,
                stageTwoRewardCoins = remoteConfigHelper.getDailyTask()?.s2_rewarded_coins ?: 0
            )
        }
    }

    private fun doReduceRewardBubblesWhenNeedReset() = intent {
        val nowInstant = nowInstant()
        val rewardBubblesResetDayStartInstant =
            walletBizKv.getRewardBubblesResetInstant()

        val rewardBubblesRefreshIntervalMinutes =
            remoteConfigHelper.getRewardBubblesRefreshIntervalMinutes()

        val resetInstantIsZero = rewardBubblesResetDayStartInstant.epochSeconds <= 0L
        val overResetOneHour =
            nowInstant >= rewardBubblesResetDayStartInstant
                .plus(rewardBubblesRefreshIntervalMinutes.toDuration(DurationUnit.MINUTES))

        if (resetInstantIsZero || overResetOneHour) {
            walletBizKv.resetRewardBubbleShowState(nowInstant).also {
                reduce { state.copy(rewardBubblesShowState = it) }
            }
        } else {
            reduce { state.copy(rewardBubblesShowState = walletBizKv.getRewardBubbleShowState()) }
        }
    }

    private fun onStepEventUpdate() = intent {

        val stepLengthCm = userSettingsDataKv.stepLengthCm
        val weightKg = userSettingsDataKv.bodyWeightKg

        val steps = state.todayStepsData.steps + 1
        val stepsDuration =
            (steps * humanAvgSecondsPerSteps).toInt().toDuration(DurationUnit.SECONDS)

        val distanceKm = (stepLengthCm * steps) / 100f / 1000f
        val distanceMi = kmToMile(distanceKm)

        reduce {
            state.copy(
                todayStepsData = DayStepsData(
                    state.todayStepsData.dayInstant,
                    steps
                ),
                duration = stepsDuration,
                distanceKm = distanceKm.scale(3),
                distanceMi = distanceMi.scale(3),
                kcal = calculateKcal(stepsDuration, weightKg).scale(3)
            )
        }
    }

    fun onExchangeCoins() = intent {
        logEventRecord("click_step_exchange")

        if (state.todayStepsData.steps >= 100) {

            adJob?.cancel()
            adJob = viewModelScope.launch {
                rewardedAdManager.tryToShowRewardedLoadingDialog("exchange")
                rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                    postSideEffect(StepsSideEffect.ToExchangeCoinsRewardedDialog)

                    adJob?.cancel()
                    adJob = null
                }
            }

        } else {
            postSideEffect(StepsSideEffect.ToNoEnoughStepsDialog)
        }
    }

    fun onStageOneRewarded() = intent {
        logEventRecord("click_step_bonus1")

        if (state.todayStepsData.steps >= 1000) {
            adJob?.cancel()
            adJob = viewModelScope.launch {
                rewardedAdManager.tryToShowRewardedLoadingDialog("task1")
                rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                    postSideEffect(StepsSideEffect.ToStageOneRewardedDialog)

                    adJob?.cancel()
                    adJob = null
                }
            }
        } else {
            postSideEffect(StepsSideEffect.ToNoEnoughStepsDialog)
        }
    }

    fun onStageTwoRewarded() = intent {
        logEventRecord("click_step_bonus2")

        if (state.todayStepsData.steps >= 5000) {
            adJob?.cancel()
            adJob = viewModelScope.launch {
                rewardedAdManager.tryToShowRewardedLoadingDialog("task2")
                rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                    postSideEffect(StepsSideEffect.ToStageTwoRewardedDialog)

                    adJob?.cancel()
                    adJob = null
                }
            }
        } else {
            postSideEffect(StepsSideEffect.ToNoEnoughStepsDialog)
        }
    }

    fun onSignIn() = intent {
        logEventRecord("click_step_checkin")

        adJob?.cancel()
        adJob = viewModelScope.launch {
            rewardedAdManager.tryToShowRewardedLoadingDialog("checkin")
            rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                postSideEffect(StepsSideEffect.ToSignInRewardedDialog)

                adJob?.cancel()
                adJob = null
            }
        }
    }

    fun onNewUserRewarded(dialogAdBizEnable: Boolean) = intent {
        if (dialogAdBizEnable) {
            postSideEffect(StepsSideEffect.ToNewUserRewardedDialog(true))
        } else {
            adJob?.cancel()
            adJob = viewModelScope.launch {
                rewardedAdManager.tryToShowRewardedLoadingDialog("newuser")
                rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                    postSideEffect(StepsSideEffect.ToNewUserRewardedDialog(false))

                    adJob?.cancel()
                    adJob = null
                }
            }
        }
    }

    fun onRewardedBubbleClick(bubbleId: Int) = intent {
        adJob?.cancel()
        adJob = viewModelScope.launch {
            rewardedAdManager.tryToShowRewardedLoadingDialog("bubble_ad")
            rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                val newRewardBubblesShowState = walletBizKv.rewardBubbleGone(bubbleId)

                debugLog("onRewardedBubbleClick() newRewardBubblesShowState: $newRewardBubblesShowState")


                val (coins: Int?, times: Int?) = state.rewardedBubbles?.get(bubbleId) ?: (0 to 1)

                postSideEffect(
                    StepsSideEffect.ToAdRewardedDialog(
                        from = "step_bubbles",
                        coins ?: 0,
                        times ?: 1
                    )
                )
                reduce { state.copy(rewardBubblesShowState = newRewardBubblesShowState) }

                adJob?.cancel()
                adJob = null
            }
        }
    }

}
