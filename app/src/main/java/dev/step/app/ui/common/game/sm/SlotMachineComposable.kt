package dev.step.app.ui.common.game.sm

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import dev.step.app.androidplatform.androidcomponent.global.debugLog

@Composable
fun SlotMachine(
    onLayout: (SlotMachineView) -> Unit,
    onSlotMachineStop: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        AndroidView(factory = {
            SlotMachineView(it).apply {
                setOnStopListener(onSlotMachineStop)
            }.apply {
                onLayout(this)
            }
        })
    }
}

@Preview
@Composable
private fun SlotMachinePreview() {
    SlotMachine(
        {},
        {
            debugLog("SlotMachinePreview onSlotMachineStop")
        },
        modifier = Modifier.fillMaxWidth()
    )
}