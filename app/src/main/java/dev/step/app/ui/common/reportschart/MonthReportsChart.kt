package dev.step.app.ui.common.reportschart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.material.icons.rounded.KeyboardArrowRight
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.ext.scale
import dev.step.app.androidplatform.ext.time.*
import dev.step.app.androidplatform.stepsToDistance
import dev.step.app.androidplatform.stepsToDurationSeconds
import dev.step.app.androidplatform.stepsToKcal
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.pojo.*
import dev.step.app.ui.screen.reports.ReportsStatisticsMode
import dev.step.app.ui.theme.*
import kotlinx.datetime.*
import java.time.format.TextStyle
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private val lunarMonth = listOf(4, 6, 9, 11)

class XAxisDayOfMonthValueFormatter(private val selectMonth: Month) : ValueFormatter() {

    override fun getFormattedValue(value: Float): String {
        val tz = TimeZone.currentSystemDefault()

        val ins = Instant.fromEpochSeconds(value.toLong())

        val ldt = ins.toLocalDateTime(tz)

        debugLog("XAxisDayOfMonthValueFormatter ldt: $ldt")

        val monthAndDayText =
            if (selectMonth != ldt.month) { // be splice to next month, but this is a bug from MPAndroidChart :)
                val monthLastDayDateTime = ins.minus(DateTimePeriod(months = 1), tz)
                    .thisMonthEndInstant(tz)
                    .toLocalDateTime(tz)

                monthLastDayDateTime.monthAndDayDisplayName()

            } else {
                ldt.monthAndDayDisplayName()
            }

        return monthAndDayText
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun MonthReportsChart(
    monthReportsData: MonthReportsData,
    stepLengthCm: Float,
    weightKg: Float,
    rsm: ReportsStatisticsMode,
    mus: MeasurementUnit,
    onMonthReportChange: (Instant) -> Unit,
    modifier: Modifier = Modifier,
) {
    val totalSteps = monthReportsData.dayStepsDataList.sumOf { it.steps }
    val avgSteps =
        if (monthReportsData.dayStepsDataList.isEmpty()) 0 else (totalSteps / monthReportsData.dayStepsDataList.size)
    val yMaxSteps = ((monthReportsData.dayStepsDataList.maxOfOrNull { it.steps }) ?: 0)
    val yOffsetSteps = yMaxSteps.toString().length * 20

    val (total, avg) = when (rsm) {
        ReportsStatisticsMode.Calories -> stepsToKcal(totalSteps, weightKg) to stepsToKcal(
            avgSteps,
            weightKg
        )

        ReportsStatisticsMode.Distance -> stepsToDistance(
            totalSteps,
            stepLengthCm,
            mus
        ) to stepsToDistance(avgSteps, stepLengthCm, mus)

        ReportsStatisticsMode.Steps -> totalSteps.toFloat() to avgSteps.toFloat()
        ReportsStatisticsMode.Time -> stepsToDurationSeconds(totalSteps) to stepsToDurationSeconds(
            avgSteps
        )
    }

    val (yMax, yOffset) = when (rsm) {
        ReportsStatisticsMode.Calories -> stepsToKcal(yMaxSteps, weightKg) to stepsToKcal(
            yOffsetSteps,
            weightKg
        ) + 10

        ReportsStatisticsMode.Distance -> stepsToDistance(
            yMaxSteps,
            stepLengthCm,
            mus
        ) to stepsToDistance(yOffsetSteps, stepLengthCm, mus)

        ReportsStatisticsMode.Steps -> yMaxSteps.toFloat() to yOffsetSteps.toFloat()
        ReportsStatisticsMode.Time -> stepsToDurationSeconds(yMaxSteps) to stepsToDurationSeconds(
            yOffsetSteps
        )
    }

    val lineChartPoints = monthReportsData.dayStepsDataList.map {
        val yValue = when (rsm) {
            ReportsStatisticsMode.Calories -> stepsToKcal(it.steps, weightKg)
            ReportsStatisticsMode.Distance -> stepsToDistance(it.steps, stepLengthCm, mus)
            ReportsStatisticsMode.Steps -> it.steps.toFloat()
            ReportsStatisticsMode.Time -> stepsToDurationSeconds(it.steps)
        }

        Entry(it.dayInstant.epochSeconds.toFloat(), yValue)
    }

    val dataSet = LineDataSet(lineChartPoints, "").apply {
        mode = LineDataSet.Mode.HORIZONTAL_BEZIER
        setDrawCircles(false)
        color = AppColor.Primary.toArgb()
        lineWidth = 2.5f
    }

    val chartData = LineData(dataSet).apply {
        setDrawValues(false)
    }

    var lineChart by remember { mutableStateOf<LineChart?>(null) }
    lineChart?.apply {
        axisLeft.apply {
            valueFormatter = when (rsm) {
                ReportsStatisticsMode.Time -> {
                    YAxisDurationValueFormatter
                }

                ReportsStatisticsMode.Steps -> {
                    YAxisIntValueFormatter
                }

                else -> {
                    YAxisFloatValueFormatter
                }
            }
            axisMaximum = yMax + yOffset
            axisMinimum = if (axisMaximum >= 1) -(axisMaximum * 0.01f) else -0.001f
        }

        xAxis.apply {
            val month = monthReportsData.monthInstant
                .toLocalDateTime(TimeZone.currentSystemDefault())
                .month

            valueFormatter = XAxisDayOfMonthValueFormatter(month)
            axisMinimum = monthReportsData
                .monthInstant
                .thisMonthStartInstant(TimeZone.currentSystemDefault())
                .epochSeconds.toFloat()
            axisMaximum = monthReportsData
                .monthInstant
                .plus(DateTimePeriod(months = 1), TimeZone.currentSystemDefault())
                .epochSeconds.toFloat() - if (month.value in lunarMonth) 0 else 86400

            debugLog("MRC axisMinimum -> ${axisMinimum.toLong()}")
            debugLog("MRC axisMaximum -> ${axisMaximum.toLong()}")
            debugLog("MRC axisMaximum - axisMinimum -> ${axisMaximum - axisMinimum}")
        }
        data = chartData
        notifyDataSetChanged()
        invalidate()
    }

    Box(modifier = modifier) {

        Column(modifier = Modifier.bodyWidth()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.text_month),
                    modifier = Modifier.weight(1f),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                )

                val (totalText, avgText) = when (rsm) {
                    ReportsStatisticsMode.Time -> {
                        Pair(
                            total.toInt().toDuration(DurationUnit.SECONDS).toHHmm(),
                            avg.toInt().toDuration(DurationUnit.SECONDS).toHHmm()
                        )
                    }

                    ReportsStatisticsMode.Steps -> {
                        total.toInt().toString() to avg.toInt().toString()
                    }

                    else -> {
                        total.scale(3).toString() to avg.scale(3).toString()
                    }
                }

                Text(
                    text = "${stringResource(id = R.string.text_total)}: $totalText    " +
                            "${stringResource(id = R.string.text_avg)}: $avgText"
                )
            }

            Column(modifier = Modifier.background(AppColor.PrimaryLightAlpha8)) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    Surface(
                        onClick = {
                            onMonthReportChange(
                                monthReportsData.monthInstant.minus(
                                    DateTimePeriod(months = 1),
                                    TimeZone.currentSystemDefault()
                                ),
                            )
                        },
                        shape = CircleShape,
                        color = Color.Black,
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.KeyboardArrowLeft,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = Color.White,
                        )
                    }


                    val monthText = monthReportsData
                        .monthInstant
                        .toLocalDateTime(TimeZone.currentSystemDefault())
                        .month
                        .displayName(style = TextStyle.FULL)

                    Text(
                        text = monthText,
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Bold,
                    )

                    Surface(
                        onClick = {
                            onMonthReportChange(
                                monthReportsData.monthInstant.plus(
                                    DateTimePeriod(months = 1),
                                    TimeZone.currentSystemDefault()
                                ),
                            )
                        },
                        shape = CircleShape,
                        color = Color.Black,
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.KeyboardArrowRight,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = Color.White,
                        )
                    }
                }

                AndroidView(
                    factory = {
                        LineChart(it).apply {
                            basicConfigure(rsm, yMax, yOffset)

                            xAxis.apply {
                                setLabelCount(7, true)

                                val month = monthReportsData.monthInstant
                                    .toLocalDateTime(TimeZone.currentSystemDefault())
                                    .month

                                valueFormatter = XAxisDayOfMonthValueFormatter(month)
                                axisMinimum = monthReportsData
                                    .monthInstant
                                    .thisMonthStartInstant(TimeZone.currentSystemDefault())
                                    .epochSeconds.toFloat()
                                axisMaximum = monthReportsData
                                    .monthInstant
                                    .plus(
                                        DateTimePeriod(months = 1),
                                        TimeZone.currentSystemDefault()
                                    )
                                    .epochSeconds.toFloat() - if (month.value in lunarMonth) 0 else 86400
                            }


                            data = chartData

                            invalidate()
                            lineChart = this
                        }
                    }, modifier = Modifier
                        .height(200.dp)
                        .padding(4.dp)
                )
            }
        }
    }
}

@Preview
@Composable
fun MonthReportsChartPreview() {
    val tz = TimeZone.currentSystemDefault()
    val now = nowInstant()
    val monthStartInstant = now.thisMonthStartInstant(tz)

    val dataList = arrayListOf<DayStepsData>()

    repeat(31) {
        dataList.add(
            DayStepsData(
                monthStartInstant.plus(DateTimePeriod(days = it), tz),
                it * 100
            )
        )
    }

    MonthReportsChart(
        monthReportsData = MonthReportsData(
            monthStartInstant,
            dataList
        ),
        2f,
        3f,
        rsm = ReportsStatisticsMode.Distance,
        mus = MeasurementUnit.Imperial,
        onMonthReportChange = {})
}
