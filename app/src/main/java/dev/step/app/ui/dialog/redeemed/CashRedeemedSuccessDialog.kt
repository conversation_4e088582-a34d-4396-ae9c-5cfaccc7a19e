package dev.step.app.ui.dialog.redeemed

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.ad.InterstitialAdFacade
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import org.koin.compose.koinInject

@Composable
fun CashRedeemedSuccessDialog(
    cashText: String,
    accountAddress: String,
    navUp: () -> Unit,
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()


    val interstitialAdFacade: InterstitialAdFacade = koinInject()
    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    LaunchedEffect(Unit) {
        globalMainActivity?.let {
            interstitialAdFacade.tryToLoadAd(it)
        }
    }

//    val _navUp = {
//        navUp()
//        interstitialAdHelper.tryToShowAd("exit_dialog_cash")
//    }

    AppDefWithCloseDialog(
        onDismiss = {
            OnBack()
            logEventRecord("exit_dialog_cash")
        },
        onClose = {
            OnBack()
            logEventRecord("exit_dialog_cash")
        },
        onConfirm = OnBack,
        confirmText = stringResource(id = R.string.text_ok),
        topPainter = painterResource(id = R.drawable.img_cash_4),
        adPlace = NativeAdPlace.Dialog,
        adPlaceName = "transfer_success"
    ) {
        Column(
            modifier = Modifier
                .bodyWidth()
                .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            BlankSpacer(height = 50.dp)

            Text(text = stringResource(R.string.cash_redeemed_successfully_title), fontSize = 20.sp)

            BlankSpacer(height = 14.dp)

            Text(
                text = buildAnnotatedString {
                    append(stringResource(R.string.cash_redeemed_successfully_content_you_are_sending))

                    withStyle(style = SpanStyle(color = AppColor.Primary, fontSize = 17.sp)) {
                        append(cashText)
                    }

                    append(stringResource(R.string.cash_redeemed_successfully_content_to))

                    withStyle(style = SpanStyle(color = AppColor.Primary, fontSize = 17.sp)) {
                        append(accountAddress)
                    }

                    append(" . ")
                    append(stringResource(R.string.cash_redeemed_successfully_content_you_can_expect_to_receive_the_money_in_7_days))
                },
                fontSize = 15.sp,
                textAlign = TextAlign.Center,
            )

            BlankSpacer(height = 20.dp)
        }
    }
}

@Preview
@Composable
private fun CashRedeemedSuccessDialogPreview() {
    AppTheme {
        CashRedeemedSuccessDialog(
            cashText = "12 BTC",
            accountAddress = "<EMAIL>",
            navUp = { /*TODO*/ })
    }
}