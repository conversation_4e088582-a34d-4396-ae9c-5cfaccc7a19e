package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcProfileHeight: ImageVector
    get() {
        if (_icProfileHight != null) {
            return _icProfileHight!!
        }
        _icProfileHight = Builder(name = "IcProfileHeight", defaultWidth = 70.0.dp, defaultHeight =
                70.0.dp, viewportWidth = 70.0f, viewportHeight = 70.0f).apply {
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 1.0f)
                lineTo(35.0f, 1.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 69.0f, 35.0f)
                lineTo(69.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 69.0f)
                lineTo(35.0f, 69.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 1.0f, 35.0f)
                lineTo(1.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 1.0f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(42.0f, 16.5f)
                lineTo(51.0f, 16.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 52.0f, 17.5f)
                lineTo(52.0f, 17.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 51.0f, 18.5f)
                lineTo(42.0f, 18.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 41.0f, 17.5f)
                lineTo(41.0f, 17.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 42.0f, 16.5f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(49.995f, 54.4744f)
                lineTo(49.995f, 17.4744f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 50.995f, 16.4744f)
                lineTo(50.995f, 16.4744f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 51.995f, 17.4744f)
                lineTo(51.995f, 54.4744f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 50.995f, 55.4744f)
                lineTo(50.995f, 55.4744f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 49.995f, 54.4744f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(45.0f, 40.5f)
                lineTo(51.0f, 40.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 52.0f, 41.5f)
                lineTo(52.0f, 41.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 51.0f, 42.5f)
                lineTo(45.0f, 42.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 44.0f, 41.5f)
                lineTo(44.0f, 41.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 45.0f, 40.5f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(45.0f, 28.5f)
                lineTo(51.0f, 28.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 52.0f, 29.5f)
                lineTo(52.0f, 29.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 51.0f, 30.5f)
                lineTo(45.0f, 30.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 44.0f, 29.5f)
                lineTo(44.0f, 29.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 45.0f, 28.5f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(42.0f, 53.5f)
                lineTo(51.0f, 53.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 52.0f, 54.5f)
                lineTo(52.0f, 54.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 51.0f, 55.5f)
                lineTo(42.0f, 55.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 41.0f, 54.5f)
                lineTo(41.0f, 54.5f)
                arcTo(1.0f, 1.0f, 0.0f, false, true, 42.0f, 53.5f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(28.3372f, 25.4473f)
                curveTo(28.938f, 25.442f, 29.5388f, 25.4372f, 30.1376f, 25.4602f)
                curveTo(31.0805f, 25.4963f, 31.8419f, 25.7396f, 32.4557f, 26.1053f)
                curveTo(33.179f, 26.5362f, 33.7117f, 27.1439f, 34.0731f, 27.8689f)
                curveTo(34.8418f, 29.4111f, 35.5767f, 30.9626f, 36.3116f, 32.5141f)
                curveTo(36.5935f, 33.1092f, 36.8753f, 33.7043f, 37.1562f, 34.2928f)
                curveTo(37.4851f, 34.974f, 37.6515f, 35.32f, 37.7989f, 35.6705f)
                curveTo(38.1395f, 36.481f, 38.04f, 37.2645f, 37.6401f, 37.9129f)
                curveTo(37.3113f, 38.446f, 36.7402f, 38.9163f, 35.9188f, 39.1367f)
                curveTo(35.236f, 39.3198f, 34.5315f, 39.2766f, 33.9233f, 39.0536f)
                curveTo(33.5846f, 38.9294f, 33.2761f, 38.7507f, 33.0103f, 38.5264f)
                lineTo(32.926f, 38.449f)
                lineTo(33.0177f, 39.4396f)
                lineTo(33.0177f, 39.4396f)
                lineTo(33.1406f, 40.7162f)
                curveTo(33.1789f, 41.112f, 33.2173f, 41.5079f, 33.255f, 41.9037f)
                curveTo(33.3401f, 42.7959f, 33.4291f, 43.6879f, 33.5181f, 44.5798f)
                curveTo(33.5394f, 44.7927f, 33.5606f, 45.0056f, 33.5818f, 45.2185f)
                curveTo(33.6337f, 45.7399f, 33.685f, 46.2614f, 33.7364f, 46.7829f)
                curveTo(33.8769f, 48.2103f, 34.0173f, 49.6377f, 34.1701f, 51.0644f)
                curveTo(34.2607f, 51.9118f, 34.024f, 52.6295f, 33.5785f, 53.177f)
                curveTo(33.1609f, 53.6902f, 32.5333f, 54.083f, 31.6749f, 54.2086f)
                curveTo(30.7935f, 54.3377f, 29.9879f, 54.1555f, 29.3815f, 53.798f)
                curveTo(28.6084f, 53.3423f, 28.1226f, 52.612f, 28.0345f, 51.7364f)
                curveTo(27.9197f, 50.5944f, 27.8128f, 49.4519f, 27.7058f, 48.3093f)
                curveTo(27.6379f, 47.5837f, 27.57f, 46.858f, 27.5f, 46.1324f)
                lineTo(27.588f, 47.06f)
                lineTo(27.5759f, 47.2412f)
                curveTo(27.5274f, 47.903f, 27.4763f, 48.5647f, 27.4238f, 49.2263f)
                lineTo(27.2636f, 51.2111f)
                curveTo(27.2443f, 51.4482f, 27.225f, 51.6854f, 27.2058f, 51.9226f)
                curveTo(27.1453f, 52.6679f, 26.7641f, 53.3439f, 26.1195f, 53.8193f)
                curveTo(25.6141f, 54.1919f, 24.9356f, 54.4374f, 24.1725f, 54.4428f)
                curveTo(23.4246f, 54.4477f, 22.6974f, 54.1858f, 22.1614f, 53.7778f)
                curveTo(21.4625f, 53.2458f, 21.097f, 52.4966f, 21.1035f, 51.7435f)
                curveTo(21.1096f, 51.0134f, 21.159f, 50.2831f, 21.2091f, 49.5552f)
                curveTo(21.2961f, 48.2663f, 21.3668f, 47.3101f, 21.4376f, 46.354f)
                curveTo(21.4647f, 45.9875f, 21.4918f, 45.621f, 21.5185f, 45.2544f)
                curveTo(21.5739f, 44.4968f, 21.6299f, 43.7392f, 21.686f, 42.9816f)
                lineTo(21.8812f, 40.3284f)
                lineTo(21.8812f, 40.3284f)
                lineTo(21.997f, 38.688f)
                lineTo(21.9386f, 38.7394f)
                lineTo(21.9386f, 38.7394f)
                lineTo(21.7745f, 38.8613f)
                curveTo(21.3942f, 39.1233f, 20.9171f, 39.3167f, 20.3049f, 39.3793f)
                curveTo(19.2249f, 39.4897f, 18.335f, 39.1191f, 17.7793f, 38.5922f)
                curveTo(17.3947f, 38.2276f, 17.1469f, 37.7778f, 17.0478f, 37.2893f)
                curveTo(16.9547f, 36.8304f, 16.9882f, 36.3275f, 17.2106f, 35.811f)
                curveTo(17.8537f, 34.3178f, 18.5077f, 32.8273f, 19.1617f, 31.3367f)
                curveTo(19.434f, 30.7161f, 19.7063f, 30.0956f, 19.9778f, 29.4747f)
                curveTo(20.0818f, 29.2369f, 20.1852f, 28.999f, 20.2885f, 28.761f)
                lineTo(20.2885f, 28.761f)
                lineTo(20.534f, 28.1964f)
                curveTo(20.922f, 27.3072f, 21.5771f, 26.6003f, 22.4537f, 26.1208f)
                curveTo(23.2242f, 25.6993f, 24.1793f, 25.4541f, 25.2805f, 25.4519f)
                lineTo(25.2805f, 25.4519f)
                lineTo(27.5336f, 25.4516f)
                curveTo(27.8013f, 25.4521f, 28.0693f, 25.4497f, 28.3372f, 25.4473f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(27.2143f, 21.0f)
                moveToRelative(-4.5f, 0.0f)
                arcToRelative(4.5f, 4.5f, 0.0f, true, true, 9.0f, 0.0f)
                arcToRelative(4.5f, 4.5f, 0.0f, true, true, -9.0f, 0.0f)
            }
        }
        .build()
        return _icProfileHight!!
    }

private var _icProfileHight: ImageVector? = null
