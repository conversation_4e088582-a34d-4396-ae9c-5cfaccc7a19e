package dev.step.app.ui.screen.withdraw

import android.os.Parcelable
import dev.step.app.data.pojo.remoteconfig.CoinsExchangeUnit
import kotlinx.parcelize.Parcelize


sealed interface RedeemedPrize : Parcelable {
    @Parcelize
    data class Cash(val coinPrice: Int, val amount: Int) : RedeemedPrize

    @Parcelize
    data class Coupon(val coinPrice: Int, val off: Int) : RedeemedPrize
}

data class WithdrawViewState(
    val coinBalance: Int? = null,
    val redeemedPrizes: List<RedeemedPrize> = emptyList(),
    val isOrganic: Boolean? = null,

    val coinsExchangeUnit: CoinsExchangeUnit? = null
) {
    companion object {
        val Empty = WithdrawViewState()
    }
}
