package dev.step.app.ui.theme.appimg

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Brush.Companion.linearGradient
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcStepDistance: ImageVector
    get() {
        if (_icStepDistance != null) {
            return _icStepDistance!!
        }
        _icStepDistance = Builder(name = "IcStepDistance", defaultWidth = 50.0.dp, defaultHeight =
                50.0.dp, viewportWidth = 50.0f, viewportHeight = 50.0f).apply {
            path(fill = linearGradient(0.0f to Color(0xFF000000), 1.0f to Color(0xFF000000), start =
                    Offset(25.479101f,2.0f), end = Offset(25.479101f,48.0f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(49.0f, 4.1379f)
                lineTo(49.0f, 4.1468f)
                lineTo(48.8944f, 27.5151f)
                lineTo(48.8256f, 27.7756f)
                curveTo(48.7247f, 28.1439f, 48.3484f, 29.0916f, 47.0544f, 29.8102f)
                curveTo(45.9991f, 30.3941f, 44.5629f, 30.6905f, 42.7871f, 30.6905f)
                lineTo(42.5622f, 30.6905f)
                curveTo(40.3185f, 30.659f, 37.4644f, 30.1695f, 34.0735f, 29.2398f)
                curveTo(32.0224f, 28.6784f, 30.1824f, 28.4448f, 28.5397f, 28.4448f)
                curveTo(24.5293f, 28.4448f, 21.7028f, 29.8282f, 19.8949f, 31.18f)
                curveTo(18.6514f, 32.1097f, 18.2201f, 33.7536f, 18.8579f, 35.1504f)
                lineTo(23.373f, 45.0717f)
                curveTo(23.8502f, 46.1136f, 23.3638f, 47.3533f, 22.2947f, 47.8203f)
                curveTo(22.0148f, 47.9416f, 21.7257f, 48.0f, 21.4366f, 48.0f)
                curveTo(20.6199f, 48.0f, 19.8352f, 47.5374f, 19.4865f, 46.7649f)
                lineTo(2.1831f, 8.728f)
                curveTo(1.9904f, 8.3058f, 1.9537f, 7.8477f, 2.0546f, 7.421f)
                curveTo(2.0822f, 7.2548f, 2.1235f, 7.0887f, 2.1877f, 6.927f)
                curveTo(2.4263f, 6.3161f, 2.9173f, 5.8086f, 3.5276f, 5.5436f)
                curveTo(5.3079f, 4.7622f, 8.162f, 3.6663f, 11.6768f, 2.9028f)
                curveTo(14.3336f, 2.3234f, 16.8848f, 2.0314f, 19.3167f, 2.0314f)
                curveTo(21.6752f, 2.0314f, 23.9236f, 2.3054f, 26.0389f, 2.8489f)
                curveTo(31.1964f, 4.1738f, 34.8719f, 4.6948f, 37.6342f, 4.6948f)
                curveTo(41.5849f, 4.6948f, 43.6864f, 3.6348f, 45.632f, 2.3593f)
                lineTo(45.7238f, 2.3009f)
                curveTo(46.0633f, 2.0988f, 46.4487f, 2.0f, 46.8296f, 2.0f)
                curveTo(47.1967f, 2.0f, 47.5638f, 2.0898f, 47.8941f, 2.274f)
                curveTo(48.5778f, 2.6557f, 49.0045f, 3.3699f, 49.0f, 4.1379f)
                close()
            }
            path(fill = linearGradient(0.0f to Color(0xFFFF844D), 1.0f to Color(0xFFF75927), start =
                    Offset(25.479101f,2.0f), end = Offset(25.479101f,46.82198f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(49.0f, 4.1379f)
                lineTo(49.0f, 4.1468f)
                lineTo(48.8944f, 27.5151f)
                lineTo(48.8256f, 27.7756f)
                curveTo(48.7247f, 28.1439f, 48.3484f, 29.0916f, 47.0544f, 29.8102f)
                curveTo(45.9991f, 30.3941f, 44.5629f, 30.6905f, 42.7871f, 30.6905f)
                lineTo(42.5622f, 30.6905f)
                curveTo(40.3185f, 30.659f, 37.4644f, 30.1695f, 34.0735f, 29.2398f)
                curveTo(32.0224f, 28.6784f, 30.1824f, 28.4448f, 28.5397f, 28.4448f)
                curveTo(24.5293f, 28.4448f, 21.7028f, 29.8282f, 19.8949f, 31.18f)
                curveTo(18.6514f, 32.1097f, 18.2201f, 33.7536f, 18.8579f, 35.1504f)
                lineTo(23.373f, 45.0717f)
                curveTo(23.8502f, 46.1136f, 23.3638f, 47.3533f, 22.2947f, 47.8203f)
                curveTo(22.0148f, 47.9416f, 21.7257f, 48.0f, 21.4366f, 48.0f)
                curveTo(20.6199f, 48.0f, 19.8352f, 47.5374f, 19.4865f, 46.7649f)
                lineTo(2.1831f, 8.728f)
                curveTo(1.9904f, 8.3058f, 1.9537f, 7.8477f, 2.0546f, 7.421f)
                curveTo(2.0822f, 7.2548f, 2.1235f, 7.0887f, 2.1877f, 6.927f)
                curveTo(2.4263f, 6.3161f, 2.9173f, 5.8086f, 3.5276f, 5.5436f)
                curveTo(5.3079f, 4.7622f, 8.162f, 3.6663f, 11.6768f, 2.9028f)
                curveTo(14.3336f, 2.3234f, 16.8848f, 2.0314f, 19.3167f, 2.0314f)
                curveTo(21.6752f, 2.0314f, 23.9236f, 2.3054f, 26.0389f, 2.8489f)
                curveTo(31.1964f, 4.1738f, 34.8719f, 4.6948f, 37.6342f, 4.6948f)
                curveTo(41.5849f, 4.6948f, 43.6864f, 3.6348f, 45.632f, 2.3593f)
                lineTo(45.7238f, 2.3009f)
                curveTo(46.0633f, 2.0988f, 46.4487f, 2.0f, 46.8296f, 2.0f)
                curveTo(47.1967f, 2.0f, 47.5638f, 2.0898f, 47.8941f, 2.274f)
                curveTo(48.5778f, 2.6557f, 49.0045f, 3.3699f, 49.0f, 4.1379f)
                close()
            }
        }
        .build()
        return _icStepDistance!!
    }

private var _icStepDistance: ImageVector? = null
