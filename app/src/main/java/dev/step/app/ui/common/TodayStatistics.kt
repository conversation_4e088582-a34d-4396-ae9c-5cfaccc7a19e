package dev.step.app.ui.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.mikhaellopez.circularprogressbar.CircularProgressBar
import dev.step.app.R
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.toHHmm
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.pojo.DayStepsData
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppImg
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.RoundedCornerShape7Dp
import dev.step.app.ui.theme.appimg.IcStepDistance
import dev.step.app.ui.theme.appimg.IcStepEdit
import dev.step.app.ui.theme.appimg.IcStepKcal
import dev.step.app.ui.theme.noRippleClickable
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@Composable
fun TodayStatistics(
    todayStepsData: DayStepsData,
    goalSteps: Int,
    distanceMi: Float,
    distanceKm: Float,
    kcal: Float,
    duration: Duration,
    mus: MeasurementUnit,
    openGoalStepChangeDialog: () -> Unit,
    onExchange: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val (distanceValue, distanceUnitValue) = when (mus) {
        MeasurementUnit.Metric -> distanceKm.toString() to "km"
        MeasurementUnit.Imperial -> distanceMi.toString() to "mi"
    }

    val todayGoalPercent = todayStepsData.steps / goalSteps.toFloat()

    Box(
        modifier = modifier
    ) {

        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {

            Box(modifier = Modifier.size(192.dp), contentAlignment = Alignment.Center) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Image(
                        painter = rememberVectorPainter(image = AppImg.IcStepEdit),
                        contentDescription = null,
                        modifier = Modifier
                            .size(30.dp)
                            .noRippleClickable(onClick = openGoalStepChangeDialog)
                    )

                    BlankSpacer(height = 4.dp)

                    Text(
                        text = todayStepsData.steps.toString(),
                        fontWeight = FontWeight.ExtraBold,
                        fontSize = 29.sp
                    )


                    Text(text = stringResource(id = R.string.text_steps), fontSize = 12.sp)
                }

                AndroidView(
                    factory = {
                        CircularProgressBar(it).apply {
                            backgroundProgressBarColor = AppColor.PrimaryLightAlpha8.toArgb()
                            backgroundProgressBarWidth = 32f

                            progressBarColorStart = AppColor.Primary.toArgb()
                            progressBarColorEnd = AppColor.PrimaryLight.toArgb()
                            progressBarWidth = 16f

                            progressMax = 1f
                            progress = todayGoalPercent

                            roundBorder = true
                        }
                    },
                    update = {
                        it.progress = todayGoalPercent
                    }
                )
            }

            BlankSpacer(height = 14.dp)

            Text(
                text = stringResource(R.string.exchange_steps_to_coins_text),
                color = AppColor.TextColorGray.copy(.7f),
                fontSize = 11.sp
            )
            FancyFadedBgButton(
                text = stringResource(id = R.string.text_exchange_coins),
                onClick = onExchange,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 96.dp)
                    .scale(.82f)
            )

            BlankSpacer(height = 15.dp)


            Row {
                BlankSpacer(width = 18.dp)

                StatisticItem(
                    painter = rememberVectorPainter(image = AppImg.IcStepDistance),
                    value = distanceValue,
                    unitName = distanceUnitValue,
                    modifier = Modifier.weight(1f)
                )

                BlankSpacer(width = 12.dp)

                StatisticItem(
                    painter = rememberVectorPainter(image = AppImg.IcStepKcal),
                    value = kcal.toString(),
                    unitName = "kcal",
                    modifier = Modifier.weight(1f)
                )

                BlankSpacer(width = 12.dp)

                StatisticItem(
                    painter = painterResource(id = R.drawable.ic_today_stat_duration),
                    value = duration.toHHmm(),
                    unitName = "h",
                    modifier = Modifier.weight(1f)
                )

                BlankSpacer(width = 18.dp)
            }
        }
    }
}

@Composable
private fun StatisticItem(
    painter: Painter,
    value: String,
    unitName: String,
    modifier: Modifier = Modifier
) {
    val adaptiveValueFontSize = when (value.length) {
        1, 2, 3 -> 17.sp
        4 -> 16.sp
        5 -> 15.sp
        6 -> 14.sp
        else -> 12.sp
    }

    val valueAndUnit = buildAnnotatedString {
        withStyle(
            MaterialTheme.typography.body1.copy(
                fontWeight = FontWeight.Bold, fontSize = adaptiveValueFontSize
            ).toSpanStyle()
        ) {
            append(value)
        }
        withStyle(
            MaterialTheme.typography.body2.copy(fontWeight = FontWeight.Medium, fontSize = 13.5.sp)
                .toSpanStyle()
        ) {
            append(" $unitName")
        }
    }

    Surface(modifier, color = AppColor.PrimaryLightAlpha8, shape = RoundedCornerShape7Dp) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.height(42.dp)
        ) {
            Image(painter = painter, contentDescription = null, modifier = Modifier.size(20.dp))
            BlankSpacer(width = 6.dp)
            Text(text = valueAndUnit, lineHeight = 18.sp, modifier = Modifier.padding(top = 3.dp))
        }
    }

}

@Preview(device = Devices.PIXEL_4)
@Composable
fun TodayStatisticsPreview() {
    AppTheme {
        TodayStatistics(
            todayStepsData = DayStepsData(nowInstant(), 5000),
            goalSteps = 7000,
            distanceMi = 4.4f,
            distanceKm = 5.5f,
            kcal = 400.1f,
            duration = 911.toDuration(DurationUnit.MINUTES),
            mus = MeasurementUnit.Imperial,
            openGoalStepChangeDialog = { },
            onExchange = {}
        )
    }
}
