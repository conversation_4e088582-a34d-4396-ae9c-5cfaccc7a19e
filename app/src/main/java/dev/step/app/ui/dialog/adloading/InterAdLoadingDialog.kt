package dev.step.app.ui.dialog.adloading

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.roudikk.guia.backstack.NavBackHandler
import dev.step.app.R
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme

@Composable
fun InterAdLoadingDialog() {
    NavBackHandler {

    }

    Dialog(
        onDismissRequest = {},
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {

            Surface(
                shape = RoundedCornerShape(10.dp),
                elevation = 0.dp
            ) {
                Column(
                    Modifier.padding(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(color = AppColor.Primary)
                    BlankSpacer(height = 8.dp)
                    Text(
                        text = stringResource(R.string.text_loading_ad),
                        fontSize = 11.sp,
                        color = AppColor.TextColorGray.copy(.7f)
                    )
                }
            }
        }
    }

}

@Preview
@Composable
private fun InterAdLoadingDialogPreview() {
    AppTheme {
        InterAdLoadingDialog()
    }
}