package dev.step.app.ui.theme.appimg

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Brush.Companion.linearGradient
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.ImgNotiTips: ImageVector
    get() {
        if (_imgNotiTips != null) {
            return _imgNotiTips!!
        }
        _imgNotiTips = Builder(name = "ImgNotiTips", defaultWidth = 280.0.dp, defaultHeight =
                280.0.dp, viewportWidth = 280.0f, viewportHeight = 280.0f).apply {
            path(fill = SolidColor(Color(0xFFFFF1E9)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(144.5f, 254.0f)
                curveTo(210.498f, 254.0f, 264.0f, 200.498f, 264.0f, 134.5f)
                curveTo(264.0f, 68.502f, 210.498f, 15.0f, 144.5f, 15.0f)
                curveTo(78.502f, 15.0f, 25.0f, 68.502f, 25.0f, 134.5f)
                curveTo(25.0f, 200.498f, 78.502f, 254.0f, 144.5f, 254.0f)
                curveTo(144.5f, 254.0f, 144.5f, 254.0f, 144.5f, 254.0f)
                close()
            }
            path(fill = linearGradient(0.0f to Color(0xFFFF9999), 1.0f to Color(0xFFFFE7DA), start =
                    Offset(168.27187f,40.04596f), end = Offset(127.5099f,22.323282f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(144.867f, 28.421f)
                curveTo(144.275f, 28.178f, 143.641f, 28.054f, 143.001f, 28.055f)
                curveTo(143.001f, 28.055f, 142.996f, 28.055f, 142.996f, 28.055f)
                curveTo(141.703f, 28.056f, 140.464f, 28.567f, 139.55f, 29.476f)
                curveTo(138.637f, 30.384f, 138.123f, 31.616f, 138.121f, 32.9f)
                curveTo(138.121f, 32.9f, 138.121f, 39.0f, 138.121f, 39.0f)
                curveTo(138.121f, 39.0f, 126.0f, 39.0f, 126.0f, 39.0f)
                curveTo(126.0f, 39.0f, 126.0f, 32.892f, 126.0f, 32.892f)
                curveTo(126.004f, 28.413f, 127.795f, 24.119f, 130.979f, 20.952f)
                curveTo(134.163f, 17.785f, 138.48f, 16.005f, 142.983f, 16.0f)
                curveTo(142.983f, 16.0f, 142.985f, 16.0f, 142.985f, 16.0f)
                curveTo(145.217f, 15.997f, 147.428f, 16.431f, 149.491f, 17.278f)
                curveTo(151.556f, 18.125f, 153.432f, 19.369f, 155.013f, 20.939f)
                curveTo(156.594f, 22.508f, 157.848f, 24.372f, 158.704f, 26.424f)
                curveTo(159.56f, 28.476f, 160.0f, 30.676f, 160.0f, 32.897f)
                curveTo(160.0f, 32.897f, 160.0f, 38.774f, 160.0f, 38.774f)
                curveTo(160.0f, 38.774f, 147.879f, 38.774f, 147.879f, 38.774f)
                curveTo(147.879f, 38.774f, 147.879f, 32.897f, 147.879f, 32.897f)
                curveTo(147.879f, 32.261f, 147.752f, 31.63f, 147.507f, 31.042f)
                curveTo(147.262f, 30.454f, 146.903f, 29.92f, 146.45f, 29.47f)
                curveTo(145.997f, 29.02f, 145.459f, 28.664f, 144.867f, 28.421f)
                curveTo(144.867f, 28.421f, 144.867f, 28.421f, 144.867f, 28.421f)
                close()
            }
            path(fill = linearGradient(0.0f to Color(0xFFFFBAA4), 1.0f to Color(0xFFFF844D), start =
                    Offset(143.0f,16.137094f), end = Offset(143.0f,38.41091f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(144.867f, 28.421f)
                curveTo(144.275f, 28.178f, 143.641f, 28.054f, 143.001f, 28.055f)
                curveTo(143.001f, 28.055f, 142.996f, 28.055f, 142.996f, 28.055f)
                curveTo(141.703f, 28.056f, 140.464f, 28.567f, 139.55f, 29.476f)
                curveTo(138.637f, 30.384f, 138.123f, 31.616f, 138.121f, 32.9f)
                curveTo(138.121f, 32.9f, 138.121f, 39.0f, 138.121f, 39.0f)
                curveTo(138.121f, 39.0f, 126.0f, 39.0f, 126.0f, 39.0f)
                curveTo(126.0f, 39.0f, 126.0f, 32.892f, 126.0f, 32.892f)
                curveTo(126.004f, 28.413f, 127.795f, 24.119f, 130.979f, 20.952f)
                curveTo(134.163f, 17.785f, 138.48f, 16.005f, 142.983f, 16.0f)
                curveTo(142.983f, 16.0f, 142.985f, 16.0f, 142.985f, 16.0f)
                curveTo(145.217f, 15.997f, 147.428f, 16.431f, 149.491f, 17.278f)
                curveTo(151.556f, 18.125f, 153.432f, 19.369f, 155.013f, 20.939f)
                curveTo(156.594f, 22.508f, 157.848f, 24.372f, 158.704f, 26.424f)
                curveTo(159.56f, 28.476f, 160.0f, 30.676f, 160.0f, 32.897f)
                curveTo(160.0f, 32.897f, 160.0f, 38.774f, 160.0f, 38.774f)
                curveTo(160.0f, 38.774f, 147.879f, 38.774f, 147.879f, 38.774f)
                curveTo(147.879f, 38.774f, 147.879f, 32.897f, 147.879f, 32.897f)
                curveTo(147.879f, 32.261f, 147.752f, 31.63f, 147.507f, 31.042f)
                curveTo(147.262f, 30.454f, 146.903f, 29.92f, 146.45f, 29.47f)
                curveTo(145.997f, 29.02f, 145.459f, 28.664f, 144.867f, 28.421f)
                curveTo(144.867f, 28.421f, 144.867f, 28.421f, 144.867f, 28.421f)
                close()
            }
            path(fill = linearGradient(0.0f to Color(0xFFFF844D), 1.0f to Color(0xFFF75927), start =
                    Offset(143.5f,204.001f), end = Offset(143.5f,231.28297f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(177.31f, 204.001f)
                curveTo(174.113f, 219.976f, 160.191f, 232.0f, 143.5f, 232.0f)
                curveTo(126.809f, 232.0f, 112.887f, 219.976f, 109.69f, 204.001f)
                close()
            }
            path(fill = linearGradient(0.0f to Color(0xFFFFBAA4), 1.0f to Color(0xFFFF844D), start =
                    Offset(143.4975f,35.980534f), end = Offset(143.4975f,191.87692f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(216.009f, 107.647f)
                curveTo(216.009f, 88.38f, 208.37f, 69.902f, 194.773f, 56.278f)
                curveTo(181.176f, 42.654f, 162.734f, 35.0f, 143.505f, 35.0f)
                curveTo(124.276f, 35.0f, 105.834f, 42.654f, 92.237f, 56.278f)
                curveTo(78.64f, 69.902f, 71.001f, 88.38f, 71.001f, 107.647f)
                curveTo(71.001f, 107.647f, 71.001f, 142.971f, 71.001f, 142.971f)
                curveTo(71.002f, 151.086f, 68.71f, 159.036f, 64.392f, 165.902f)
                curveTo(60.074f, 172.768f, 53.904f, 178.269f, 46.597f, 181.769f)
                curveTo(44.918f, 182.57f, 43.5f, 183.834f, 42.509f, 185.411f)
                curveTo(41.518f, 186.988f, 40.995f, 188.815f, 41.0f, 190.679f)
                curveTo(41.0f, 190.679f, 41.0f, 190.87f, 41.0f, 190.87f)
                curveTo(41.0f, 191.543f, 41.13f, 192.21f, 41.386f, 192.833f)
                curveTo(41.643f, 193.455f, 42.019f, 194.02f, 42.493f, 194.497f)
                curveTo(42.968f, 194.974f, 43.532f, 195.351f, 44.152f, 195.609f)
                curveTo(44.773f, 195.867f, 45.438f, 196.0f, 46.11f, 196.0f)
                curveTo(46.11f, 196.0f, 240.891f, 196.0f, 240.891f, 196.0f)
                curveTo(241.562f, 196.0f, 242.227f, 195.867f, 242.848f, 195.609f)
                curveTo(243.468f, 195.351f, 244.033f, 194.974f, 244.507f, 194.497f)
                curveTo(244.982f, 194.02f, 245.357f, 193.455f, 245.614f, 192.833f)
                curveTo(245.871f, 192.21f, 246.0f, 191.543f, 246.0f, 190.87f)
                curveTo(246.0f, 190.87f, 246.0f, 190.679f, 246.0f, 190.679f)
                curveTo(246.0f, 188.821f, 245.476f, 187.001f, 244.487f, 185.429f)
                curveTo(243.498f, 183.858f, 242.086f, 182.598f, 240.413f, 181.797f)
                curveTo(233.101f, 178.295f, 226.929f, 172.79f, 222.61f, 165.918f)
                curveTo(218.291f, 159.048f, 216.003f, 151.092f, 216.009f, 142.971f)
                curveTo(216.009f, 142.971f, 216.009f, 107.647f, 216.009f, 107.647f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(273.901f, 98.016f)
                curveTo(273.955f, 97.92f, 273.988f, 97.813f, 273.997f, 97.704f)
                curveTo(274.007f, 97.594f, 273.992f, 97.484f, 273.956f, 97.381f)
                curveTo(273.919f, 97.277f, 273.86f, 97.182f, 273.784f, 97.102f)
                curveTo(273.707f, 97.023f, 273.614f, 96.96f, 273.511f, 96.918f)
                curveTo(270.508f, 95.736f, 259.882f, 92.228f, 254.075f, 99.301f)
                curveTo(250.19f, 103.949f, 244.698f, 107.014f, 238.659f, 107.903f)
                curveTo(238.507f, 107.923f, 238.365f, 107.988f, 238.251f, 108.089f)
                curveTo(238.137f, 108.19f, 238.057f, 108.323f, 238.021f, 108.47f)
                curveTo(237.985f, 108.618f, 237.995f, 108.772f, 238.05f, 108.913f)
                curveTo(238.104f, 109.055f, 238.201f, 109.177f, 238.327f, 109.263f)
                curveTo(243.459f, 112.856f, 260.414f, 122.011f, 273.901f, 98.016f)
                curveTo(273.901f, 98.016f, 273.901f, 98.016f, 273.901f, 98.016f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(47.922f, 148.378f)
                curveTo(47.965f, 148.302f, 47.991f, 148.218f, 47.998f, 148.132f)
                curveTo(48.005f, 148.045f, 47.994f, 147.958f, 47.964f, 147.877f)
                curveTo(47.934f, 147.795f, 47.887f, 147.72f, 47.826f, 147.657f)
                curveTo(47.764f, 147.594f, 47.689f, 147.544f, 47.607f, 147.511f)
                curveTo(45.193f, 146.588f, 36.616f, 143.821f, 31.932f, 149.356f)
                curveTo(28.806f, 153.023f, 24.389f, 155.444f, 19.529f, 156.154f)
                curveTo(19.407f, 156.17f, 19.293f, 156.221f, 19.202f, 156.301f)
                curveTo(19.11f, 156.38f, 19.046f, 156.485f, 19.017f, 156.6f)
                curveTo(18.988f, 156.716f, 18.996f, 156.838f, 19.04f, 156.949f)
                curveTo(19.084f, 157.06f, 19.161f, 157.156f, 19.262f, 157.224f)
                curveTo(23.374f, 160.102f, 37.046f, 167.334f, 47.922f, 148.378f)
                curveTo(47.922f, 148.378f, 47.922f, 148.378f, 47.922f, 148.378f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(251.066f, 138.056f)
                curveTo(251.03f, 137.99f, 251.008f, 137.918f, 251.002f, 137.843f)
                curveTo(250.996f, 137.769f, 251.005f, 137.694f, 251.03f, 137.623f)
                curveTo(251.055f, 137.553f, 251.095f, 137.489f, 251.147f, 137.435f)
                curveTo(251.198f, 137.381f, 251.261f, 137.339f, 251.329f, 137.312f)
                curveTo(253.327f, 136.502f, 260.403f, 134.109f, 264.267f, 138.933f)
                curveTo(266.856f, 142.106f, 270.515f, 144.198f, 274.537f, 144.805f)
                curveTo(274.644f, 144.815f, 274.745f, 144.859f, 274.826f, 144.93f)
                curveTo(274.907f, 145.002f, 274.963f, 145.097f, 274.987f, 145.203f)
                curveTo(275.011f, 145.308f, 275.001f, 145.419f, 274.96f, 145.519f)
                curveTo(274.918f, 145.619f, 274.846f, 145.703f, 274.754f, 145.759f)
                curveTo(271.343f, 148.228f, 260.045f, 154.472f, 251.066f, 138.056f)
                curveTo(251.066f, 138.056f, 251.066f, 138.056f, 251.066f, 138.056f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(5.106f, 106.332f)
                curveTo(5.048f, 106.225f, 5.012f, 106.108f, 5.003f, 105.986f)
                curveTo(4.993f, 105.865f, 5.009f, 105.743f, 5.051f, 105.629f)
                curveTo(5.092f, 105.514f, 5.157f, 105.409f, 5.242f, 105.321f)
                curveTo(5.327f, 105.232f, 5.43f, 105.162f, 5.545f, 105.116f)
                curveTo(8.879f, 103.815f, 20.681f, 99.939f, 27.13f, 107.746f)
                curveTo(31.449f, 112.893f, 37.559f, 116.281f, 44.276f, 117.256f)
                curveTo(44.444f, 117.28f, 44.601f, 117.353f, 44.726f, 117.466f)
                curveTo(44.851f, 117.579f, 44.938f, 117.726f, 44.977f, 117.889f)
                curveTo(45.016f, 118.051f, 45.005f, 118.221f, 44.944f, 118.377f)
                curveTo(44.884f, 118.533f, 44.777f, 118.667f, 44.638f, 118.763f)
                curveTo(38.913f, 122.731f, 20.081f, 132.859f, 5.106f, 106.332f)
                curveTo(5.106f, 106.332f, 5.106f, 106.332f, 5.106f, 106.332f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(45.541f, 75.454f)
                curveTo(45.213f, 74.165f, 46.403f, 73.003f, 47.684f, 73.363f)
                curveTo(47.684f, 73.363f, 56.328f, 75.791f, 56.328f, 75.791f)
                curveTo(57.609f, 76.151f, 58.02f, 77.762f, 57.068f, 78.691f)
                curveTo(57.068f, 78.691f, 50.64f, 84.964f, 50.64f, 84.964f)
                curveTo(49.688f, 85.894f, 48.087f, 85.444f, 47.759f, 84.155f)
                curveTo(47.759f, 84.155f, 45.541f, 75.454f, 45.541f, 75.454f)
                curveTo(45.541f, 75.454f, 45.541f, 75.454f, 45.541f, 75.454f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(243.98f, 33.59f)
                curveTo(244.492f, 31.47f, 247.508f, 31.47f, 248.02f, 33.59f)
                curveTo(248.02f, 33.59f, 249.742f, 40.726f, 249.742f, 40.726f)
                curveTo(249.925f, 41.483f, 250.517f, 42.075f, 251.274f, 42.258f)
                curveTo(251.274f, 42.258f, 258.41f, 43.98f, 258.41f, 43.98f)
                curveTo(260.53f, 44.492f, 260.53f, 47.508f, 258.41f, 48.02f)
                curveTo(258.41f, 48.02f, 251.274f, 49.742f, 251.274f, 49.742f)
                curveTo(250.517f, 49.925f, 249.925f, 50.517f, 249.742f, 51.274f)
                curveTo(249.742f, 51.274f, 248.02f, 58.41f, 248.02f, 58.41f)
                curveTo(247.508f, 60.53f, 244.492f, 60.53f, 243.98f, 58.41f)
                curveTo(243.98f, 58.41f, 242.258f, 51.274f, 242.258f, 51.274f)
                curveTo(242.075f, 50.517f, 241.483f, 49.925f, 240.726f, 49.742f)
                curveTo(240.726f, 49.742f, 233.59f, 48.02f, 233.59f, 48.02f)
                curveTo(231.47f, 47.508f, 231.47f, 44.492f, 233.59f, 43.98f)
                curveTo(233.59f, 43.98f, 240.726f, 42.258f, 240.726f, 42.258f)
                curveTo(241.483f, 42.075f, 242.075f, 41.483f, 242.258f, 40.726f)
                curveTo(242.258f, 40.726f, 243.98f, 33.59f, 243.98f, 33.59f)
                curveTo(243.98f, 33.59f, 243.98f, 33.59f, 243.98f, 33.59f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(96.98f, 238.59f)
                curveTo(97.492f, 236.47f, 100.508f, 236.47f, 101.02f, 238.59f)
                curveTo(101.02f, 238.59f, 102.742f, 245.726f, 102.742f, 245.726f)
                curveTo(102.925f, 246.483f, 103.517f, 247.075f, 104.274f, 247.258f)
                curveTo(104.274f, 247.258f, 111.41f, 248.98f, 111.41f, 248.98f)
                curveTo(113.53f, 249.492f, 113.53f, 252.508f, 111.41f, 253.02f)
                curveTo(111.41f, 253.02f, 104.274f, 254.742f, 104.274f, 254.742f)
                curveTo(103.517f, 254.925f, 102.925f, 255.517f, 102.742f, 256.274f)
                curveTo(102.742f, 256.274f, 101.02f, 263.41f, 101.02f, 263.41f)
                curveTo(100.508f, 265.53f, 97.492f, 265.53f, 96.98f, 263.41f)
                curveTo(96.98f, 263.41f, 95.258f, 256.274f, 95.258f, 256.274f)
                curveTo(95.075f, 255.517f, 94.483f, 254.925f, 93.726f, 254.742f)
                curveTo(93.726f, 254.742f, 86.59f, 253.02f, 86.59f, 253.02f)
                curveTo(84.47f, 252.508f, 84.47f, 249.492f, 86.59f, 248.98f)
                curveTo(86.59f, 248.98f, 93.726f, 247.258f, 93.726f, 247.258f)
                curveTo(94.483f, 247.075f, 95.075f, 246.483f, 95.258f, 245.726f)
                curveTo(95.258f, 245.726f, 96.98f, 238.59f, 96.98f, 238.59f)
                curveTo(96.98f, 238.59f, 96.98f, 238.59f, 96.98f, 238.59f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(64.0f, 234.0f)
                curveTo(67.314f, 234.0f, 70.0f, 231.314f, 70.0f, 228.0f)
                curveTo(70.0f, 224.686f, 67.314f, 222.0f, 64.0f, 222.0f)
                curveTo(60.686f, 222.0f, 58.0f, 224.686f, 58.0f, 228.0f)
                curveTo(58.0f, 231.314f, 60.686f, 234.0f, 64.0f, 234.0f)
                close()
                moveTo(66.424f, 225.576f)
                curveTo(67.094f, 226.245f, 67.429f, 227.053f, 67.429f, 228.0f)
                curveTo(67.429f, 228.947f, 67.094f, 229.755f, 66.424f, 230.424f)
                curveTo(65.755f, 231.094f, 64.947f, 231.429f, 64.0f, 231.429f)
                curveTo(63.053f, 231.429f, 62.245f, 231.094f, 61.576f, 230.424f)
                curveTo(60.906f, 229.755f, 60.571f, 228.947f, 60.571f, 228.0f)
                curveTo(60.571f, 227.053f, 60.906f, 226.245f, 61.576f, 225.576f)
                curveTo(62.245f, 224.906f, 63.053f, 224.571f, 64.0f, 224.571f)
                curveTo(64.947f, 224.571f, 65.755f, 224.906f, 66.424f, 225.576f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFFFDAC8)),
                    strokeLineWidth = 2.482868f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(71.0f, 41.0f)
                moveToRelative(-5.0f, 0.0f)
                arcToRelative(5.0f, 5.0f, 0.0f, true, true, 10.0f, 0.0f)
                arcToRelative(5.0f, 5.0f, 0.0f, true, true, -10.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFFFDAC8)),
                    strokeLineWidth = 2.482868f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(259.0f, 219.0f)
                moveToRelative(-5.0f, 0.0f)
                arcToRelative(5.0f, 5.0f, 0.0f, true, true, 10.0f, 0.0f)
                arcToRelative(5.0f, 5.0f, 0.0f, true, true, -10.0f, 0.0f)
            }
        }
        .build()
        return _imgNotiTips!!
    }

private var _imgNotiTips: ImageVector? = null
