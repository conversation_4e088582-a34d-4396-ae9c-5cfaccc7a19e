package dev.step.app.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import dev.step.app.ui.theme.bodyWidth

@Composable
fun ProfileHeightSettingContentImperial(
    title: String,
    ft: Int,
    `in`: Int,
    onFtChange: (Int) -> Unit,
    onInChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier) {
        Text(
            text = title,
            modifier = Modifier
                .fillMaxWidth()
                .background(color = Color.White)
                .zIndex(1.1f),
            fontWeight = FontWeight.Bold,
            fontSize = 18.sp,
            textAlign = TextAlign.Center,
        )

        BlankSpacer(height = 12.dp)

        Row(
            modifier = Modifier
                .bodyWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.weight(1f))

            NumberPicker(currentValue = ft, minValue = 0, maxValue = 9, onValueChange = onFtChange)

            Text(
                text = "ft",
                fontWeight = FontWeight.Bold,
                fontSize = 17.sp
            )

            Spacer(modifier = Modifier.weight(1f))

            NumberPicker(
                currentValue = `in`,
                minValue = 0,
                maxValue = 11,
                onValueChange = onInChange
            )

            Text(
                text = "in",
                fontWeight = FontWeight.Bold,
                fontSize = 17.sp
            )

            Spacer(modifier = Modifier.weight(1f))

        }

        BlankSpacer(height = 12.dp)

    }
}

@Composable
fun ProfileHeightSettingContentMetric(
    title: String,
    cm: Int,
    onCmChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier) {

        Text(
            text = title,
            modifier = Modifier.fillMaxWidth(),
            fontWeight = FontWeight.Bold,
            fontSize = 18.sp,
            textAlign = TextAlign.Center,
        )


        BlankSpacer(height = 12.dp)

        Row(
            modifier = Modifier
                .bodyWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.weight(1f))

            NumberPicker(
                currentValue = cm,
                minValue = 30,
                maxValue = 280,
                onValueChange = onCmChange
            )

            Text(
                text = "cm",
                modifier = Modifier.weight(1f),
                fontWeight = FontWeight.Bold,
                fontSize = 18.sp,
                textAlign = TextAlign.Start
            )
        }

        BlankSpacer(height = 14.dp)
    }
}

@Preview
@Composable
fun ProfileHeightSettingContentMetricPreview() {
    ProfileHeightSettingContentMetric(title = "lalala", cm = 170, onCmChange = {})
}

@Preview
@Composable
fun ProfileHeightSettingContentImperialPreview() {
    ProfileHeightSettingContentImperial(
        title = "lalala",
        ft = 7,
        `in` = 1,
        onFtChange = {},
        onInChange = {})
}
