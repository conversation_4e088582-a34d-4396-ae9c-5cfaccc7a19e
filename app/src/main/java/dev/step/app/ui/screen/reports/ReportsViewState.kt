package dev.step.app.ui.screen.reports

import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.pojo.DayReportsData
import dev.step.app.data.pojo.MonthReportsData
import dev.step.app.data.pojo.WeekReportsData

sealed interface ReportsStatisticsMode {
    object Steps : ReportsStatisticsMode
    object Calories : ReportsStatisticsMode
    object Time : ReportsStatisticsMode
    object Distance : ReportsStatisticsMode
}

data class ReportsViewState(
    val isInit:Boolean = false,
    val rsm: ReportsStatisticsMode = ReportsStatisticsMode.Steps,
    val dayReportsData: DayReportsData = DayReportsData(nowInstant(), listOf()),
    val weekReportsData: WeekReportsData = WeekReportsData(nowInstant(), nowInstant(), listOf()),
    val monthReportsData: MonthReportsData = MonthReportsData(nowInstant(), listOf()),
    val stepLengthCm: Float = 0f,
    val weightKg: Float = 0f,
    val mus:MeasurementUnit = MeasurementUnit.Imperial,
) {
    companion object {
        val Empty = ReportsViewState()
    }
}
