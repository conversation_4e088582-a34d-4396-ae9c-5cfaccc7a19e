package dev.step.app.ui.screen.game1

import dev.step.app.data.pojo.wallet.GameRemaining
import dev.step.app.ui.common.game.lkw.LuckyWheelData

data class WheelItemWithAward(
    val data: LuckyWheelData,
    val percentage: Int
)

data class Game1ViewState(
    val walletCoins: Int? = null,
    val remaining: GameRemaining = GameRemaining(),
    val remainingLimit: Int = 8,
    val wheelItemsWithAward: List<WheelItemWithAward> = emptyList(),

    val index: Int = 0,
    val rotateToIndex: Int = 0,

    val enabled: Boolean = false,

    val withdrawEnable: Boolean = false
) {
    companion object {
        val Empty = Game1ViewState()
    }
}