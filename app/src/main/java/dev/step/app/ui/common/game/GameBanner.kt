package dev.step.app.ui.common.game

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

private const val item_ui_design_aspect_ratio = 308 / 360f

@Composable
fun GameBanner(
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    modifier: Modifier = Modifier
) {

    val screenWidthDp = LocalConfiguration.current.screenWidthDp

    val itemWidthDp = (screenWidthDp - 16 * 2 - 10 * 2) / 3f

    val itemHeightDp = itemWidthDp / item_ui_design_aspect_ratio

    Row(modifier = modifier, horizontalArrangement = Arrangement.Center) {
        Game1BannerItem(
            onClick = openGame1,
            modifier = Modifier.size(width = itemWidthDp.dp, height = itemHeightDp.dp)
        )

        Spacer(modifier = Modifier.weight(1f))

        Game2BannerItem(
            onClick = openGame2,
            modifier = Modifier.size(width = itemWidthDp.dp, height = itemHeightDp.dp)
        )

        Spacer(modifier = Modifier.weight(1f))

        Game3BannerItem(
            onClick = openGame3,
            modifier = Modifier.size(width = itemWidthDp.dp, height = itemHeightDp.dp)
        )
    }
}


@Preview(
    device = Devices.PIXEL_4
)
@Composable
private fun GameBannerPreview() {
    GameBanner(
        openGame1 = { /*TODO*/ },
        openGame2 = { /*TODO*/ },
        openGame3 = { /*TODO*/ }
    )
}