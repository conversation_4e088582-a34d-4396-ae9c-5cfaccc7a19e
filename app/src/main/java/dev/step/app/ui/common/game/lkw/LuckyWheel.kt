package dev.step.app.ui.common.game.lkw

import androidx.annotation.DrawableRes
import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Button
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import dev.step.app.R
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.noRippleClickable
import dev.step.app.ui.theme.textScaleByLocale

data class LuckyWheelData(
    val coins: Int,
    @DrawableRes val coinsResId: Int,
) {
    companion object {
        val Test = listOf(
            LuckyWheelData(1000, R.drawable.img_lkw_coin_1),
            LuckyWheelData(2000, R.drawable.img_lkw_coin_2),
            LuckyWheelData(3000, R.drawable.img_lkw_coin_3),
            LuckyWheelData(4000, R.drawable.img_lkw_coin_4),
            LuckyWheelData(3000, R.drawable.img_lkw_coin_3),
            LuckyWheelData(1000, R.drawable.img_lkw_coin_1),
        )
    }
}

@Composable
fun LuckyWheel(
    dataList: List<LuckyWheelData>,
    remaining: Int,
    index: Int,
    rotateToIndex: Int,
    onPlay: () -> Unit,
    onRotateFinished: (index: Int) -> Unit,
    modifier: Modifier = Modifier,
    durationMillis: Int = 5000,
    playState: MutableState<Boolean> = mutableStateOf(false),
    enabled: Boolean = true
) {


    var initRotate by remember {
        mutableFloatStateOf(indexToAngle(index))
    }

    var playing by remember { playState }

    var playTimes by remember {
        mutableIntStateOf(0)
    }

    val targetAngle =
        if (!playing) initRotate else indexToAngle(rotateToIndex) + 360 * 12 * (playTimes + 1)

    val rotateAngle by animateFloatAsState(
        targetValue = targetAngle,
        animationSpec = tween(
            durationMillis = durationMillis,
            easing = CubicBezierEasing(0.265f, 0.300f, 0.015f, 1.000f)
        ),
        label = "",
        finishedListener = {
            initRotate = targetAngle
            playing = false
            playTimes++
            onRotateFinished(rotateToIndex)
        }
    )

    Box(modifier = modifier) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .padding(bottom = 74.dp)
                .zIndex(1f)
        ) {
            Image(
                painter = painterResource(id = R.drawable.img_lkw_wheel),
                contentDescription = null,
                modifier = Modifier.width(360.dp)
            )

            Column(modifier = Modifier.rotate(rotateAngle)) {
                Image(
                    painter = painterResource(id = R.drawable.img_lkw_arrow),
                    contentDescription = null,
                    modifier = Modifier.size(52.dp),
                )
                BlankSpacer(height = 74.dp)
                Image(
                    painter = painterResource(id = R.drawable.img_lkw_arrow),
                    contentDescription = null,
                    modifier = Modifier
                        .size(52.dp)
                        .alpha(0f)
                )
            }

            Image(
                painter = painterResource(id = R.drawable.img_lkw_center),
                contentDescription = null,
                modifier = Modifier
                    .size(124.dp)
                    .noRippleClickable {
                        if (!playing) {
                            onPlay()
                        }
                        if (enabled) {
                            playing = true
                        }
                    }
            )

            Box(modifier = Modifier.matchParentSize()) {
                dataList.getOrNull(0)?.let {
                    LuckyWheelItem(
                        data = it,
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(top = 36.dp, start = 40.dp)
                            .rotate(indexToAngle(0))
                    )
                }

                dataList.getOrNull(1)?.let {
                    LuckyWheelItem(
                        data = it,
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(top = 110.dp, end = 27.dp)
                            .rotate(indexToAngle(1))
                    )
                }

                dataList.getOrNull(2)?.let {
                    LuckyWheelItem(
                        data = it,
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(bottom = 76.dp, end = 42.dp)
                            .rotate(indexToAngle(2))
                    )
                }

                dataList.getOrNull(3)?.let {
                    LuckyWheelItem(
                        data = it,
                        modifier = Modifier
                            .align(Alignment.BottomStart)
                            .padding(bottom = 34.dp, start = 114.dp)
                            .rotate(indexToAngle(3))
                    )
                }

                dataList.getOrNull(4)?.let {
                    LuckyWheelItem(
                        data = it,
                        modifier = Modifier
                            .align(Alignment.BottomStart)
                            .padding(bottom = 108.dp, start = 24.dp)
                            .rotate(indexToAngle(4))
                    )
                }

                dataList.getOrNull(5)?.let {
                    LuckyWheelItem(
                        data = it,
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(top = 78.dp, start = 44.dp)
                            .rotate(indexToAngle(5))
                    )
                }
            }

        }

        Box(modifier = Modifier.align(Alignment.BottomCenter)) {
            Image(
                painter = painterResource(id = R.drawable.img_lkw_base),
                contentDescription = null,
                modifier = Modifier.width(360.dp)
            )

            Text(
                text = stringResource(id = R.string.title_today_remaining_times) + remaining.toString(),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 23.dp),
                fontSize = if (Locale.current.toLanguageTag() == "ru-RU") 15.sp else 20.sp,
                color = Color(0xFF5C4132),
            )
        }

    }
}

@Composable
fun LuckyWheelItem(
    data: LuckyWheelData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "+ ${data.coins} ${stringResource(R.string.text_coins)}",
            fontSize = if (Locale.current.toLanguageTag() == "ru-RU") 14.sp else 16.sp,
            color = Color(0xFFFF601A)
        )
        Image(
            painter = painterResource(id = data.coinsResId),
            contentDescription = null,
            modifier = Modifier.size(40.dp)
        )
    }
}

private fun indexToAngle(index: Int): Float {
    return 10f + index * 60
}

@Preview(
    device = Devices.NEXUS_5
)
@Composable
private fun LuckyWheelPreview() {
    var index by remember {
        mutableIntStateOf(0)
    }

    var rotateToIndex by remember {
        mutableIntStateOf(1)
    }

    val playState = remember { mutableStateOf(false) }
    AppTheme {
        Column {
            LuckyWheel(
                dataList = LuckyWheelData.Test,
                remaining = 2,
                index = index,
                rotateToIndex = rotateToIndex,
                onPlay = {},
                onRotateFinished = {
                    index = it
                    rotateToIndex = it + 1
                },
                playState = playState,
                enabled = false
            )

            Button(onClick = { /*playState.value = true*/ }) {

            }
        }
    }
}
