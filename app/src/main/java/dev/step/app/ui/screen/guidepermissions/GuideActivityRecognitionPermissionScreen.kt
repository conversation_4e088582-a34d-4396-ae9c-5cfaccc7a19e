package dev.step.app.ui.screen.guidepermissions

import android.os.Build
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.DirectionsRun
import androidx.compose.material.icons.rounded.HealthAndSafety
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.roudikk.guia.backstack.NavBackHandler
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.replaceLast
import dev.step.app.HomeNode
import dev.step.app.R
import dev.step.app.ScreenDestinationNode
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.biz.ActivityRecognitionPermissionRequester
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.FancyFadedBgButton
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject

@Parcelize
object GuideActivityRecognitionPermissionNode : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        val userOperateDataKv: UserOperateDataKv = koinInject()

        userOperateDataKv.hasBeenGuided = true

        GuideActivityRecognitionPermissionScreen(
            onNext = { canGoBack ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    navigator.push(GuideNotificationPermissionScreenNode(canGoBack = canGoBack))
                } else {
                    navigator.replaceLast(HomeNode())
                }
            }
        )
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun GuideActivityRecognitionPermissionScreen(
    onNext: (canBack: Boolean) -> Unit
) {
    NavBackHandler {

    }

    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()
    val context = LocalContext.current
    val permissionState = ActivityRecognitionPermissionRequester.permissionState()

    var ignoringBatteryOptimization by remember {
        mutableStateOf<Boolean?>(false)
    }

    LaunchedEffect(Unit) {
        IgnoringBatteryOptimizationRequester.ignoringEventFlow.onEach {
            ignoringBatteryOptimization = it
        }.launchIn(this)
    }

    val onGrant = {
//        if (permissionState == null || permissionState.status.isGranted) {
//            onNext(false)
//        } else {
//            ActivityRecognitionPermissionRequester.launchPermissionRequest(context, permissionState)
//        }

        ignoringBatteryOptimization =
            ignoringBatteryOptimizationRequester.hasIgnoring(context.findActivity())

        when {
            permissionState?.allPermissionsGranted == false && ignoringBatteryOptimization == false -> {
                ignoringBatteryOptimizationRequester.openSystemBatteryOptimizationSettings(context.findActivity())
//                ActivityRecognitionPermissionRequester.launchPermissionRequest(
//                    context,
//                    permissionState
//                )
            }

            permissionState?.allPermissionsGranted == false && ignoringBatteryOptimization != false -> {
                ActivityRecognitionPermissionRequester.launchPermissionRequest(
                    context,
                    permissionState
                )
            }

            permissionState?.allPermissionsGranted != false && ignoringBatteryOptimization == false -> {
                ignoringBatteryOptimizationRequester.openSystemBatteryOptimizationSettings(context.findActivity())
            }

            permissionState?.allPermissionsGranted != false && ignoringBatteryOptimization != false -> {
                onNext(false)
            }
        }
    }


//    LaunchedEffect(permissionState?.status?.isGranted) {
//        if (permissionState == null || permissionState.status.isGranted) {
//            onNext(false)
//        }
//    }

    LaunchedEffect(permissionState?.allPermissionsGranted, ignoringBatteryOptimization) {
        when {
            permissionState?.allPermissionsGranted != false && ignoringBatteryOptimization != false -> {
                onNext(false)
            }

//            permissionState?.status?.isGranted != false && ignoringBatteryOptimization == false -> {
//                onGrant()
//            }

            permissionState?.allPermissionsGranted == false && ignoringBatteryOptimization != false -> {
                onGrant()
            }
        }
    }

    GuidePermissionContent1(
        onGrant = onGrant,
        onNext = {
            onNext(true)
        },
        modifier = Modifier.fillMaxSize()
    )
}

@Composable
private fun GuidePermissionContent1(
    onGrant: () -> Unit,
    onNext: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        BlankSpacer(height = context.statusBarHeight)
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.End) {
            TextButton(onClick = onNext) {
                Text(text = stringResource(id = R.string.text_next), fontSize = 15.5.sp)
            }
        }

        Spacer(modifier = Modifier.weight(0.6f))

        Image(
            painter = painterResource(id = R.drawable.ic_app_launcher),
            contentDescription = null,
            modifier = Modifier.size(72.dp)
        )

        BlankSpacer(height = 26.dp)

        Text(
            text = stringResource(id = R.string.app_name),
            style = MaterialTheme.typography.h5.copy(color = AppColor.TextColorBlack)
        )

        BlankSpacer(height = 30.dp)

        Text(
            text = stringResource(R.string.text_additional_permissions_required),
            fontSize = 19.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            textAlign = TextAlign.Center,
        )
        BlankSpacer(height = 8.dp)
        Text(
            text = stringResource(R.string.text_we_require_additional_permissions_tips),
            fontSize = 15.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            textAlign = TextAlign.Center,
        )

        BlankSpacer(height = 30.dp)

        PermissionItem(
            iconPainter = rememberVectorPainter(image = Icons.Rounded.DirectionsRun),
            title = stringResource(R.string.permissions_required_text_physical_activity),
            description = stringResource(R.string.permissions_required_text_track_your_fitness_movements),
            modifier = Modifier.padding(horizontal = 16.dp)
        )
        BlankSpacer(height = 16.dp)
        PermissionItem(
            iconPainter = rememberVectorPainter(image = Icons.Rounded.HealthAndSafety),
            title = stringResource(R.string.text_protected_app),
            description = stringResource(R.string.text_ignore_battery_optimization),
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        BlankSpacer(height = 36.dp)

        FancyFadedBgButton(
            text = stringResource(R.string.text_allow),
            onClick = onGrant,
            modifier = Modifier
                .padding(horizontal = 84.dp)
                .fillMaxWidth(),
            fontSize = 15.sp
        )

        Spacer(modifier = Modifier.weight(1f))
    }
}

@Composable
private fun PermissionItem(
    iconPainter: Painter,
    title: String,
    description: String,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(10.dp),
        color = AppColor.PrimaryLightAlpha8
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            BlankSpacer(width = 4.dp)
            Box(modifier = Modifier.padding(horizontal = 18.dp, vertical = 22.dp)) {
                Icon(
                    painter = iconPainter,
                    contentDescription = null,
                    modifier = Modifier.size(28.dp),
                    tint = AppColor.Primary
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(text = title, fontSize = 16.sp)
                BlankSpacer(height = 2.dp)
                Text(text = description, fontSize = 13.sp, fontWeight = FontWeight.Normal)
            }
        }
    }
}

@Preview(
    device = Devices.PIXEL_4
)
@Composable
private fun Preview() {
    AppTheme {
        Column {
            GuidePermissionContent1(onGrant = { /*TODO*/ }, onNext = { /*TODO*/ })
        }
    }
}
