package dev.step.app.ui.screen.redeemedcoupon

import androidx.compose.ui.text.input.TextFieldValue
import dev.step.app.ui.screen.withdraw.RedeemedPrize

data class RedeemedCouponViewState(
    val coupon: RedeemedPrize.Coupon? = null,
    val emailTextField: TextFieldValue = TextFieldValue(),
    val messageTextField: TextFieldValue = TextFieldValue(),
) {
    companion object {
        val Empty = RedeemedCouponViewState()
    }
}
