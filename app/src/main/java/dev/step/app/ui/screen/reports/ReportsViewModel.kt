package dev.step.app.ui.screen.reports

import androidx.lifecycle.ViewModel
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.ext.time.*
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.data.pojo.MonthReportsData
import dev.step.app.data.pojo.WeekReportsData
import dev.step.app.data.repo.StepsTrackRecordRepo
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class ReportsViewModel(
    private val userSettingsDataKv: UserSettingsDataKv,
    private val stepsTrackRecordRepo: StepsTrackRecordRepo,
) : ViewModel(), ContainerHost<ReportsViewState, Unit> {

    override val container: Container<ReportsViewState, Unit> = container(ReportsViewState.Empty)

    fun onRefresh() = intent {
        val tz = TimeZone.currentSystemDefault()
        val now = nowInstant()

        val dayReportsData = stepsTrackRecordRepo.dayReportsData(now)

        val weekReportsData = WeekReportsData(
            now.thisWeekStartDateTime(tz).toInstant(tz),
            now.thisWeekEndDateTime(tz).toInstant(tz),
            stepsTrackRecordRepo.weekStepsData(now)
        )

        val monthReportsData = MonthReportsData(
            now.thisMonthStartInstant(tz),
            stepsTrackRecordRepo.monthStepsData(now)
        )

        val slCm = userSettingsDataKv.stepLengthCm
        val wKg = userSettingsDataKv.bodyWeightKg
        val mus = userSettingsDataKv.measurementUnit ?: MeasurementUnit.Imperial

        reduce {
            state.copy(
                isInit = true,
                dayReportsData = dayReportsData,
                weekReportsData = weekReportsData,
                monthReportsData = monthReportsData,
                stepLengthCm = slCm,
                weightKg = wKg,
                mus = mus,
            )
        }
    }

    fun onRsmChange(rsm: ReportsStatisticsMode) = intent {
        reduce { state.copy(rsm = rsm) }
    }

    fun onDayChange(instant: Instant) = intent {
        val dayReportsData = stepsTrackRecordRepo.dayReportsData(instant)

        reduce { state.copy(dayReportsData = dayReportsData) }
    }

    fun onWeekChange(startInstant: Instant, endInstant: Instant) = intent {

        val weekReportsData = WeekReportsData(
            startInstant,
            endInstant,
            stepsTrackRecordRepo.weekStepsData(startInstant)
        )

        reduce { state.copy(weekReportsData = weekReportsData) }
    }

    fun onMonthChange(instant: Instant) = intent {
        val tz = TimeZone.currentSystemDefault()

        val monthStepsData = stepsTrackRecordRepo.monthStepsData(instant)
        debugLog("monthStepsData -> $monthStepsData")

        val monthReportsData = MonthReportsData(
            instant.thisMonthStartInstant(tz),
            stepsTrackRecordRepo.monthStepsData(instant)
        )

        reduce { state.copy(monthReportsData = monthReportsData) }
    }
}
