package dev.step.app.ui.dialog.redeempicturesuccess

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import dev.step.app.DialogDestinationNode
import dev.step.app.R
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.bodyWidth
import kotlinx.parcelize.Parcelize

@Parcelize
class RedeemPictureSuccessDialogNode(
    val id: Int,
) : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        AppDefWithCloseDialog(
            onDismiss = navigator::pop,
            onClose = { },
            onConfirm = navigator::pop,
            confirmText = stringResource(id = R.string.text_ok),
            topPainter = painterResource(id = R.drawable.img_bigmoji_like),
            adPlace = null,
            adPlaceName = null,
            showCloseIcon = false
        ) {
            Column(
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                BlankSpacer(height = 32.dp)

                Text(text = stringResource(R.string.text_unlocked_successfully), fontSize = 20.sp)

                BlankSpacer(height = 12.dp)

                Text(
                    text = stringResource(R.string.text_click_to_preview_and_download_),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp)
                )

                BlankSpacer(height = 20.dp)
            }
        }
    }
}