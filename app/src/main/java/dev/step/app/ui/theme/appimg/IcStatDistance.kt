package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcStatDistance: ImageVector
    get() {
        if (_icStatDistance != null) {
            return _icStatDistance!!
        }
        _icStatDistance = Builder(name = "IcStatDistance", defaultWidth = 30.0.dp, defaultHeight =
                30.0.dp, viewportWidth = 30.0f, viewportHeight = 30.0f).apply {
            path(fill = SolidColor(Color(0xFFFFFFFF)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(18.3036f, 20.1261f)
                curveTo(17.8689f, 19.9381f, 17.3382f, 19.9618f, 16.9326f, 20.1872f)
                curveTo(16.5269f, 20.4127f, 16.3157f, 20.8012f, 16.3869f, 21.1912f)
                curveTo(16.4581f, 21.5812f, 16.7994f, 21.9057f, 17.2689f, 22.0298f)
                curveTo(18.5555f, 22.4745f, 19.3928f, 23.0874f, 19.3928f, 23.5863f)
                curveTo(19.3928f, 24.5355f, 16.5133f, 25.9131f, 12.0f, 25.9131f)
                curveTo(7.4867f, 25.9131f, 4.6072f, 24.5409f, 4.6072f, 23.5918f)
                curveTo(4.6072f, 23.1253f, 5.3288f, 22.5613f, 6.4792f, 22.1274f)
                curveTo(7.1072f, 21.871f, 7.3664f, 21.2639f, 7.0649f, 20.7556f)
                curveTo(6.7634f, 20.2474f, 6.0101f, 20.0216f, 5.3628f, 20.2454f)
                curveTo(3.1981f, 21.0643f, 2.0f, 22.2521f, 2.0f, 23.5918f)
                curveTo(2.0f, 26.1029f, 6.3022f, 27.9958f, 12.0f, 27.9958f)
                curveTo(14.4574f, 28.0475f, 16.8939f, 27.6231f, 19.1001f, 26.7592f)
                curveTo(20.9721f, 25.9456f, 22.0f, 24.8229f, 22.0f, 23.5918f)
                curveTo(22.0f, 22.1979f, 20.6521f, 20.945f, 18.3036f, 20.1261f)
                lineTo(18.3036f, 20.1261f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFFFFF)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(27.941f, 7.5844f)
                curveTo(27.8193f, 7.1701f, 27.5042f, 6.8469f, 27.1025f, 6.7242f)
                lineTo(14.744f, 3.0504f)
                curveTo(14.3952f, 2.9458f, 14.0196f, 3.0057f, 13.7174f, 3.2139f)
                curveTo(13.4152f, 3.4221f, 13.2173f, 3.7575f, 13.1766f, 4.1306f)
                lineTo(11.0093f, 23.5665f)
                curveTo(10.9687f, 23.903f, 11.0618f, 24.2422f, 11.2673f, 24.5066f)
                curveTo(11.4773f, 24.7872f, 11.7901f, 24.9657f, 12.1316f, 25.0f)
                lineTo(12.2736f, 25.0f)
                curveTo(12.9019f, 25.0005f, 13.43f, 24.5121f, 13.4991f, 23.8665f)
                lineTo(14.286f, 16.8122f)
                lineTo(24.3612f, 19.1059f)
                curveTo(24.7826f, 19.1997f, 25.2208f, 19.0603f, 25.5183f, 18.7379f)
                curveTo(25.8158f, 18.4154f, 25.9296f, 17.9563f, 25.8189f, 17.5257f)
                lineTo(24.5289f, 12.6517f)
                lineTo(27.7153f, 8.7912f)
                curveTo(27.9847f, 8.455f, 28.0698f, 8.0001f, 27.941f, 7.5844f)
                close()
            }
        }
        .build()
        return _icStatDistance!!
    }

private var _icStatDistance: ImageVector? = null
