package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcProfileGender: ImageVector
    get() {
        if (_icProfileGender != null) {
            return _icProfileGender!!
        }
        _icProfileGender = Builder(name = "IcProfileGender", defaultWidth = 70.0.dp, defaultHeight =
                70.0.dp, viewportWidth = 70.0f, viewportHeight = 70.0f).apply {
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 1.0f)
                lineTo(35.0f, 1.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 69.0f, 35.0f)
                lineTo(69.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 69.0f)
                lineTo(35.0f, 69.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 1.0f, 35.0f)
                lineTo(1.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 1.0f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(53.4694f, 42.2333f)
                curveTo(53.3405f, 41.756f, 52.8411f, 41.4715f, 52.354f, 41.5978f)
                lineTo(46.4743f, 43.1226f)
                lineTo(45.2175f, 38.6566f)
                curveTo(45.0835f, 38.1806f, 44.581f, 37.9012f, 44.0954f, 38.0324f)
                curveTo(43.6096f, 38.1637f, 43.3244f, 38.6559f, 43.4584f, 39.1319f)
                lineTo(44.7102f, 43.5801f)
                lineTo(39.2005f, 45.0089f)
                curveTo(38.7134f, 45.1353f, 38.423f, 45.6246f, 38.552f, 46.1019f)
                curveTo(38.6602f, 46.5026f, 39.0294f, 46.7674f, 39.4334f, 46.7674f)
                curveTo(39.5106f, 46.7674f, 39.5892f, 46.7577f, 39.6674f, 46.7374f)
                lineTo(45.1954f, 45.3038f)
                lineTo(46.8951f, 51.3434f)
                curveTo(47.0066f, 51.7399f, 47.3738f, 52.0f, 47.7741f, 52.0f)
                curveTo(47.8544f, 52.0f, 47.9361f, 51.9895f, 48.0172f, 51.9676f)
                curveTo(48.5029f, 51.8363f, 48.7881f, 51.3441f, 48.6542f, 50.8681f)
                lineTo(46.9595f, 44.8463f)
                lineTo(52.8209f, 43.3262f)
                curveTo(53.308f, 43.1999f, 53.5983f, 42.7106f, 53.4694f, 42.2333f)
                close()
                moveTo(29.8545f, 45.3695f)
                lineTo(23.6411f, 48.8739f)
                lineTo(26.2788f, 39.0355f)
                lineTo(24.5139f, 38.5812f)
                lineTo(21.8643f, 48.464f)
                lineTo(18.1998f, 42.3345f)
                curveTo(17.945f, 41.9085f, 17.386f, 41.7654f, 16.9513f, 42.015f)
                curveTo(16.5166f, 42.2646f, 16.3706f, 42.8122f, 16.6253f, 43.2383f)
                lineTo(21.3111f, 51.0762f)
                curveTo(21.3268f, 51.1143f, 21.3443f, 51.152f, 21.3658f, 51.1886f)
                curveTo(21.5047f, 51.425f, 21.7372f, 51.5755f, 21.991f, 51.6216f)
                curveTo(22.2105f, 51.6652f, 22.4387f, 51.6278f, 22.6318f, 51.5166f)
                curveTo(22.6655f, 51.497f, 22.6979f, 51.4752f, 22.7287f, 51.4515f)
                lineTo(30.7648f, 46.9193f)
                curveTo(31.2015f, 46.6729f, 31.3518f, 46.1264f, 31.1004f, 45.6984f)
                curveTo(30.849f, 45.2704f, 30.2912f, 45.1232f, 29.8545f, 45.3695f)
                lineTo(29.8545f, 45.3695f)
                close()
            }
            path(fill = SolidColor(Color(0xFF2D3142)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(50.3527f, 21.2218f)
                curveTo(48.3231f, 19.1442f, 45.6246f, 18.0f, 42.7544f, 18.0f)
                curveTo(40.2781f, 18.0f, 37.9091f, 18.8628f, 36.0008f, 20.4446f)
                curveTo(34.0943f, 18.861f, 31.7347f, 18.0f, 29.2456f, 18.0f)
                curveTo(26.3754f, 18.0f, 23.6769f, 19.1442f, 21.6473f, 21.2218f)
                curveTo(19.6178f, 23.2995f, 18.5f, 26.0618f, 18.5f, 29.0f)
                curveTo(18.5f, 31.9382f, 19.6177f, 34.7006f, 21.6473f, 36.7782f)
                curveTo(23.6769f, 38.8558f, 26.3754f, 40.0f, 29.2456f, 40.0f)
                curveTo(31.7347f, 40.0f, 34.0943f, 39.1389f, 36.0008f, 37.5555f)
                curveTo(37.9091f, 39.1372f, 40.2781f, 40.0f, 42.7544f, 40.0f)
                curveTo(45.6246f, 40.0f, 48.3231f, 38.8558f, 50.3527f, 36.7782f)
                curveTo(52.3822f, 34.7005f, 53.5f, 31.9382f, 53.5f, 29.0f)
                curveTo(53.5f, 26.0618f, 52.3823f, 23.2994f, 50.3527f, 21.2218f)
                close()
                moveTo(20.2544f, 29.0f)
                curveTo(20.2544f, 23.9249f, 24.2879f, 19.7959f, 29.2456f, 19.7959f)
                curveTo(34.2034f, 19.7959f, 38.2368f, 23.9249f, 38.2368f, 29.0f)
                curveTo(38.2368f, 34.0751f, 34.2034f, 38.2041f, 29.2456f, 38.2041f)
                curveTo(24.2879f, 38.2041f, 20.2544f, 34.0751f, 20.2544f, 29.0f)
                close()
                moveTo(42.7544f, 38.2041f)
                curveTo(40.7587f, 38.2041f, 38.8464f, 37.5342f, 37.2815f, 36.3023f)
                curveTo(39.0332f, 34.2876f, 39.9912f, 31.7193f, 39.9912f, 29.0f)
                curveTo(39.9912f, 26.2807f, 39.0332f, 23.7124f, 37.2815f, 21.6977f)
                curveTo(38.8464f, 20.4658f, 40.7587f, 19.7959f, 42.7544f, 19.7959f)
                curveTo(47.7122f, 19.7959f, 51.7456f, 23.9249f, 51.7456f, 29.0f)
                curveTo(51.7456f, 34.0751f, 47.7122f, 38.2041f, 42.7544f, 38.2041f)
                close()
            }
        }
        .build()
        return _icProfileGender!!
    }

private var _icProfileGender: ImageVector? = null
