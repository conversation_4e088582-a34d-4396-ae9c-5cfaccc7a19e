package dev.step.app.ui.screen.redeemedcash

import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.WalletBizKv
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class RedeemedCashViewModel(
    private val walletBizKv: WalletBizKv,
//    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val rewardedAdManager: RewardedAdManager,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val userOperateDataKv: UserOperateDataKv,
) : ViewModel(), ContainerHost<RedeemedCashViewState, RedeemedCashSideEffect> {

    private var coinPrice: Int = 0

    override val container: Container<RedeemedCashViewState, RedeemedCashSideEffect> =
        container(RedeemedCashViewState.Empty)

    fun configure(
        coinPrice: Int,
        amount: Int,
    ) = intent {
        <EMAIL> = coinPrice

        reduce {
            state.copy(
                amount = amount,
            )
        }

        val tenjinAttribution = userOperateDataKv.tenjinAttr
        val isOrganic = tenjinAttribution.isOrganic()

        val coinsExchangeUnit = if (isOrganic) {
            remoteConfigHelper.getOUserWithDrawUS().let { wd ->
                wd?.coinsExchangeUnit
            }
        } else {
            remoteConfigHelper.getPUserWithDrawUS().let { wd ->
                wd?.coinsExchangeUnit
            }
        }

        coinsExchangeUnit?.let {
            val currencyText = coinsExchangeUnit.currency_text
            val currencyTextPlacementIsStart = coinsExchangeUnit.currency_text_placement_is_start

            reduce {
                state.copy(
                    currencyText = currencyText,
                    currencyTextPlacementIsStart = currencyTextPlacementIsStart,
                    cashText = if (currencyTextPlacementIsStart) "$currencyText$amount" else "$amount $currencyText"
                )
            }
        }
    }

    fun onAccountAddressChange(accountAddress: TextFieldValue) = intent {
        reduce {
            state.copy(accountAddress = accountAddress)
        }
    }

    private var onRedeemNowJob: Job? = null
    fun onRedeemNow() = intent {
        val accountAddress = state.accountAddress.text

        if (accountAddress.trim().isNotEmpty()) {
            onRedeemNowJob?.cancel()
            onRedeemNowJob = viewModelScope.launch {
                rewardedAdManager.tryToShowRewardedLoadingDialog("RedeemedCash")
                rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                    postSideEffect(
                        RedeemedCashSideEffect.Redeemed(
                            cashText = state.cashText,
                            accountAddress = accountAddress,
                        )
                    )

                    walletBizKv.setCoinBalance(walletBizKv.getCoinBalance() - coinPrice)

                    onRedeemNowJob?.cancel()
                    onRedeemNowJob = null
                }
            }
        } else {
            postSideEffect(RedeemedCashSideEffect.AccountAddressError)
        }
    }

    @Volatile
    var isAccountAddressClicked: Boolean = false
}