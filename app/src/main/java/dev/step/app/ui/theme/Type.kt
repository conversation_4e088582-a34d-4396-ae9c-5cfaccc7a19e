package dev.step.app.ui.theme

import androidx.compose.material.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import dev.step.app.R

val AppFont = FontFamily(
    Font(R.font.gabarito_bold),
    Font(R.font.gabarito_extrabold),
    Font(R.font.gabarito_medium),
    Font(R.font.gabarito_regular),
)

// Set of Material typography styles to start with
val Typography = Typography(
    defaultFontFamily = AppFont,
    body1 = TextStyle(
        fontFamily = AppFont,
        fontWeight = FontWeight.Bold,
        fontSize = 16.sp,
        color = AppColor.TextColorBlack
    ),
    button = TextStyle(
        fontFamily = AppFont,
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        color = AppColor.TextColorBlack
    ),
    caption = TextStyle(
        fontFamily = AppFont,
        fontWeight = FontWeight.Bold,
        fontSize = 12.sp,
        color = AppColor.TextColorBlack
    )
)
