package dev.step.app.ui.dialog.steplengthchange

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
//import dev.step.app.androidplatform.biz.MaxInterstitialAdHelper
import dev.step.app.androidplatform.cmToFtIn
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.ui.common.ProfileHeightSettingContentImperial
import dev.step.app.ui.common.ProfileHeightSettingContentMetric
import dev.step.app.ui.common.AppDefDialog
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

@Composable
fun StepLengthChangeDialog(
    navUp: () -> Unit,
    userSettingsDataKv: UserSettingsDataKv = koinInject()
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator)

    val scope = rememberCoroutineScope()

    var mus by remember {
        mutableStateOf<MeasurementUnit?>(null)
    }

    var slCm by remember { mutableFloatStateOf(0f) }
    var slFt by remember { mutableIntStateOf(1) }
    var slIn by remember { mutableIntStateOf(0) }

    LaunchedEffect(Unit) {
        mus = userSettingsDataKv.measurementUnit

        slCm = userSettingsDataKv.stepLengthCm

        userSettingsDataKv.stepLengthFtIn.let {

            if (it.first == null || it.second == null) {
                val (_ft, _in) = cmToFtIn(slCm)
                slFt = _ft
                slIn = _in
            } else {
                slFt = it.first!!
                slIn = it.second!!
            }
        }
    }

    AppDefDialog(
        onDismiss = navUp,
        onCancel = navUp,
        onConfirm = {
            scope.launch {
                mus?.let {
                    when (mus!!) {
                        MeasurementUnit.Metric -> userSettingsDataKv.changeStepLength(
                            cm = slCm,
                            mus = mus!!
                        )

                        MeasurementUnit.Imperial -> userSettingsDataKv.changeStepLength(
                            ft = slFt,
                            `in` = slIn,
                            mus = mus!!
                        )
                    }
                }

//                interstitialAdHelper.tryToShowAd(
//                    "save_step_length",
//                    onAdShowingOrSkip = { navUp() }
//                )
                OnBack()
            }
        },
    ) {
        when (mus) {
            null -> {

            }

            MeasurementUnit.Metric -> ProfileHeightSettingContentMetric(
                title = stringResource(id = dev.step.app.R.string.text_step_length),
                cm = slCm.toInt(),
                onCmChange = {
                    slCm = it.toFloat()
                }
            )

            MeasurementUnit.Imperial -> ProfileHeightSettingContentImperial(
                title = stringResource(id = dev.step.app.R.string.text_step_length),
                ft = slFt,
                `in` = slIn,
                onFtChange = {
                    slFt = it
                },
                onInChange = {
                    slIn = it
                },
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
    }
}
