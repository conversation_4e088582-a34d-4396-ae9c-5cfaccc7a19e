package dev.step.app.ui.common.reportschart

import com.github.mikephil.charting.formatter.ValueFormatter
import dev.step.app.androidplatform.ext.time.toHHmm
import kotlin.time.DurationUnit
import kotlin.time.toDuration

object YAxisDurationValueFormatter : ValueFormatter() {

    override fun getFormattedValue(value: Float): String {
        return value.toLong().toDuration(DurationUnit.SECONDS).toHHmm()
    }
}
