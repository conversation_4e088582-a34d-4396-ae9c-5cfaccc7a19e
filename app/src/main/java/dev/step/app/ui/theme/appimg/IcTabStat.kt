package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcTabStat: ImageVector
    get() {
        if (_icTabStat != null) {
            return _icTabStat!!
        }
        _icTabStat = Builder(name = "IcTabStat", defaultWidth = 60.0.dp, defaultHeight = 60.0.dp,
                viewportWidth = 60.0f, viewportHeight = 60.0f).apply {
            path(fill = SolidColor(Color(0xFF9FA2A5)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(30.0f, 7.0f)
                lineTo(30.0f, 7.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 35.0f, 12.0f)
                lineTo(35.0f, 48.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 30.0f, 53.0f)
                lineTo(30.0f, 53.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 25.0f, 48.0f)
                lineTo(25.0f, 12.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 30.0f, 7.0f)
                close()
            }
            path(fill = SolidColor(Color(0xFF9FA2A5)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(13.0f, 19.0f)
                lineTo(13.0f, 19.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 18.0f, 24.0f)
                lineTo(18.0f, 48.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 13.0f, 53.0f)
                lineTo(13.0f, 53.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 8.0f, 48.0f)
                lineTo(8.0f, 24.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 13.0f, 19.0f)
                close()
            }
            path(fill = SolidColor(Color(0xFF9FA2A5)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(47.0f, 27.0f)
                lineTo(47.0f, 27.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 52.0f, 32.0f)
                lineTo(52.0f, 48.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 47.0f, 53.0f)
                lineTo(47.0f, 53.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 42.0f, 48.0f)
                lineTo(42.0f, 32.0f)
                arcTo(5.0f, 5.0f, 0.0f, false, true, 47.0f, 27.0f)
                close()
            }
        }
        .build()
        return _icTabStat!!
    }

private var _icTabStat: ImageVector? = null
