package dev.step.app.ui.theme.appimg

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush.Companion.linearGradient
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcStepKcal: ImageVector
    get() {
        if (_icStepKcal != null) {
            return _icStepKcal!!
        }
        _icStepKcal = Builder(name = "IcStepKcal", defaultWidth = 50.0.dp, defaultHeight = 50.0.dp,
                viewportWidth = 50.0f, viewportHeight = 50.0f).apply {
            path(fill = linearGradient(0.0f to Color(0xFFFF844D), 1.0f to Color(0xFFF75927), start =
                    Offset(26.79645f,1.0f), end = Offset(26.79645f,48.74515f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(37.4008f, 16.9262f)
                lineTo(33.656f, 20.601f)
                curveTo(33.656f, 20.601f, 33.656f, 5.9016f, 21.1766f, 1.0f)
                curveTo(21.1766f, 1.0f, 19.9266f, 14.4728f, 13.6869f, 19.3744f)
                curveTo(7.4524f, 24.2759f, -5.027f, 38.9754f, 19.9318f, 50.0f)
                curveTo(19.9318f, 50.0f, 7.4524f, 36.5272f, 23.6766f, 26.7241f)
                curveTo(23.6766f, 26.7241f, 22.4266f, 31.6256f, 28.6663f, 36.522f)
                curveTo(34.906f, 41.4236f, 28.6663f, 49.9948f, 28.6663f, 49.9948f)
                curveTo(28.6663f, 49.9948f, 58.6199f, 42.6503f, 37.4008f, 16.9262f)
                close()
            }
        }
        .build()
        return _icStepKcal!!
    }

private var _icStepKcal: ImageVector? = null
