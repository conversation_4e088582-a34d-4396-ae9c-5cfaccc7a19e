package dev.step.app.ui.screen.guideprofilesettings

import dev.step.app.data.adt.GenderSetting
import dev.step.app.data.adt.MeasurementUnit


data class GuideProfileSettingsViewState(
    val gender: GenderSetting = GenderSetting.Female,
    val mus: MeasurementUnit = MeasurementUnit.Imperial,

    val bodyHeightFt: Int = 0,
    val bodyHeightIn: Int = 0,
    val bodyWeightLb: Float = 160f,

    val bodyHeightCm: Int = 177,
    val bodyWeightKg: Float = 75f
) {
    companion object {
        val Empty = GuideProfileSettingsViewState()

        val FemaleDefaultState = GuideProfileSettingsViewState(
            gender = GenderSetting.Female,

            bodyHeightFt = 5,
            bodyHeightIn = 5,
            bodyWeightLb = 130f,

            bodyWeightKg = 60f,
            bodyHeightCm = 165
        )

        val MaleDefaultState = GuideProfileSettingsViewState(
            gender = GenderSetting.Male,

            bodyHeightFt = 5,
            bodyHeightIn = 9,
            bodyWeightLb = 165f,

            bodyWeightKg = 75f,
            bodyHeightCm = 175
        )

        val OthersDefaultState = GuideProfileSettingsViewState(
            gender = GenderSetting.Others,

            bodyHeightFt = 5,
            bodyHeightIn = 8,
            bodyWeightLb = 130f,

            bodyWeightKg = 57f,
            bodyHeightCm = 172
        )
    }
}
