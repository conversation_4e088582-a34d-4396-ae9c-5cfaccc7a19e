package dev.step.app.ui.screen.permissionsmanager

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.MultiplePermissionsState
import com.google.accompanist.permissions.PermissionState
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.ActivityRecognitionPermissionRequester
import dev.step.app.androidplatform.biz.NotificationPermissionRequester
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.androidplatform.memorystore.StepTrackingSession
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class PermissionsManagerViewModel(
    private val stepTrackingSession: StepTrackingSession,
    private val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester,
    private val notificationPermissionRequester: NotificationPermissionRequester,
) : ViewModel(), ContainerHost<PermissionsManagerViewState, Unit> {
    override val container: Container<PermissionsManagerViewState, Unit> =
        container(PermissionsManagerViewState.Empty)

    fun onRefresh(activity: Activity) = intent {
        val isActivityRecognitionGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.ACTIVITY_RECOGNITION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        val isIgnoreBatteryOptimizationsGranted =
            ignoringBatteryOptimizationRequester.hasIgnoring(activity) == true

        val isNotificationGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        reduce {
            state.copy(
                isActivityRecognitionGranted = isActivityRecognitionGranted,
                isIgnoreBatteryOptimizationsGranted = isIgnoreBatteryOptimizationsGranted,
                isNotificationGranted = isNotificationGranted
            )
        }

        startTrack()
    }

    @OptIn(ExperimentalPermissionsApi::class)
    fun requestActivityRecognition(
        activity: Activity,
        activityRecognitionPermissionState: MultiplePermissionsState
    ) {
        ActivityRecognitionPermissionRequester.launchPermissionRequest(
            activity,
            activityRecognitionPermissionState
        )
    }

    fun requestIgnoringBatteryOptimization(activity: Activity) {
        ignoringBatteryOptimizationRequester.openSystemBatteryOptimizationSettings(activity)
    }

    fun requestNotificationPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            notificationPermissionRequester.customRequesterOpenToNotificationSettings(activity)
        }
    }

    fun doShowActivityRecognitionDialog(
        show: Boolean
    ) = intent {
        reduce {
            state.copy(
                showActivityRecognitionDialog = show
            )
        }
    }

    fun doShowIgnoreBatteryOptimizationsDialog(
        show: Boolean
    ) = intent {
        reduce {
            state.copy(
                showIgnoreBatteryOptimizationsDialog = show
            )
        }
    }

    fun doShowNotificationPermissionDialog(
        show: Boolean
    ) = intent {
        reduce {
            state.copy(
                showNotificationDialog = show
            )
        }
    }

    fun startTrack() {
        debugLog("stepTrackingSession.startTracking()")
        stepTrackingSession.startTracking()
    }
}