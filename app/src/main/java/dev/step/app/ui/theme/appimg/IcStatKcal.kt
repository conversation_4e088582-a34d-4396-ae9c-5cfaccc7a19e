package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcStatKcal: ImageVector
    get() {
        if (_icStatKcal != null) {
            return _icStatKcal!!
        }
        _icStatKcal = Builder(name = "IcStatKcal", defaultWidth = 30.0.dp, defaultHeight = 30.0.dp,
                viewportWidth = 30.0f, viewportHeight = 30.0f).apply {
            path(fill = SolidColor(Color(0xFFFFFFFF)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(11.6594f, 2.0309f)
                curveTo(11.9433f, 2.1021f, 18.6253f, 3.8256f, 19.9835f, 8.3971f)
                curveTo(20.2437f, 9.2726f, 20.3701f, 10.8163f, 20.3064f, 12.3307f)
                curveTo(20.2718f, 13.1559f, 20.1721f, 14.2008f, 19.9247f, 15.2566f)
                curveTo(20.2386f, 15.033f, 20.5779f, 14.7566f, 20.9143f, 14.4263f)
                curveTo(21.5434f, 13.8081f, 22.5786f, 12.5795f, 22.4417f, 11.2955f)
                curveTo(22.3952f, 10.8597f, 22.6557f, 10.4478f, 23.0771f, 10.2905f)
                curveTo(23.4987f, 10.1333f, 23.9763f, 10.2696f, 24.2428f, 10.6236f)
                curveTo(26.0723f, 13.0526f, 27.0f, 15.4815f, 27.0f, 17.8426f)
                curveTo(27.0f, 19.3484f, 26.6958f, 20.8098f, 26.0957f, 22.1859f)
                curveTo(25.5166f, 23.5148f, 24.6873f, 24.7079f, 23.6315f, 25.7322f)
                curveTo(22.5757f, 26.7565f, 21.346f, 27.5608f, 19.9763f, 28.123f)
                curveTo(18.5588f, 28.7048f, 17.0526f, 29.0f, 15.5005f, 29.0f)
                curveTo(13.9484f, 29.0f, 12.4421f, 28.7048f, 11.0237f, 28.1227f)
                curveTo(9.654f, 27.5608f, 8.4243f, 26.7562f, 7.3685f, 25.7319f)
                curveTo(6.3127f, 24.7076f, 5.4837f, 23.5145f, 4.9043f, 22.1856f)
                curveTo(4.3042f, 20.8094f, 4.0f, 19.3481f, 4.0f, 17.8423f)
                curveTo(4.0f, 14.9936f, 5.5609f, 13.0398f, 7.0705f, 11.1503f)
                curveTo(7.5809f, 10.5115f, 8.1088f, 9.8508f, 8.5695f, 9.1623f)
                curveTo(10.027f, 6.9843f, 10.4099f, 2.9258f, 10.4137f, 2.8852f)
                curveTo(10.4393f, 2.6012f, 10.5927f, 2.3424f, 10.8326f, 2.1779f)
                curveTo(11.0724f, 2.0132f, 11.3749f, 1.9595f, 11.6594f, 2.0309f)
                close()
                moveTo(20.9565f, 19.0005f)
                lineTo(20.9225f, 19.0013f)
                curveTo(20.4769f, 19.0134f, 20.0944f, 19.2744f, 19.9784f, 19.6435f)
                curveTo(19.8085f, 20.1845f, 19.3909f, 21.0158f, 18.3649f, 21.8951f)
                curveTo(17.3428f, 22.7711f, 16.3801f, 23.1259f, 15.7534f, 23.2696f)
                curveTo(15.3242f, 23.3679f, 15.0187f, 23.693f, 15.002f, 24.0737f)
                lineTo(15.002f, 24.0737f)
                lineTo(15.0007f, 24.0999f)
                curveTo(14.9757f, 24.6703f, 15.5918f, 25.1096f, 16.2394f, 24.9758f)
                curveTo(16.3486f, 24.9535f, 16.4584f, 24.9276f, 16.567f, 24.8982f)
                curveTo(17.7183f, 24.5849f, 18.8426f, 23.9774f, 19.8188f, 23.1408f)
                curveTo(20.794f, 22.305f, 21.5052f, 21.3401f, 21.8749f, 20.351f)
                curveTo(21.9089f, 20.2599f, 21.9388f, 20.1721f, 21.9651f, 20.0878f)
                curveTo(22.1402f, 19.5279f, 21.6327f, 18.982f, 20.9565f, 19.0005f)
                lineTo(20.9565f, 19.0005f)
                close()
            }
        }
        .build()
        return _icStatKcal!!
    }

private var _icStatKcal: ImageVector? = null
