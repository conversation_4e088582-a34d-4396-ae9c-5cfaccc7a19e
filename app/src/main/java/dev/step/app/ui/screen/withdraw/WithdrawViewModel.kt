package dev.step.app.ui.screen.withdraw

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.TenjinHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.data.pojo.remoteconfig.CoinsExchangeUnit
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class WithdrawViewModel(
    private val walletBizKv: WalletBizKv,
    private val userOperateDataKv: UserOperateDataKv,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
//    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val rewardedAdManager: RewardedAdManager,
    private val tenjinHelper: TenjinHelper,
) : ViewModel(), ContainerHost<WithdrawViewState, WithdrawSideEffect> {

    override val container: Container<WithdrawViewState, WithdrawSideEffect> =
        container(WithdrawViewState.Empty)

    fun onRefresh(context: Context) = intent {
        val coins = walletBizKv.getCoinBalance()

        val tenjinAttribution = tenjinHelper.getOrFetchAttributionInfo(context.findActivity())

        val isOrganic = tenjinAttribution.isOrganic()

        val redeemedPrizes = mutableListOf<RedeemedPrize>()

        var coinsExchangeUnit: CoinsExchangeUnit? = null

        if (isOrganic) {
            val oUserWithDrawUS = remoteConfigHelper.getOUserWithDrawUS()

            if (oUserWithDrawUS != null) {
                coinsExchangeUnit = oUserWithDrawUS.coinsExchangeUnit

                redeemedPrizes.add(
                    RedeemedPrize.Coupon(
                        coinPrice = oUserWithDrawUS.amount1_coins,
                        off = oUserWithDrawUS.amount1_off
                    )
                )
                redeemedPrizes.add(
                    RedeemedPrize.Coupon(
                        coinPrice = oUserWithDrawUS.amount2_coins,
                        off = oUserWithDrawUS.amount2_off
                    )
                )
                redeemedPrizes.add(
                    RedeemedPrize.Coupon(
                        coinPrice = oUserWithDrawUS.amount3_coins,
                        off = oUserWithDrawUS.amount3_off
                    )
                )
                redeemedPrizes.add(
                    RedeemedPrize.Coupon(
                        coinPrice = oUserWithDrawUS.amount4_coins,
                        off = oUserWithDrawUS.amount4_off
                    )
                )
            }
        } else {
            val pUserWithDrawUS = remoteConfigHelper.getPUserWithDrawUS()

            if (pUserWithDrawUS != null) {
                coinsExchangeUnit = pUserWithDrawUS.coinsExchangeUnit

                redeemedPrizes.add(
                    RedeemedPrize.Cash(
                        coinPrice = pUserWithDrawUS.amount1_coins,
                        amount = pUserWithDrawUS.amount1_amount
                    )
                )
                redeemedPrizes.add(
                    RedeemedPrize.Cash(
                        coinPrice = pUserWithDrawUS.amount2_coins,
                        amount = pUserWithDrawUS.amount2_amount
                    )
                )
                redeemedPrizes.add(
                    RedeemedPrize.Cash(
                        coinPrice = pUserWithDrawUS.amount3_coins,
                        amount = pUserWithDrawUS.amount3_amount
                    )
                )
                redeemedPrizes.add(
                    RedeemedPrize.Cash(
                        coinPrice = pUserWithDrawUS.amount4_coins,
                        amount = pUserWithDrawUS.amount4_amount
                    )
                )
            }
        }

        reduce {
            state.copy(
                coinBalance = coins,
                redeemedPrizes = redeemedPrizes,
                isOrganic = isOrganic,
                coinsExchangeUnit = coinsExchangeUnit
            )
        }
    }

    private var exchangeRedeemedPrizeAdJob: Job? = null
    fun onExchangeRedeemedPrize(
        redeemedPrize: RedeemedPrize,
        rewardAdFrom: String,
    ) {
        when (redeemedPrize) {
            is RedeemedPrize.Cash -> onExchangeCash(redeemedPrize, rewardAdFrom)
            is RedeemedPrize.Coupon -> onExchangeCoupon(redeemedPrize, rewardAdFrom)
        }
    }

    private fun onExchangeCash(
        cash: RedeemedPrize.Cash,
        rewardAdFrom: String
    ) {
        if (walletBizKv.getCoinBalance() < cash.coinPrice) {
            intent { postSideEffect(WithdrawSideEffect.InsufficientInCoins) }
            return
        }

        exchangeRedeemedPrizeAdJob?.cancel()

        exchangeRedeemedPrizeAdJob = viewModelScope.launch {
            rewardedAdManager.tryToShowRewardedLoadingDialog(rewardAdFrom)
            rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                intent {
                    postSideEffect(WithdrawSideEffect.ToRedeemedCash(cash))
                    exchangeRedeemedPrizeAdJob?.cancel()
                    exchangeRedeemedPrizeAdJob = null
                }
            }
        }
    }

    private fun onExchangeCoupon(
        coupon: RedeemedPrize.Coupon,
        rewardAdFrom: String
    ) {
        if (walletBizKv.getCoinBalance() < coupon.coinPrice) {
            intent { postSideEffect(WithdrawSideEffect.InsufficientInCoins) }
            return
        }

        exchangeRedeemedPrizeAdJob?.cancel()

        exchangeRedeemedPrizeAdJob = viewModelScope.launch {
            rewardedAdManager.tryToShowRewardedLoadingDialog(rewardAdFrom)
            rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                intent {
                    postSideEffect(
                        WithdrawSideEffect.ToRedeemedCoupon(coupon)
                    )
                }

                exchangeRedeemedPrizeAdJob?.cancel()
                exchangeRedeemedPrizeAdJob = null
            }
        }
    }
}