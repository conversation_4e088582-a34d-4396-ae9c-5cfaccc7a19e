package dev.step.app.ui.dialog.rewardederror

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import org.koin.compose.koinInject

@Composable
fun RewardedErrorDialog(
    navUp: () -> Unit,
    popBackStack: () -> Unit,
) {
//    val rewardedAdHelper: MaxRewardedAdHelper = koinInject()
    val rewardedAdManager: RewardedAdManager = koinInject()

    AppDefWithCloseDialog(
        onDismiss = navUp,
        onClose = navUp,
        onConfirm = {
            popBackStack()
            rewardedAdManager.tryToShowRewardedLoadingDialog(instantlyLoad = true)
        },
        confirmText = stringResource(id = R.string.rewarded_ad_tips_error_btn_text),
        topPainter = painterResource(id = R.drawable.img_bigmoji_sad),
        adPlace = NativeAdPlace.Dialog,
        adPlaceName = "rewarded_ad_error"
    ) {
        Column(
            modifier = Modifier.bodyWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            BlankSpacer(height = 32.dp)

            Text(
                text = stringResource(id = R.string.rewarded_ad_tips_error_content_1),
                fontSize = 20.sp,
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 20.dp)
            )

            BlankSpacer(height = 20.dp)
        }
    }
}

@Preview
@Composable
fun RewardedErrorDialogPreview() {
    AppTheme {
        RewardedErrorDialog(navUp = { /*TODO*/ }) {

        }
    }

}