package dev.step.app.ui.common.game.sm

import android.content.Context
import android.util.AttributeSet
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import dev.step.app.R

class SlotMachineItemView : AppCompatImageView {
    private var mHeight = 0
    private var mNumber = 0

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    var data: Int
        get() = mNumber
        set(number) {
            mNumber = number

            val resId = getResId(number)

            setImageResource(resId)
        }

    fun setItemHeight(height: Int) {
        mHeight = height
    }

    fun setItemSelected(selected: Boolean) {}
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(mHeight, MeasureSpec.EXACTLY))
    }

    @DrawableRes
    private fun getResId(number: Int): Int {
        return when (number) {
            0 -> R.drawable.img_sm_blue
            1 -> R.drawable.img_sm_green
            2 -> R.drawable.img_sm_red
            3 -> R.drawable.img_sm_purple
            4 -> R.drawable.img_sm_yellow
//            5 -> R.drawable.img_free_spins_star
//            6 -> R.drawable.img_free_spins_flash
//            7 -> R.drawable.img_free_spins_clover
//            8 -> R.drawable.img_free_spins_diamond
//            9 -> R.drawable.img_free_spins_heart
            else -> R.drawable.img_sm_blue
        }
    }
}