package dev.step.app.ui.screen.reports

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.eventFlow
import dev.step.app.MainActivity
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.pojo.DayReportsData
import dev.step.app.data.pojo.MonthReportsData
import dev.step.app.data.pojo.WeekReportsData
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.reportschart.DayReportsChart
import dev.step.app.ui.common.reportschart.MonthReportsChart
import dev.step.app.ui.common.reportschart.WeekReportsChart
import dev.step.app.ui.screen.permissionsmanager.IgnoreBatteryOptimizationDialog
import dev.step.app.ui.theme.*
import dev.step.app.ui.theme.appimg.IcStatDistance
import dev.step.app.ui.theme.appimg.IcStatDuration
import dev.step.app.ui.theme.appimg.IcStatKcal
import dev.step.app.ui.theme.appimg.IcStatStep
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.datetime.Instant
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun ReportsScreenInHome(
    reportsViewModel: ReportsViewModel
) {
    val context = LocalContext.current
    var ignoringBatteryOptimizationDialogShowState: Boolean by remember { mutableStateOf(false) }

    val lifecycleOwner = LocalLifecycleOwner.current
    var lifecycleEvent by remember { mutableStateOf<Lifecycle.Event?>(null) }
    LaunchedEffect(Unit) {
        lifecycleOwner.lifecycle.eventFlow.onEach { event ->
            lifecycleEvent = event
        }.launchIn(this)
    }

    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()
    LaunchedEffect(lifecycleEvent) {
        delay(300)
        if (
            ignoringBatteryOptimizationRequester.canOpenBatteryOptimizationSettings(context.findActivity())
            && MainActivity.windowFocusChangedFlow.first() == true
        ) {
            ignoringBatteryOptimizationDialogShowState = true
        }
    }

    if (ignoringBatteryOptimizationDialogShowState) {
        IgnoreBatteryOptimizationDialog(
            onDismiss = { ignoringBatteryOptimizationDialogShowState = false },
            onRequest = {
                ignoringBatteryOptimizationRequester
                    .openSystemBatteryOptimizationSettings(context.findActivity())
                ignoringBatteryOptimizationDialogShowState = false
            },
            isForceOpen = false
        )
    }


    ReportsScreen(
        reportsViewModel = reportsViewModel
    )
}

@Composable
private fun ReportsScreen(
    reportsViewModel: ReportsViewModel
) {

    val viewState by reportsViewModel.collectAsState()

    LaunchedEffect(Unit) {
        reportsViewModel.onRefresh()
    }

    Scaffold(
        topBar = {
            ReportsTopTab(rsm = viewState.rsm, onRsmChange = { mode ->
                reportsViewModel.onRsmChange(mode)
            })
        },
        modifier = Modifier.fillMaxSize(), backgroundColor = AppColor.BackgroundDefault
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            ReportsStatistics(
                dayReportsData = viewState.dayReportsData,
                weekReportsData = viewState.weekReportsData,
                monthReportsData = viewState.monthReportsData,
                stepLengthCm = viewState.stepLengthCm,
                weightKg = viewState.weightKg,
                rsm = viewState.rsm,
                mus = viewState.mus,
                onDayReportChange = reportsViewModel::onDayChange,
                onWeekReportChange = reportsViewModel::onWeekChange,
                onMonthReportChange = reportsViewModel::onMonthChange
            )
        }
    }
}

@Composable
private fun ReportsTopTab(
    rsm: ReportsStatisticsMode,
    onRsmChange: (ReportsStatisticsMode) -> Unit,
    modifier: Modifier = Modifier,
) {
    val statusBarHeight = LocalContext.current.statusBarHeight


    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(AppColor.BackgroundDefault)
    ) {
        BlankSpacer(height = statusBarHeight)

        Text(
            text = stringResource(id = R.string.text_report),
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            color = AppColor.TextColorBlack,
            fontSize = 22.sp,
            fontWeight = FontWeight.Bold
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            ReportSelectionItem(
                text = stringResource(id = R.string.text_steps),
                icon = AppImg.IcStatStep,
                reportsStatisticsMode = ReportsStatisticsMode.Steps,
                isSelected = rsm == ReportsStatisticsMode.Steps,
                onItemClick = onRsmChange,
                modifier = Modifier.weight(1f),
            )
            BlankSpacer(width = 10.dp)
            ReportSelectionItem(
                text = stringResource(id = R.string.text_calories),
                icon = AppImg.IcStatKcal,
                reportsStatisticsMode = ReportsStatisticsMode.Calories,
                isSelected = rsm == ReportsStatisticsMode.Calories,
                onItemClick = onRsmChange,
                modifier = Modifier.weight(1f),
            )
            BlankSpacer(width = 10.dp)
            ReportSelectionItem(
                text = stringResource(id = R.string.text_time),
                icon = AppImg.IcStatDuration,
                reportsStatisticsMode = ReportsStatisticsMode.Time,
                isSelected = rsm == ReportsStatisticsMode.Time,
                onItemClick = onRsmChange,
                modifier = Modifier.weight(1f),
            )
            BlankSpacer(width = 10.dp)
            ReportSelectionItem(
                text = stringResource(id = R.string.text_distance),
                icon = AppImg.IcStatDistance,
                reportsStatisticsMode = ReportsStatisticsMode.Distance,
                isSelected = rsm == ReportsStatisticsMode.Distance,
                onItemClick = onRsmChange,
                modifier = Modifier.weight(1f),
            )
        }
        BlankSpacer(height = 6.dp)
    }
}

@OptIn(ExperimentalMaterialApi::class, ExperimentalFoundationApi::class)
@Composable
private fun ReportSelectionItem(
    text: String,
    icon: ImageVector,
    reportsStatisticsMode: ReportsStatisticsMode,
    isSelected: Boolean,
    onItemClick: (ReportsStatisticsMode) -> Unit,
    modifier: Modifier = Modifier,
) {
    val selectionBrush =
        if (isSelected)
            AppColor.FadedPrimaryBrushVertical
        else
            AppColor.FadedPrimaryAlphaBrushVertical

    val textColor = if (isSelected) Color.White else AppColor.Primary

    Surface(
        onClick = {
            onItemClick(reportsStatisticsMode)
        },
        modifier = modifier,
        shape = RoundedCornerShape7Dp,
    ) {
        Row(
            modifier = Modifier
                .background(selectionBrush)
                .fillMaxWidth()
                .padding(vertical = 9.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(16.dp),
                tint = textColor
            )
            BlankSpacer(width = 2.dp)
            Text(
                text = text,
                color = textColor,
                fontSize = 14.sp,
                modifier = Modifier.basicMarquee(),
                maxLines = 1
            )
        }

    }
}


@Composable
fun ReportsStatistics(
    dayReportsData: DayReportsData,
    weekReportsData: WeekReportsData,
    monthReportsData: MonthReportsData,
    stepLengthCm: Float,
    weightKg: Float,
    rsm: ReportsStatisticsMode,
    mus: MeasurementUnit,
    onDayReportChange: (Instant) -> Unit,
    onWeekReportChange: (Instant, Instant) -> Unit,
    onMonthReportChange: (Instant) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Column(modifier = Modifier.fillMaxWidth()) {
            DayReportsChart(
                dayReportsData = dayReportsData,
                stepLengthCm = stepLengthCm,
                weightKg = weightKg,
                rsm = rsm,
                mus = mus,
                onDayReportChange = onDayReportChange
            )
            WeekReportsChart(
                weekReportsData = weekReportsData,
                stepLengthCm = stepLengthCm,
                weightKg = weightKg,
                rsm = rsm,
                mus = mus,
                onWeekReportChange = onWeekReportChange
            )
            MonthReportsChart(
                monthReportsData = monthReportsData,
                stepLengthCm = stepLengthCm,
                weightKg = weightKg,
                rsm = rsm,
                mus = mus,
                onMonthReportChange = onMonthReportChange
            )
        }
    }


}

@Preview
@Composable
fun ReportsTopTabPreview() {
    var rsm by remember {
        mutableStateOf<ReportsStatisticsMode>(ReportsStatisticsMode.Steps)
    }
}
