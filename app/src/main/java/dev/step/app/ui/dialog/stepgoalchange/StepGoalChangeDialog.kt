package dev.step.app.ui.dialog.stepgoalchange

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Add
import androidx.compose.material.icons.rounded.Remove
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
//import dev.step.app.androidplatform.biz.MaxInterstitialAdHelper
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.AppDefDialog
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.RoundedCornerShape7Dp
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.delay
import org.koin.compose.koinInject

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun StepGoalChangeDialog(
    navUp: () -> Unit,
    userSettingsDataKv: UserSettingsDataKv = koinInject(),
) {
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val scope = rememberCoroutineScope()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator)

    val inputFocusRequester = remember { FocusRequester() }

    var stepGoalTextFieldValue by remember {
        mutableStateOf(
            TextFieldValue(
                text = "",
                selection = TextRange(0)
            )
        )
    }

    LaunchedEffect(Unit) {
        val stepGoalText = userSettingsDataKv.stepsGoal.toString()
        stepGoalTextFieldValue = stepGoalTextFieldValue.copy(
            text = stepGoalText,
            selection = TextRange(stepGoalText.lastIndex + 1)
        )
    }

    LaunchedEffect(Unit) {
        delay(200)
        inputFocusRequester.requestFocus()
    }

    AppDefDialog(
        onDismiss = navUp,
        onCancel = navUp,
        onConfirm = {
            runCatching {
                stepGoalTextFieldValue.text.toInt()
            }.onSuccess { stepGoal ->
                userSettingsDataKv.changeStepsGoal(stepGoal)
                OnBack()
//                interstitialAdHelper.tryToShowAd("save_step_goal", onAdShowingOrSkip = { navUp() })
            }.onFailure {
                it.printStackTrace()
            }
        },
    ) {
        Column(
            modifier = Modifier
                .bodyWidth()
                .padding(bottom = 28.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Text(
                text = stringResource(id = dev.step.app.R.string.text_step_goal),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
            )

            BlankSpacer(height = 26.dp)


            Row(modifier = Modifier.bodyWidth(), verticalAlignment = Alignment.CenterVertically) {
                Surface(
                    onClick = {
                        runCatching {
                            val sgt = stepGoalTextFieldValue.text.toIntOrNull() ?: 0
                            if (sgt <= 0) return@runCatching

                            stepGoalTextFieldValue =
                                stepGoalTextFieldValue.copy(text = (stepGoalTextFieldValue.text.toInt() - 100).toString())
                        }
                    },
                    shape = CircleShape,
                    color = AppColor.GrayBackground
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Remove,
                        contentDescription = null,
                        modifier = Modifier
                            .size(24.dp),
                        tint = AppColor.Primary
                    )
                }

                BlankSpacer(width = 16.dp)

                OutlinedTextField(
                    value = stepGoalTextFieldValue,
                    onValueChange = {
                        stepGoalTextFieldValue = it
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    colors = TextFieldDefaults.textFieldColors(
                        textColor = AppColor.Primary,
                        backgroundColor = AppColor.GrayBackground
                    ),
                    textStyle = LocalTextStyle.current.copy(
                        fontSize = 23.sp,
                        textAlign = TextAlign.Center
                    ),
                    modifier = Modifier
                        .focusRequester(inputFocusRequester)
                        .width(128.dp),
                    shape = RoundedCornerShape7Dp,
                    maxLines = 1,
                )

                BlankSpacer(width = 16.dp)

                Surface(
                    onClick = {
                        runCatching {
                            val sgt = stepGoalTextFieldValue.text.toIntOrNull() ?: 0
                            stepGoalTextFieldValue = if (sgt <= 0) {
                                stepGoalTextFieldValue.copy(text = "100")
                            } else {
                                stepGoalTextFieldValue.copy(text = (stepGoalTextFieldValue.text.toInt() + 100).toString())
                            }
                        }
                    },
                    shape = CircleShape,
                    color = AppColor.GrayBackground
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Add,
                        contentDescription = null,
                        modifier = Modifier
                            .size(24.dp),
                        tint = AppColor.Primary
                    )
                }
            }

        }
    }
}
