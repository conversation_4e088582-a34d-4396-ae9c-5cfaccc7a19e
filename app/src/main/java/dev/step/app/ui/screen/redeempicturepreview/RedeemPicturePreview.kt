package dev.step.app.ui.screen.redeempicturepreview

import android.Manifest
import android.os.Build
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.rememberNavigator
import com.roudikk.guia.extensions.pop
import dev.step.app.R
import dev.step.app.ScreenDestinationNode
import dev.step.app.androidplatform.androidcomponent.global.showToast
import dev.step.app.androidplatform.saveDrawableToExternalStorage
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.FancyFadedBgButton
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

@Parcelize
data class RedeemPicturePreviewNode(
    @DrawableRes val drawableResId: Int
) : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        RedeemPicturePreview(drawableResId, navigator)
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun RedeemPicturePreview(
    @DrawableRes drawableResId: Int,
    navigator: Navigator,
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val rememberPermissionState =
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q)
            rememberPermissionState(permission = Manifest.permission.WRITE_EXTERNAL_STORAGE)
        else null


    Scaffold(
        topBar = {
            RedeemPicturePreviewTopBar(navUp = navigator::pop)
        },
        bottomBar = {
            RedeemPicturePreviewBottomBar(
                drawableResId = drawableResId,
                onSave = {
                    if (rememberPermissionState?.status?.isGranted == false) {
                        rememberPermissionState.launchPermissionRequest()
                    } else {
                        scope.launch {
                            context.saveDrawableToExternalStorage(
                                it,
                                fileName = "picture_$it.png"
                            )
                            showToast(text = context.getString(R.string.text_saved_successfully))
                        }
                    }
                },
                modifier = Modifier.navigationBarsPadding()
            )
        }
    ) {
        Image(
            painter = painterResource(id = drawableResId),
            contentDescription = null,
            modifier = Modifier
                .padding(it)
                .padding(horizontal = 30.dp)
                .fillMaxSize()
                .navigationBarsPadding()
        )
    }
}

@Composable
private fun RedeemPicturePreviewBottomBar(
    @DrawableRes drawableResId: Int,
    onSave: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.bodyWidth()) {
        BlankSpacer(height = 8.dp)
        FancyFadedBgButton(
            text = stringResource(R.string.text_add_to_gallery),
            onClick = { onSave(drawableResId) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 72.dp)
        )
        BlankSpacer(height = 48.dp)
    }
}

@Composable
private fun RedeemPicturePreviewTopBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier
        .padding(vertical = 8.dp)
        .fillMaxWidth()
) {
    Row(
        modifier = modifier.statusBarsPadding(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        IconButton(navUp) {
            Icon(
                imageVector = Icons.Rounded.KeyboardArrowLeft,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = AppColor.TextColorBlack
            )
        }

        Text(
            text = stringResource(R.string.text_reward),
            fontSize = 20.sp,
        )
    }
}

@Preview
@Composable
private fun RedeemPicturePreviewPreview() {
    AppTheme {
        RedeemPicturePreview(
            drawableResId = R.drawable.img_redeem_picture_1,
            navigator = rememberNavigator()
        )
    }
}