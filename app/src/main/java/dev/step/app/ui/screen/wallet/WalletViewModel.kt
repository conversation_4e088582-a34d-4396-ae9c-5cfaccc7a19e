package dev.step.app.ui.screen.wallet

import androidx.lifecycle.ViewModel

import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.WalletBizKv
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class WalletViewModel(
    private val walletBizKv: WalletBizKv,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val userOperateDataKv: UserOperateDataKv,
) : ViewModel(), ContainerHost<WalletViewState, WalletSideEffect> {

    override val container: Container<WalletViewState, WalletSideEffect> =
        container(WalletViewState.Empty)

    fun onRefresh() = intent {
        val tz = TimeZone.currentSystemDefault()

        val nowInstant = nowInstant()
        val todayOfWeek = nowInstant.toLocalDateTime(tz).dayOfWeek
        val coins = walletBizKv.getCoinBalance()
        val signIn = walletBizKv.getSignIn(todayOfWeek)

        val tenjinAttribution = userOperateDataKv.tenjinAttr
        val isOrganic = tenjinAttribution.isOrganic()

        val coinsExchangeUnit = if (isOrganic) {
            remoteConfigHelper.getOUserWithDrawUS().let { wd ->
                wd?.coinsExchangeUnit
            }
        } else {
            remoteConfigHelper.getPUserWithDrawUS().let { wd ->
                wd?.coinsExchangeUnit
            }
        }

        val signInRewardCoins = remoteConfigHelper.getDailyTask()?.sign_in_rewarded_coins

        reduce {
            state.copy(
                coins = coins,
                signIn = signIn,
                todayOfWeek = todayOfWeek,
                signInRewardCoins = signInRewardCoins,
                coinsExchangeUnit = coinsExchangeUnit,

                useWithdraw = !isOrganic
            )
        }
    }
}