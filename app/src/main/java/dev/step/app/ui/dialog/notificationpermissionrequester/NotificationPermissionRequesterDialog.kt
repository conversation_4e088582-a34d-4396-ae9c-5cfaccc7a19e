package dev.step.app.ui.dialog.notificationpermissionrequester

import android.os.Build
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import com.roudikk.guia.backstack.NavBackHandler
import dev.step.app.R
import dev.step.app.androidplatform.biz.NotificationPermissionRequester
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.screen.permissionsmanager.NotificationPermissionDialog
import dev.step.app.ui.theme.AppImg
import dev.step.app.ui.theme.appimg.ImgNotiTips
import org.koin.compose.koinInject

@Suppress("LocalVariableName")
@Composable
fun NotificationPermissionRequesterDialog(
    navUp: () -> Unit,
) {
    val context = LocalContext.current
    val notificationPermissionRequester: NotificationPermissionRequester = koinInject()
    val splashHelper: SplashHelper = koinInject()

    val _navUp = {
        notificationPermissionRequester.requestCounterIncrement()
        navUp()
    }

    NavBackHandler {
        _navUp()
    }

    NotificationPermissionDialog(
        onDismiss = _navUp,
        onRequest = {
            _navUp()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                notificationPermissionRequester.customRequesterOpenToNotificationSettings(
                    activity = context.findActivity()
                )
            }
        }
    )

//    AppDefWithCloseDialog(
//        onDismiss = _navUp,
//        onClose = _navUp,
//        onConfirm = {
//            _navUp()
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                notificationPermissionRequester.customRequesterOpenToNotificationSettings(
//                    activity = context.findActivity()
//                )
//            }
//        },
//        confirmText = stringResource(id = R.string.permission_req_button_grant),
//        topPainter = null,
//        adPlaceholder = null,
//        adPlaceName = null,
//        showCloseIcon = false,
//        bottomPadding = 40.dp,
//        properties = DialogProperties(
//            usePlatformDefaultWidth = false,
//            dismissOnClickOutside = false
//        ),
//        content = {
//            Column(
//                horizontalAlignment = Alignment.CenterHorizontally,
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(horizontal = 18.dp)
//            ) {
//                BlankSpacer(height = 20.dp)
//
//                Text(
//                    text = stringResource(id = R.string.noti_permission_req_title),
//                    fontSize = 17.sp
//                )
//
//                BlankSpacer(height = 14.dp)
//
//                Image(
//                    imageVector = AppImg.ImgNotiTips,
//                    contentDescription = null,
//                    modifier = Modifier.size(150.dp)
//                )
//
//                BlankSpacer(height = 14.dp)
//
//                Text(
//                    text = stringResource(id = R.string.noti_permission_req_content),
//                    textAlign = TextAlign.Center
//                )
//
//                BlankSpacer(height = 30.dp)
//            }
//        })
}

@Preview
@Composable
private fun NotificationPermissionRequesterDialogPreview() {
    NotificationPermissionRequesterDialog(navUp = {})
}
