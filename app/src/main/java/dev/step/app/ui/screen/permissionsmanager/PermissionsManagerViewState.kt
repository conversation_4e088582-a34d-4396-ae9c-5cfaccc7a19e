package dev.step.app.ui.screen.permissionsmanager

data class PermissionsManagerViewState(
    val isActivityRecognitionGranted: Boolean = false,
    val isIgnoreBatteryOptimizationsGranted: Boolean = false,
    val isNotificationGranted: <PERSON>olean = false,

    val showActivityRecognitionDialog: <PERSON>olean = false,
    val showIgnoreBatteryOptimizationsDialog: <PERSON>olean = false,
    val showNotificationDialog: <PERSON>olean = false,
) {
    companion object {
        val Empty = PermissionsManagerViewState()
    }
}