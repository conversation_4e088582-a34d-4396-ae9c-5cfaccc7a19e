package dev.step.app.ui.screen.profile

import androidx.lifecycle.ViewModel
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.cmToFtIn
import dev.step.app.androidplatform.ext.scale
import dev.step.app.data.adt.GenderSetting
import dev.step.app.data.adt.MotionSensorSensitivity
import dev.step.app.data.kvstore.UserSettingsDataKv
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class ProfileViewModel(
    private val userSettingsDataKv: UserSettingsDataKv,
    val splashHelper: SplashHelper,
) : ViewModel(), ContainerHost<ProfileViewState, Unit> {

    override val container: Container<ProfileViewState, Unit> = container(ProfileViewState.Empty)

    fun onRefreshUi() = intent {
        val stepGoal = userSettingsDataKv.stepsGoal
        val mss = userSettingsDataKv.motionSensorSensitivity ?: MotionSensorSensitivity.Lv1
        val bwLb = userSettingsDataKv.bodyWeightLb
        val bwKg = userSettingsDataKv.bodyWeightKg
        val bhFtIn = userSettingsDataKv.bodyHeightFtIn
        val bhCm = userSettingsDataKv.bodyHeightCm
        val gender = userSettingsDataKv.gender ?: GenderSetting.Others
        var slFtIn = userSettingsDataKv.stepLengthFtIn
        val slCm = userSettingsDataKv.stepLengthCm
        val mus = userSettingsDataKv.measurementUnit


        if (slFtIn.first == null || slFtIn.second == null) {
            slFtIn = cmToFtIn(slCm)
        }

        reduce {
            state.copy(
                stepGoal = stepGoal,
                sensitivity = mss,
                bodyWeightLb = bwLb.scale(1),
                bodyWeightKg = bwKg.scale(1),
                bodyHeightFtIn = bhFtIn,
                bodyHeightCm = bhCm.scale(0).toInt(),
                gender = gender,
                stepLengthFtIn = slFtIn,
                stepLengthCm = slCm.scale(0).toInt(),
                measureUnit = mus
            )
        }
    }
}
