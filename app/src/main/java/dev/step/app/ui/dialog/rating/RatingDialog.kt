package dev.step.app.ui.dialog.rating

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Star
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.androidplatform.biz.RatingHelper
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.ui.common.AppDefWithCloseDialog
import org.koin.compose.koinInject
import dev.step.app.R
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth

@Composable
fun RatingDialog(
    navUp: () -> Unit,
    toRateOnFeedback: () -> Unit,
    userOperateDataStore: UserOperateDataKv = koinInject(),
    reviewsHelper: RatingHelper = koinInject()
) {
    val context = LocalContext.current

    var selectedStars by remember { mutableIntStateOf(0) }

    AppDefWithCloseDialog(
        onDismiss = {
            navUp()
            logEventRecord("rate_dialog_exit")
        },
        onClose = {
            navUp()
            logEventRecord("rate_dialog_exit")
        },
        onConfirm = {
            navUp()
            logEventRecord("rate_dialog_later")
        },
        confirmText = stringResource(R.string.title_maybe_later),
        topPainter = null,
        adPlace = null,
        adPlaceName = null,
        content = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                BlankSpacer(height = 16.dp)

                Image(
                    painter = painterResource(id = R.drawable.img_rating),
                    contentDescription = null,
                    modifier = Modifier.size(110.dp)
                )

                BlankSpacer(height = 16.dp)

                Text(
                    text = stringResource(R.string.rating_title),
                    color = Color.Black,
                    fontSize = 20.sp
                )

                BlankSpacer(height = 10.dp)

                Text(
                    text = stringResource(R.string.rating_tips),
                    color = Color.Black,
                    fontSize = 15.sp,
                    modifier = Modifier
                        .bodyWidth()
                        .padding(horizontal = 16.dp),
                    textAlign = TextAlign.Center
                )

                BlankSpacer(height = 10.dp)

                RateStar(maxStarCount = 5, selectedStarCount = 0, onStarSelect = {
                    logEventRecord("rate_dialog_tapstar")

                    selectedStars = it

                    if (it >= 4) {
                        reviewsHelper.openGooglePlayInAppReviews(context.findActivity())
                        navUp()
                    } else {
                        toRateOnFeedback()
                        logEventRecord("rate_dialog_feedback")
                    }

                    userOperateDataStore.setOpenRatingDialogTimes(3)
                })

                BlankSpacer(height = 10.dp)
            }
        }
    )
}

@Composable
fun RateStar(
    maxStarCount: Int,
    selectedStarCount: Int,
    onStarSelect: (selectedStarCount: Int) -> Unit,
    modifier: Modifier = Modifier,
    starSize: Dp = 40.dp,
    starSpacing: Dp = 8.dp
) {
    Row(modifier = modifier) {
        (0 until maxStarCount).forEach { index ->
            val tintColor =
                if (selectedStarCount < index + 1) AppColor.TextColorGray else Color(0xFFFDAD23)

            Icon(
                imageVector = Icons.Rounded.Star,
                contentDescription = null,
                tint = tintColor,
                modifier = Modifier
                    .size(starSize)
                    .clickable {
                        onStarSelect(index + 1)
                    }
            )

            if (maxStarCount != index + 1) {
                BlankSpacer(width = starSpacing)
            }
        }
    }
}

@Preview
@Composable
private fun RatingDialogPreview() {
    AppTheme {
        RatingDialog(navUp = { /*TODO*/ }, toRateOnFeedback = { /*TODO*/ })
    }
}