package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcProfileMeasureUnit: ImageVector
    get() {
        if (_icProfileMeasureUnit != null) {
            return _icProfileMeasureUnit!!
        }
        _icProfileMeasureUnit = Builder(name = "IcProfileMeasureUnit", defaultWidth = 70.0.dp,
                defaultHeight = 70.0.dp, viewportWidth = 70.0f, viewportHeight = 70.0f).apply {
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 1.0f)
                lineTo(35.0f, 1.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 69.0f, 35.0f)
                lineTo(69.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 69.0f)
                lineTo(35.0f, 69.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 1.0f, 35.0f)
                lineTo(1.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 1.0f)
                close()
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 0.5f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(18.0f, 23.125f)
                lineTo(18.0f, 51.0f)
                curveTo(18.0f, 52.6569f, 19.3431f, 54.0f, 21.0f, 54.0f)
                lineTo(32.9499f, 54.0f)
                curveTo(34.6068f, 54.0f, 35.9499f, 52.6569f, 35.9499f, 51.0f)
                lineTo(35.9499f, 19.0f)
                curveTo(35.9499f, 17.3431f, 34.6068f, 16.0f, 32.9499f, 16.0f)
                lineTo(21.0f, 16.0f)
                curveTo(19.3431f, 16.0f, 18.0f, 17.3431f, 18.0f, 19.0f)
                lineTo(18.0f, 23.125f)
                close()
                moveTo(20.3933f, 46.875f)
                lineTo(29.0867f, 46.875f)
                curveTo(29.7425f, 46.875f, 30.2742f, 46.3433f, 30.2742f, 45.6875f)
                curveTo(30.2742f, 45.0317f, 29.7425f, 44.5f, 29.0867f, 44.5f)
                lineTo(20.3933f, 44.5f)
                lineTo(20.3933f, 44.5f)
                lineTo(20.3933f, 39.75f)
                lineTo(24.2486f, 39.75f)
                curveTo(24.9044f, 39.75f, 25.4361f, 39.2183f, 25.4361f, 38.5625f)
                curveTo(25.4361f, 37.9067f, 24.9044f, 37.375f, 24.2486f, 37.375f)
                lineTo(20.3933f, 37.375f)
                lineTo(20.3933f, 37.375f)
                lineTo(20.3933f, 32.625f)
                lineTo(29.0867f, 32.625f)
                curveTo(29.7425f, 32.625f, 30.2742f, 32.0933f, 30.2742f, 31.4375f)
                curveTo(30.2742f, 30.7817f, 29.7425f, 30.25f, 29.0867f, 30.25f)
                lineTo(20.3933f, 30.25f)
                lineTo(20.3933f, 30.25f)
                lineTo(20.3933f, 25.5f)
                lineTo(24.2486f, 25.5f)
                curveTo(24.9044f, 25.5f, 25.4361f, 24.9683f, 25.4361f, 24.3125f)
                curveTo(25.4361f, 23.6567f, 24.9044f, 23.125f, 24.2486f, 23.125f)
                lineTo(20.3933f, 23.125f)
                lineTo(20.3933f, 23.125f)
                lineTo(20.3933f, 19.375f)
                curveTo(20.3933f, 18.8227f, 20.841f, 18.375f, 21.3933f, 18.375f)
                lineTo(32.5566f, 18.375f)
                curveTo(33.1089f, 18.375f, 33.5566f, 18.8227f, 33.5566f, 19.375f)
                lineTo(33.5566f, 50.625f)
                curveTo(33.5566f, 51.1773f, 33.1089f, 51.625f, 32.5566f, 51.625f)
                lineTo(21.3933f, 51.625f)
                curveTo(20.841f, 51.625f, 20.3933f, 51.1773f, 20.3933f, 50.625f)
                lineTo(20.3933f, 46.875f)
                lineTo(20.3933f, 46.875f)
                close()
            }
            path(fill = SolidColor(Color(0xFF2D3142)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 0.5f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(50.6593f, 22.5752f)
                curveTo(51.951f, 22.5765f, 52.998f, 23.6149f, 53.0f, 24.8968f)
                lineTo(53.0f, 24.8968f)
                lineTo(53.0f, 46.1186f)
                curveTo(52.9993f, 46.6049f, 52.8284f, 47.0758f, 52.5165f, 47.4521f)
                lineTo(52.5165f, 47.4521f)
                lineTo(47.8223f, 53.085f)
                curveTo(47.7852f, 53.1296f, 47.7442f, 53.1708f, 47.6999f, 53.2083f)
                curveTo(47.2782f, 53.565f, 46.6472f, 53.5122f, 46.2906f, 53.0905f)
                lineTo(46.2906f, 53.0905f)
                lineTo(41.5288f, 47.4604f)
                curveTo(41.2102f, 47.0829f, 41.0352f, 46.6063f, 41.0346f, 46.1138f)
                lineTo(41.0346f, 46.1138f)
                lineTo(41.0346f, 24.8968f)
                curveTo(41.0365f, 23.6154f, 42.0828f, 22.5771f, 43.374f, 22.5752f)
                lineTo(43.374f, 22.5752f)
                close()
                moveTo(49.8468f, 46.9284f)
                lineTo(44.2021f, 46.9284f)
                lineTo(47.0454f, 50.2902f)
                lineTo(49.8468f, 46.9284f)
                close()
                moveTo(50.6067f, 29.7299f)
                lineTo(43.386f, 29.7299f)
                lineTo(43.4219f, 44.5534f)
                lineTo(50.6067f, 44.5534f)
                lineTo(50.6067f, 29.7299f)
                close()
                moveTo(50.6067f, 24.8968f)
                lineTo(43.3728f, 24.9502f)
                lineTo(43.3788f, 27.3561f)
                lineTo(50.6067f, 27.3561f)
                lineTo(50.6067f, 24.8968f)
                close()
            }
        }
        .build()
        return _icProfileMeasureUnit!!
    }

private var _icProfileMeasureUnit: ImageVector? = null
