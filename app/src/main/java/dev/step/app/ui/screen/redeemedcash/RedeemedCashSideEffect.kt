package dev.step.app.ui.screen.redeemedcash

sealed interface RedeemedCashSideEffect {
    data class Redeemed(val cashText: String, val accountAddress: String) : RedeemedCashSideEffect
    data object AccountAddressError : RedeemedCashSideEffect
}

fun handleRedeemedCashSideEffect(
    it: RedeemedCashSideEffect,
    onRedeemed: (cashText: String, accountAddress: String) -> Unit,
    onAccountAddressError: () -> Unit,
) {
    when (it) {
        is RedeemedCashSideEffect.Redeemed -> onRedeemed(it.cashText, it.accountAddress)
        is RedeemedCashSideEffect.AccountAddressError -> onAccountAddressError()
    }
}