@file:Suppress("MemberVisibilityCanBePrivate")

package dev.step.app.ui.common.game.sc

import android.content.Context
import android.content.res.TypedArray
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.drawable.BitmapDrawable
import android.os.AsyncTask
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import androidx.core.graphics.toRect
import dev.step.app.R
import dev.step.app.androidplatform.CoroutinesAsyncTask
import dev.step.app.androidplatform.viewScope
import kotlin.math.abs
import kotlin.math.min
import kotlin.math.roundToInt


class ScratchView : View {
    /**
     * 遮罩 Paint
     */
    private var mMaskPaint: Paint? = null

    /**
     * 产生遮罩效果的 Bitmap
     */
    private var mMaskBitmap: Bitmap? = null

    /**
     * 绘制遮罩的 Canvas
     */
    private var mMaskCanvas: Canvas? = null

    /**
     * 普通绘制 Bitmap 用的 Paint
     */
    private var mBitmapPaint: Paint? = null

    /**
     * 水印
     */
    private var mWatermark: BitmapDrawable? = null

    /**
     * 橡皮檫画笔
     */
    private var mErasePaint: Paint? = null

    /**
     * 擦除轨迹
     */
    private var mErasePath: Path? = null

    /**
     * 擦除效果起始点的x坐标
     */
    private var mStartX = 0f

    /**
     * 擦除效果起始点的y坐标
     */
    private var mStartY = 0f

    /**
     * 最小滑动距离
     */
    private var mTouchSlop = 0

    /**
     * 完成擦除
     */
    private var mIsCompleted = false

    /**
     * 最大擦除比例
     */
    private var mMaxPercent = DEFAULT_PERCENT

    /**
     * 当前擦除比例
     */
    private var mPercent = 0

    /**
     * 存放蒙层像素信息的数组
     */
    private var mPixels: IntArray? = null
    private var mEraseStatusListener: EraseStatusListener? = null

    constructor(context: Context) : super(context) {
        val typedArray = context.obtainStyledAttributes(R.styleable.ScratchView)
        init(typedArray)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ScratchView)
        init(typedArray)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        val typedArray =
            context.obtainStyledAttributes(attrs, R.styleable.ScratchView, defStyleAttr, 0)
        init(typedArray)
    }

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        val typedArray = context.obtainStyledAttributes(
            attrs,
            R.styleable.ScratchView,
            defStyleAttr,
            defStyleRes
        )
        init(typedArray)
    }

    private fun init(typedArray: TypedArray) {
        val maskColor = typedArray.getColor(R.styleable.ScratchView_maskColor, DEFAULT_MASKER_COLOR)
        val watermarkResId = typedArray.getResourceId(R.styleable.ScratchView_watermark, -1)
        val eraseSize = typedArray.getFloat(R.styleable.ScratchView_eraseSize, DEFAULT_ERASER_SIZE)
        mMaxPercent = typedArray.getInt(R.styleable.ScratchView_maxPercent, DEFAULT_PERCENT)
        typedArray.recycle()
        mMaskPaint = Paint().apply {
            isAntiAlias = true //抗锯齿
            isDither = true //防抖
        }
        setMaskColor(maskColor)
        mBitmapPaint = Paint().apply {
            isAntiAlias = true
            isDither = true
        }
        setWatermark(watermarkResId)
        mErasePaint = Paint().apply {
            isAntiAlias = true
            isDither = true
            xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR) //设置擦除效果
            style = Paint.Style.STROKE
            strokeCap = Paint.Cap.ROUND //设置笔尖形状，让绘制的边缘圆滑
        }
        setEraserSize(eraseSize)
        mErasePath = Path()
        val viewConfiguration = ViewConfiguration.get(context)
        mTouchSlop = viewConfiguration.scaledTouchSlop
    }

    /**
     * 设置橡皮檫尺寸大小（默认大小是 60）
     *
     * @param eraserSize 橡皮檫尺寸大小
     */
    fun setEraserSize(eraserSize: Float) {
        mErasePaint?.strokeWidth = eraserSize
    }

    /**
     * 设置蒙板颜色
     *
     * @param color 十六进制颜色值，如：0xffff0000（不透明的红色）
     */
    fun setMaskColor(color: Int) {
        mMaskPaint?.color = color
    }

    /**
     * 设置最大的擦除比例
     *
     * @param max 大于0，小于等于100
     */
    fun setMaxPercent(max: Int) {
        if (max > 100 || max <= 0) {
            return
        }
        mMaxPercent = max
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val measuredWidth = measureSize(widthMeasureSpec)
        val measuredHeight = measureSize(heightMeasureSpec)
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        mMaskBitmap?.let {
            canvas.drawBitmap(
                it,
                0f,
                0f,
                mBitmapPaint
            ) //绘制图层遮罩
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                startErase(event.x, event.y)
                invalidate()
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                erase(event.x, event.y)
                invalidate()
                return true
            }

            MotionEvent.ACTION_UP -> {
                stopErase()
                invalidate()
                return true
            }

            else -> {}
        }
        return super.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        createMasker(w, h)
    }

    /**
     * 创建蒙层
     *
     * @param width
     * @param height
     */
    private fun createMasker(width: Int, height: Int) {
        mMaskBitmap = Bitmap.createBitmap(
            width,
            height.let { if (height == 0) 1 else height },
            Bitmap.Config.ARGB_8888
        )
        mMaskCanvas = Canvas(mMaskBitmap!!)
        val rect = RectF(0f, 0f, width.toFloat(), height.toFloat())
        mMaskCanvas?.drawRoundRect(rect, 20f, 20f, mMaskPaint!!) //绘制生成和控件大小一致的遮罩 Bitmap
        if (mWatermark != null) {
            val bounds = RectF(rect)
            mWatermark?.bounds = bounds.toRect()
            mWatermark?.draw(mMaskCanvas!!)
        }
        mPixels = IntArray(width * height)
    }

    private fun measureSize(measureSpec: Int): Int {
        var size = 0
        val specMode = MeasureSpec.getMode(measureSpec)
        val specSize = MeasureSpec.getSize(measureSpec)
        if (specMode == MeasureSpec.EXACTLY) {
            size = specSize
        } else {
            if (specMode == MeasureSpec.AT_MOST) {
                size = min(size, specSize)
            }
        }
        return size
    }

    /**
     * 开始擦除
     *
     * @param x
     * @param y
     */
    private fun startErase(x: Float, y: Float) {
        mErasePath?.reset()
        mErasePath?.moveTo(x, y)
        mStartX = x
        mStartY = y
    }

    /**
     * 擦除
     *
     * @param x
     * @param y
     */
    private fun erase(x: Float, y: Float) {
        val dx = abs(x - mStartX).toInt()
        val dy = abs(y - mStartY).toInt()
        if (dx >= mTouchSlop || dy >= mTouchSlop) {
            mStartX = x
            mStartY = y
            mErasePath?.lineTo(x, y)
            mMaskCanvas?.drawPath(mErasePath!!, mErasePaint!!)
            //updateErasePercent();
            mErasePath?.reset()
            mErasePath?.moveTo(mStartX, mStartY)
        }
    }

    private fun updateErasePercent() {
        val width = width
        val height = height
        val at = object : CoroutinesAsyncTask<Int?, Int?, Boolean>(viewScope) {
            override fun doInBackground(vararg params: Int?): Boolean {
                val _width = params[0] ?: return false
                val _height = params[1] ?: return false
                mMaskBitmap?.getPixels(
                    mPixels ?: intArrayOf(),
                    0,
                    _width,
                    0,
                    0,
                    _width,
                    _height
                ) //获取覆盖图层中所有的像素信息，stride用于表示一行的像素个数有多少
                var erasePixelCount = 0f //擦除的像素个数
                val totalPixelCount = (_width * _height).toFloat() //总像素个数
                var pos = 0
                while (pos < totalPixelCount) {
                    if (mPixels?.getOrNull(pos) == 0) { //透明的像素值为0
                        erasePixelCount++
                    }
                    pos++
                }
                var percent = 0
                if (erasePixelCount >= 0 && totalPixelCount > 0) {
                    percent = (erasePixelCount * 100 / totalPixelCount).roundToInt()
                    publishProgress(percent)
                }
                return percent >= mMaxPercent
            }

            override fun onProgressUpdate(vararg values: Int?) {
                super.onProgressUpdate(*values)
                mPercent = values.getOrNull(0) ?: 0
                onPercentUpdate()
            }

            override fun onPostExecute(result: Boolean?) {
                super.onPostExecute(result)
                if (result == true && !mIsCompleted) { //标记擦除，并完成回调
                    mIsCompleted = true
                    if (mEraseStatusListener != null) {
                        mEraseStatusListener?.onCompleted(this@ScratchView)
                    }
                }
            }
        }
        at.execute(width, height)
    }

    /**
     * 停止擦除
     */
    private fun stopErase() {
        mStartX = 0f
        mStartY = 0f
        mErasePath?.reset()
        updateErasePercent()
    }

    private fun onPercentUpdate() {
        mEraseStatusListener?.onProgress(mPercent)
    }

    /**
     * 设置擦除监听器
     *
     * @param listener
     */
    fun setEraseStatusListener(listener: EraseStatusListener?) {
        mEraseStatusListener = listener
    }

    /**
     * 设置水印图标
     *
     * @param resId 图标资源id，-1表示去除水印
     */
    fun setWatermark(resId: Int) {
        if (resId == -1) {
            mWatermark = null
        } else {
            val bitmap = BitmapFactory.decodeResource(resources, resId)
            mWatermark = BitmapDrawable(bitmap)
            mWatermark?.setTileModeXY(Shader.TileMode.REPEAT, Shader.TileMode.REPEAT)
        }
    }

    /**
     * 重置为初始状态
     */
    fun reset() {
        mIsCompleted = false
        val width = width
        val height = height
        createMasker(width, height)
        invalidate()
        updateErasePercent()
    }

    /**
     * 清除整个图层
     */
    fun clear() {
        val width = width
        val height = height
        mMaskBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        mMaskCanvas = Canvas(mMaskBitmap!!)
        val rect = Rect(0, 0, width, height)
        mMaskCanvas?.drawRect(rect, mErasePaint!!)
        invalidate()
        updateErasePercent()
    }

    /**
     * 擦除状态监听器
     */
    interface EraseStatusListener {
        /**
         * 擦除进度
         *
         * @param percent 进度值，大于0，小于等于100；
         */
        fun onProgress(percent: Int)

        /**
         * 擦除完成回调函数
         *
         * @param view
         */
        fun onCompleted(view: View?)
    }

    companion object {
        private val TAG = ScratchView::class.java.simpleName

        /**
         * 最小的橡皮擦尺寸大小
         */
        private const val DEFAULT_ERASER_SIZE = 60f

        /**
         * 默认蒙板的颜色
         */
        private const val DEFAULT_MASKER_COLOR = -0x333334

        /**
         * 默认擦除比例
         */
        private const val DEFAULT_PERCENT = 70

        /**
         * 最大擦除比例
         */
        private const val MAX_PERCENT = 100
    }
}