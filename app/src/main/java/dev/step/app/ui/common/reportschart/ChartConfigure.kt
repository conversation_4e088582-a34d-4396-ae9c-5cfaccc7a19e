package dev.step.app.ui.common.reportschart

import android.content.Context
import android.graphics.Typeface
import android.view.ViewGroup
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.content.res.ResourcesCompat
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import dev.step.app.R
import dev.step.app.ui.screen.reports.ReportsStatisticsMode
import dev.step.app.ui.theme.AppColor

@Suppress("ObjectPropertyName")
private var _statTypeface: Typeface? = null

private fun statTypeface(context: Context): Typeface? {
    if (_statTypeface != null) return _statTypeface

    val tf = ResourcesCompat.getFont(context, R.font.gabarito_medium)
    _statTypeface = tf
    return _statTypeface
}

internal fun LineChart.basicConfigure(
    rsm: ReportsStatisticsMode,
    yMax: Float,
    yOffset: Float,
) {
    layoutParams = ViewGroup.LayoutParams(
        ViewGroup.LayoutParams.MATCH_PARENT,
        ViewGroup.LayoutParams.MATCH_PARENT
    )

    description.isEnabled = false

    isDragEnabled = false
    isDoubleTapToZoomEnabled = false
    setTouchEnabled(true)
    setScaleEnabled(false)
    setPinchZoom(false)

    val cbm = ChartBubbleMarker(this.context, R.layout.layout_chart_bubble)
    cbm.chartView = this
    marker = cbm

    legend.apply {
        textColor = Color.Transparent.toArgb()
        formSize = 0f
        textSize = 0f
    }

    val tf = statTypeface(context)

    axisLeft.apply {
        tf?.let { typeface = tf }

        setLabelCount(6, true)
        textColor = AppColor.TextColorBlack.toArgb()
        setPosition(YAxis.YAxisLabelPosition.OUTSIDE_CHART)
        enableGridDashedLine(12f, 12f, 2f)
        gridColor = Color.LightGray.toArgb()
        axisLineColor = Color.Transparent.toArgb()

        valueFormatter = when (rsm) {
            ReportsStatisticsMode.Time -> {
                YAxisDurationValueFormatter
            }

            ReportsStatisticsMode.Steps -> {
                YAxisIntValueFormatter
            }

            else -> {
                YAxisFloatValueFormatter
            }
        }
        axisMinimum = 0f
        axisMaximum = yMax + yOffset
    }

    axisRight.isEnabled = false

    xAxis.apply {
        tf?.let { typeface = tf }

        position = XAxis.XAxisPosition.BOTTOM
        axisLineColor = Color.Transparent.toArgb()
        setDrawGridLines(false)
    }
}
