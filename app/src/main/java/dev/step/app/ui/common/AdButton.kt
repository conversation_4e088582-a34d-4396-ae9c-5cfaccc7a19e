package dev.step.app.ui.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.ui.theme.AppColor
import dev.step.app.R

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun RewardedAdButton(
    text: String,
    times: Int?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isShowRewardedAdVideoIcon: Boolean = true,
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        shape = CircleShape,
    ) {
        Box(
            modifier = Modifier
                .background(AppColor.FadedPrimaryBrushVertical)
                .padding(horizontal = 16.dp)
        ) {

            Row(
                Modifier.padding(start = 16.dp, end = 24.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (isShowRewardedAdVideoIcon) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_video),
                        contentDescription = null,
                        modifier = Modifier.size(22.dp)
                    )
                }

                Text(
                    text = text,
                    modifier = Modifier.padding(12.dp),
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
            }


            times?.let {
                Surface(
                    shape = CircleShape,
                    modifier = Modifier
                        .size(24.dp)
                        .align(Alignment.CenterEnd),
                ) {
                    Box(contentAlignment = Alignment.Center) {
                        Text(text = "X$times", color = AppColor.Primary, fontSize = 12.sp)
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun AdButtonPreview() {
    RewardedAdButton(
        text = "lalala lalalaalala ",
        times = 5,
        onClick = { /*TODO*/ },
        modifier = Modifier.padding(horizontal = 32.dp)
    )
}
