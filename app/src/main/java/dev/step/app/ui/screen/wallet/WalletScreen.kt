package dev.step.app.ui.screen.wallet

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.eventFlow
import dev.step.app.MainActivity
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAd
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.androidplatform.ext.scale
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.androidplatform.ext.time.displayName
import dev.step.app.data.pojo.remoteconfig.CoinsExchangeUnit
import dev.step.app.data.pojo.wallet.SignIn
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.appluck.AppLuckItem
import dev.step.app.ui.common.appluck.AppLuckPlaceholder
import dev.step.app.ui.common.game.GameBanner
import dev.step.app.ui.screen.permissionsmanager.IgnoreBatteryOptimizationDialog
import dev.step.app.ui.theme.AppColor
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.DayOfWeek
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.math.RoundingMode
import kotlin.math.absoluteValue

val walletScreenHasBeenInactiveFlow = MutableStateFlow<Boolean?>(true)

@Composable
fun WalletScreenInHome(
    openWithdraw: () -> Unit,
    openRedeemPicture: () -> Unit,
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    openRewardedDialog: (from: String, coins: Int, times: Int) -> Unit,
    walletViewModel: WalletViewModel,
) {
    WalletScreen(
        openWithdraw = {
            openWithdraw()
            logEventRecord("click_wallet_withdraw")
        },
        openGame1 = {
            openGame1()
            logEventRecord("click_wallet_game1")
        },
        openGame2 = {
            openGame2()
            logEventRecord("click_wallet_game3")
        },
        openGame3 = {
            openGame3()
            logEventRecord("click_wallet_game2")
        },
        openRewardedDialog = openRewardedDialog,
        openRedeemPicture = openRedeemPicture,
        viewModel = walletViewModel
    )
}

@Composable
private fun WalletScreen(
    openWithdraw: () -> Unit,
    openRedeemPicture: () -> Unit,
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    openRewardedDialog: (from: String, coins: Int, times: Int) -> Unit,
    viewModel: WalletViewModel,
) {
    val context = LocalContext.current
    var ignoringBatteryOptimizationDialogShowState: Boolean by remember { mutableStateOf(false) }

    val lifecycleOwner = LocalLifecycleOwner.current
    var lifecycleEvent by remember { mutableStateOf<Lifecycle.Event?>(null) }
    LaunchedEffect(Unit) {
        lifecycleOwner.lifecycle.eventFlow.onEach { event ->
            lifecycleEvent = event
        }.launchIn(this)
    }

    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()
    LaunchedEffect(lifecycleEvent) {
        delay(300)
        if (
            ignoringBatteryOptimizationRequester.canOpenBatteryOptimizationSettings(context.findActivity())
            && MainActivity.windowFocusChangedFlow.first() == true
        ) {
            ignoringBatteryOptimizationDialogShowState = true
        }
    }

    if (ignoringBatteryOptimizationDialogShowState) {
        IgnoreBatteryOptimizationDialog(
            onDismiss = { ignoringBatteryOptimizationDialogShowState = false },
            onRequest = {
                ignoringBatteryOptimizationRequester
                    .openSystemBatteryOptimizationSettings(context.findActivity())
                ignoringBatteryOptimizationDialogShowState = false
            },
            isForceOpen = false
        )
    }


    DisposableEffect(Unit) {
        onDispose {
            debugLog("WalletScreen interstitialAd walletScreenHasBeenInactiveFlow.update { true }")
            walletScreenHasBeenInactiveFlow.update { true }
        }
    }

//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    LaunchedEffect(Unit) {
        launch {
            delay(2000)
            debugLog("WalletScreen interstitialAd walletScreenHasBeenInactiveFlow.update { false }")
            walletScreenHasBeenInactiveFlow.update { false }
        }

//1        clickEarnCoinNotiEventFlow.onEach {
//            val walletScreenHasBeenInactive = walletScreenHasBeenInactiveFlow.first()
//            debugLog("WalletScreen interstitialAd clickEarnCoinNotiEventFlow.OnEach walletScreenHasBeenInactive: $walletScreenHasBeenInactive")
//
//            if (walletScreenHasBeenInactive == true) {
//                debugLog("WalletScreen interstitialAd interstitialAdHelper.tryToShowAd(\"click_earn_noti\")")
//                interstitialAdHelper.tryToShowAd("click_earn_noti")
//
//                walletScreenHasBeenInactiveFlow.emit(null)
//            }
//
//            clickEarnCoinNotiEventFlow.resetReplayCache()
//        }.launchIn(this)
    }

    OnLifecycleEvent { o, event ->
//        debugLog("WalletScreen interstitialAd o: ${o.hashCode()} event: $event")

        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            else -> {}
        }
    }

    val viewState by viewModel.collectAsState()

    viewModel.collectSideEffect {
        handleWalletSideEffect(
            it = it,
            openRewardedDialog = openRewardedDialog
        )
    }

    Scaffold(
        topBar = {
            WalletTopBar(
                coins = viewState.coins,
                coinsExchangeUnit = viewState.coinsExchangeUnit,
                useWithdraw = viewState.useWithdraw,
                openWithdraw = openWithdraw,
                openRedeemPicture = openRedeemPicture,
            )
        },
    ) {
        Box(modifier = Modifier.fillMaxSize().zIndex(1f)) {
            AppLuckItem(
                placeholder = AppLuckPlaceholder.NO_5,
                modifier = Modifier
                    .padding(end = 18.dp)
                    .align(Alignment.TopEnd)
            )
        }

        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxSize()
                .padding(top = it.calculateTopPadding()),
        ) {
            BlankSpacer(height = 16.dp)

            SignInContent(
                todayOfWeek = viewState.todayOfWeek,
                signIn = viewState.signIn,
                signInRewardCoins = viewState.signInRewardCoins,
            )

            BlankSpacer(height = 16.dp)

            DailyTaskContent(
                openGame1 = openGame1,
                openGame3 = openGame3,
                openGame2 = openGame2,
            )

            BlankSpacer(height = 16.dp)

//            MRecAd(
//                placeholder = MRecAdPlaceholder.HomeScreen,
//                placeName = "wallet",
//            )
            AdmobNativeAd(
                place = NativeAdPlace.Wallet,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun WalletTopBar(
    coins: Int,
    coinsExchangeUnit: CoinsExchangeUnit?,
    useWithdraw: Boolean,
    openWithdraw: () -> Unit,
    openRedeemPicture: () -> Unit,
) {
    val statusBarHeight = LocalContext.current.statusBarHeight

    Surface {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            BlankSpacer(height = statusBarHeight)

            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp, vertical = 18.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(R.string.title_my_coins),
                    fontSize = 20.sp,
                )
                BlankSpacer(width = 4.dp)

                if (useWithdraw) {
                    val currencyUnitSizeAndCurrencyText =
                        if (coinsExchangeUnit?.currency_text_placement_is_start == true) {
                            "${coinsExchangeUnit.currency_text}${coinsExchangeUnit.currency_unit_size}"
                        } else {
                            "${coinsExchangeUnit?.currency_unit_size} ${coinsExchangeUnit?.currency_text}"
                        }

                    Text(
                        text = "(${coinsExchangeUnit?.one_unit_exchange_coins} ${
                            stringResource(
                                id = R.string.text_coins
                            ).lowercase()
                        } = $currencyUnitSizeAndCurrencyText)",
                        fontSize = 12.sp,
                        modifier = Modifier.padding(top = 3.dp)
                    )
                }
            }

            Surface(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 4.dp),
                shape = RoundedCornerShape(10.dp),
                color = AppColor.PrimaryLightAlpha8
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_coin),
                        contentDescription = null,
                        modifier = Modifier.size(26.dp)
                    )

                    BlankSpacer(width = 2.dp)

                    val coinText = buildAnnotatedString {
                        withStyle(SpanStyle(color = AppColor.Primary, fontSize = 20.sp)) {
                            append(coins.toString())
                        }

                        if (useWithdraw) {
                            withStyle(SpanStyle(fontSize = 12.sp)) {
                                append(" ${stringResource(id = R.string.text_coins)} = ")
                            }

                            if (coinsExchangeUnit?.currency_text_placement_is_start == true) {
                                withStyle(SpanStyle(fontSize = 18.sp)) {
                                    append(coinsExchangeUnit.currency_text)
                                }
                                withStyle(SpanStyle(fontSize = 8.sp)) {
                                    append(" ")
                                }
                            }

                            withStyle(SpanStyle(color = AppColor.Primary, fontSize = 20.sp)) {
                                val coinsPerOneUnitF =
                                    coinsExchangeUnit?.one_unit_exchange_coins?.toFloat() ?: 0f
                                if (coinsPerOneUnitF == 0f) {
                                    append("")
                                } else {
                                    val unitSize = coinsExchangeUnit?.currency_unit_size ?: 1
                                    val scale = when (unitSize.toString().length) {
                                        1 -> 2
                                        2 -> 1
                                        else -> 0
                                    }

                                    append(
                                        (coins / coinsPerOneUnitF * (unitSize))
                                            .scale(scale, RoundingMode.DOWN)
                                            .let { f -> if (scale == 0) f.toInt() else f }
                                            .toString()
                                    )
                                }

                            }

                            if (coinsExchangeUnit?.currency_text_placement_is_start == false) {
                                withStyle(SpanStyle(fontSize = 12.sp)) {
                                    append(" ${coinsExchangeUnit.currency_text}")
                                }
                            }
                        } else {
                            withStyle(SpanStyle(fontSize = 16.sp)) {
                                append(" ${stringResource(id = R.string.text_coins)}")
                            }
                        }
                    }

                    Text(text = coinText, maxLines = 2)

                    Spacer(modifier = Modifier.weight(1f))

                    if (useWithdraw) {
                        Surface(
                            onClick = openWithdraw,
                            shape = CircleShape,
                            color = AppColor.Primary,
                        ) {
                            Row(
                                Modifier.padding(horizontal = 10.dp, vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.ic_wallet),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .height(22.dp),
                                    tint = Color.White
                                )

                                Text(
                                    text = stringResource(id = R.string.text_withdraw),
                                    color = Color.White,
                                    fontSize = 13.sp,
                                    maxLines = 1
                                )
                            }
                        }
                    } else {
                        Surface(
                            onClick = openRedeemPicture,
                            shape = CircleShape,
                            color = AppColor.Primary,
                        ) {
                            Row(
                                Modifier.padding(horizontal = 14.dp, vertical = 6.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(id = R.string.text_reward),
                                    color = Color.White,
                                    fontSize = 14.sp,
                                    maxLines = 1
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun SignInContent(
    todayOfWeek: DayOfWeek,
    signIn: SignIn,
    signInRewardCoins: Int?,
    modifier: Modifier = Modifier,
) {
    Column(modifier.fillMaxWidth()) {
        val horizontalPadding = 16.dp

        Text(
            text = stringResource(R.string.text_sign_in),
            modifier = Modifier.padding(horizontal = horizontalPadding)
        )

        BlankSpacer(height = 10.dp)

        Column(modifier = Modifier.padding(horizontal = horizontalPadding)) {
            val itemWidth =
                ((LocalConfiguration.current.screenWidthDp.absoluteValue - (horizontalPadding * 2).value - 4 * 6) / 7).dp

            Row(modifier = Modifier.fillMaxWidth()) {

                SignInFirstDayItem(
                    signInRewardCoins = signInRewardCoins,
                    isSign = signIn.sun,
                    todayOfWeek = todayOfWeek,
                    dayOfWeek = DayOfWeek.SUNDAY,
                    modifier = Modifier.width(itemWidth)
                )
                BlankSpacer(width = 4.dp)
                SignInItem(
                    signInRewardCoins = signInRewardCoins,
                    isSign = signIn.mon,
                    todayOfWeek = todayOfWeek,
                    dayOfWeek = DayOfWeek.MONDAY,
                    modifier = Modifier.width(itemWidth)
                )
                BlankSpacer(width = 4.dp)
                SignInItem(
                    signInRewardCoins = signInRewardCoins,
                    isSign = signIn.tue,
                    todayOfWeek = todayOfWeek,
                    dayOfWeek = DayOfWeek.TUESDAY,
                    modifier = Modifier.width(itemWidth)
                )
                BlankSpacer(width = 4.dp)
                SignInItem(
                    signInRewardCoins = signInRewardCoins,
                    isSign = signIn.wed,
                    todayOfWeek = todayOfWeek,
                    dayOfWeek = DayOfWeek.WEDNESDAY,
                    modifier = Modifier.width(itemWidth)
                )
                BlankSpacer(width = 4.dp)
                SignInItem(
                    signInRewardCoins = signInRewardCoins,
                    isSign = signIn.thu,
                    todayOfWeek = todayOfWeek,
                    dayOfWeek = DayOfWeek.THURSDAY,
                    modifier = Modifier.width(itemWidth)
                )
                BlankSpacer(width = 4.dp)
                SignInItem(
                    signInRewardCoins = signInRewardCoins,
                    isSign = signIn.fri,
                    todayOfWeek = todayOfWeek,
                    dayOfWeek = DayOfWeek.FRIDAY,
                    modifier = Modifier.width(itemWidth)
                )
                BlankSpacer(width = 4.dp)
                SignInItem(
                    signInRewardCoins = signInRewardCoins,
                    isSign = signIn.sat,
                    todayOfWeek = todayOfWeek,
                    dayOfWeek = DayOfWeek.SATURDAY,
                    modifier = Modifier.width(itemWidth)
                )
            }
        }
    }
}

data class SignInItemStyleData(
    val coinIcon: Painter,
    val signInStateIcon: Painter?,
    val signInStateText: String?,
    val alpha: Float,
    val dayOfWeekTextColor: Color,
    val awardTextColor: Color,
    val awardStateColor: Color,
    val awardStateBgColor: Color,
    val awardText: String,
)

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun SignInFirstDayItem(
    signInRewardCoins: Int?,
    isSign: Boolean,
    todayOfWeek: DayOfWeek,
    dayOfWeek: DayOfWeek,
    modifier: Modifier = Modifier,
) {
    val isToday = todayOfWeek.value == dayOfWeek.value

    val isTodayBefore =
        if (dayOfWeek != DayOfWeek.SUNDAY) dayOfWeek.value < todayOfWeek.value else true

    val styleData = when {
        isSign -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin),
                signInStateIcon = rememberVectorPainter(image = Icons.Rounded.CheckCircle),
                signInStateText = null,
                1f,
                AppColor.TextColorBlack,
                AppColor.Primary,
                AppColor.Primary,
                AppColor.Primary,
                "+$signInRewardCoins "
            )
        }

        isToday -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin),
                signInStateIcon = rememberVectorPainter(image = Icons.Rounded.Edit),
                signInStateText = null,
                1f,
                AppColor.TextColorBlack,
                AppColor.Primary,
                AppColor.Primary,
                AppColor.Primary,
                "+$signInRewardCoins "
            )
        }

        isTodayBefore -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin_gray),
                signInStateIcon = null,
                signInStateText = stringResource(R.string.text_missed),
                1f,
                AppColor.TextColorBlack,
                AppColor.TextColorGray,
                AppColor.TextColorDarkGray,
                AppColor.TextColorDarkGray.copy(.3f),
                "+0 "
            )
        }

        else -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin),
                signInStateIcon = null,
                signInStateText = stringResource(R.string.text_missed),
                .6f,
                AppColor.TextColorBlack,
                AppColor.Primary,
                AppColor.Primary,
                AppColor.Primary,
                "+$signInRewardCoins "
            )
        }
    }


    Surface(
        shape = RoundedCornerShape(10.dp),
        color = styleData.awardTextColor.copy(alpha = 0.12f)
    ) {
        Column(
            modifier = modifier.alpha(styleData.alpha),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            BlankSpacer(height = 8.dp)

            val dayOfWeekText = dayOfWeek.displayName()

            Text(
                text = dayOfWeekText,
                fontSize = 12.sp,
//                    color = if (todayOfWeek == DayOfWeek.SUNDAY && isToday) Color.White else dayOfWeekTextColor
                color = styleData.dayOfWeekTextColor
            )

            BlankSpacer(height = 5.dp)

            Image(
                painter = styleData.coinIcon,
                contentDescription = null,
                modifier = Modifier.size(26.dp)
            )

            BlankSpacer(height = 5.dp)

            Text(
                text = styleData.awardText,
                fontSize = 12.sp,
                color = styleData.awardTextColor
            )

            BlankSpacer(height = 5.dp)

            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(24.dp),
                color = styleData.awardTextColor.copy(alpha = 0.3f)
            ) {
                Box(contentAlignment = Alignment.Center) {
                    if (styleData.signInStateIcon != null) {
                        Icon(
                            painter = styleData.signInStateIcon,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(18.dp)
                        )
                    } else {
                        Text(
                            text = styleData.signInStateText ?: "",
                            fontSize = 11.sp,
                            color = styleData.awardStateColor,
                            maxLines = 1,
                            modifier = Modifier.basicMarquee()
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun SignInItem(
    signInRewardCoins: Int?,
    isSign: Boolean,
    todayOfWeek: DayOfWeek,
    dayOfWeek: DayOfWeek,
    modifier: Modifier = Modifier,
) {
    val isToday = todayOfWeek.value == dayOfWeek.value

    val isTodayBefore =
        if (todayOfWeek == DayOfWeek.SUNDAY) false else dayOfWeek.value < todayOfWeek.value

    val styleData = when {
        isSign -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin),
                signInStateIcon = rememberVectorPainter(image = Icons.Rounded.CheckCircle),
                signInStateText = null,
                1f,
                AppColor.TextColorBlack,
                AppColor.Primary,
                AppColor.Primary,
                AppColor.Primary,
                "+$signInRewardCoins "
            )
        }

        isToday -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin),
                signInStateIcon = rememberVectorPainter(image = Icons.Rounded.Edit),
                signInStateText = null,
                1f,
                AppColor.TextColorBlack,
                AppColor.Primary,
                AppColor.Primary,
                AppColor.Primary,
                "+$signInRewardCoins "
            )
        }

        isTodayBefore -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin_gray),
                signInStateIcon = null,
                signInStateText = stringResource(R.string.text_missed),
                1f,
                AppColor.TextColorBlack,
                AppColor.TextColorGray,
                AppColor.TextColorDarkGray,
                AppColor.TextColorDarkGray.copy(.3f),
                "+0 "
            )
        }

        else -> {
            SignInItemStyleData(
                painterResource(id = R.drawable.ic_coin),
                signInStateIcon = null,
                signInStateText = stringResource(R.string.text_missed),
                .6f,
                AppColor.TextColorBlack,
                AppColor.Primary,
                AppColor.Primary,
                AppColor.Primary,
                "+$signInRewardCoins "
            )
        }
    }

    Surface(
        shape = RoundedCornerShape(10.dp),
        color = styleData.awardTextColor.copy(alpha = 0.12f)
    ) {
        Column(
            modifier = modifier.alpha(styleData.alpha),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            BlankSpacer(height = 8.dp)

            val dayOfWeekText = dayOfWeek.displayName()

            Text(
                text = dayOfWeekText,
                fontSize = 12.sp,
//                    color = if (todayOfWeek == DayOfWeek.SUNDAY && isToday) Color.White else dayOfWeekTextColor
                color = styleData.dayOfWeekTextColor
            )

            BlankSpacer(height = 5.dp)

            Image(
                painter = styleData.coinIcon,
                contentDescription = null,
                modifier = Modifier.size(26.dp)
            )

            BlankSpacer(height = 5.dp)

            Text(
                text = styleData.awardText,
                fontSize = 12.sp,
                color = styleData.awardTextColor
            )

            BlankSpacer(height = 5.dp)

            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(24.dp),
                color = styleData.awardStateBgColor,
            ) {
                Box(contentAlignment = Alignment.Center) {
                    if (styleData.signInStateIcon != null) {
                        Icon(
                            painter = styleData.signInStateIcon,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(18.dp)
                        )
                    } else {
                        Text(
                            text = styleData.signInStateText ?: "",
                            fontSize = 11.sp,
                            color = styleData.awardStateColor,
                            maxLines = 1,
                            modifier = Modifier.basicMarquee()
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DailyTaskContent(
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier.fillMaxWidth()) {

        Text(
            text = stringResource(R.string.game_banner_tips),
            fontSize = 15.sp,
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        BlankSpacer(height = 10.dp)

        Column(modifier.padding(horizontal = 16.dp)) {
            GameBanner(
                openGame1 = openGame1,
                openGame2 = openGame2,
                openGame3 = openGame3,
            )
        }

    }
}

@Preview
@Composable
private fun ReportsTopTabPreview() {
    WalletTopBar(
        coins = 5,
        coinsExchangeUnit = CoinsExchangeUnit(100, 100, "BTC", false),
        useWithdraw = false,
        openWithdraw = {},
        openRedeemPicture = {},
    )
}

@Preview
@Composable
private fun SignInItemPreview() {
    SignInItem(
        signInRewardCoins = 3000,
        isSign = false,
        todayOfWeek = DayOfWeek.THURSDAY,
        dayOfWeek = DayOfWeek.SATURDAY,
    )
}

@Preview
@Composable
private fun SignInContentPreview() {
    SignInContent(
        todayOfWeek = DayOfWeek.FRIDAY,
        signIn = SignIn(mon = true, wed = true),
        signInRewardCoins = 3000,
    )
}

//@Preview
//@Composable
//private fun DailyTaskContentPreview() {
//    DailyTaskContent(
//        superWheelPeople = 911,
//        911,
//        911,
//        openGame1 = {},
//        {},
//        {}
//    )
//}