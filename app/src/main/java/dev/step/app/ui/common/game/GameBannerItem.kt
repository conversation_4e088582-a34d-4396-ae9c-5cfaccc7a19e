package dev.step.app.ui.common.game

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import dev.step.app.R
import dev.step.app.ui.theme.noRippleClickable

@Composable
fun Game1BannerItem(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.noRippleClickable(onClick)) {

        Image(
            painter = painterResource(id = R.drawable.img_banner_game_1),
            contentDescription = null,
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Composable
fun Game2BannerItem(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {

    Box(modifier = modifier.noRippleClickable(onClick)) {

        Image(
            painter = painterResource(id = R.drawable.img_banner_game_2),
            contentDescription = null,
            modifier = Modifier.fillMaxSize()
        )

    }

}

@Composable
fun Game3BannerItem(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.noRippleClickable(onClick)) {

        Image(
            painter = painterResource(id = R.drawable.img_banner_game_3),
            contentDescription = null,
            modifier = Modifier.fillMaxSize()
        )
    }

}

@Preview
@Composable
private fun SuperWheelBannerPreview() {
    Game1BannerItem(
        {}
    )
}