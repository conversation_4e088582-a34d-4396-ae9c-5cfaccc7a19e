package dev.step.app.ui.screen.redeempicture

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogFinishEventFlow
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.data.pojo.wallet.RedeemPictureData
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
class RedeemPictureViewModel(
    private val walletBizKv: WalletBizKv,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
//    private val rewardedAdHelper: MaxRewardedAdHelper,
    private val rewardedAdManager: RewardedAdManager,
) : ViewModel(), ContainerHost<RedeemPictureViewState, RedeemPictureSideEffect> {

    override val container: Container<RedeemPictureViewState, RedeemPictureSideEffect> =
        container(RedeemPictureViewState())

    fun onRefresh() = intent {

        reduce {
            state.copy(
                coinBalance = walletBizKv.getCoinBalance(),
                redeemPictures = walletBizKv.redeemPictures(),
                picturePriceByCoins = remoteConfigHelper.redeemPicturePriceByCoins().toInt()
            )
        }
    }

    private var redeemJob: Job? = null
    fun onRedeemed(redeemPictureData: RedeemPictureData) = intent {
        if (redeemPictureData.unlock) {
            postSideEffect(RedeemPictureSideEffect.NavToPreview(redeemPictureData.drawableRes))
        } else {
            if (walletBizKv.getCoinBalance() < state.picturePriceByCoins) {
                postSideEffect(RedeemPictureSideEffect.InsufficientInCoins)
                return@intent
            }

            redeemJob?.cancel()

            redeemJob = viewModelScope.launch {
                rewardedAdManager.tryToShowRewardedLoadingDialog("reward_picture")
                rewardedLoadingDialogFinishEventFlow.take(1).collectLatest {
                    postSideEffect(RedeemPictureSideEffect.RedeemSuccess(redeemPictureData.id))
                    redeemJob?.cancel()
                    redeemJob = null

                    walletBizKv.setCoinBalance(walletBizKv.getCoinBalance() - state.picturePriceByCoins)
                    walletBizKv.unlockRedeemPicture(redeemPictureData.id)
                    onRefresh()
                }
            }
        }
    }

}