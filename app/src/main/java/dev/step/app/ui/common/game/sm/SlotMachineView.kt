package dev.step.app.ui.common.game.sm

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.ViewGroup
import android.widget.LinearLayout
import kotlin.random.Random

class SlotMachineView : LinearLayout {
    private var mItem0: SlotListView? = null
    private var mItem1: SlotListView? = null
    private var mItem2: SlotListView? = null
    private var mItem3: SlotListView? = null
    private var mEndCount = 0
    private var mStarted = false

    private var mOnStopListener: OnStopListener? = null

    companion object {
        fun interface OnStopListener {
            fun onStop()
        }
    }

    constructor(context: Context?) : super(context) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init()
    }

    fun setOnStopListener(onStopListener: OnStopListener) {
        mOnStopListener = onStopListener
    }

    fun randomScrollSpeed() = Random.nextInt(60, 100)

    fun start() {
        if (mStarted) {
            return
        }
        mStarted = true
        mEndCount = 0
        mItem0?.start(randomScrollSpeed())
        mItem1?.start(randomScrollSpeed())
        mItem2?.start(randomScrollSpeed())
        mItem3?.start(randomScrollSpeed())
    }

    fun stop(result: String) {
        if (!mStarted) {
            return
        }
        val offset = 2

        val c0 = result.getOrNull(0) ?: '0'
        val c1 = result.getOrNull(1) ?: '0'
        val c2 = result.getOrNull(2) ?: '0'
        val c3 = result.getOrNull(3) ?: '0'
        mItem0?.stop(c0.code + offset)
        mItem1?.stop(c1.code + offset)
        mItem2?.stop(c2.code + offset)
        mItem3?.stop(c3.code + offset)
    }

    private fun init() {
        orientation = HORIZONTAL
        gravity = Gravity.CENTER_VERTICAL
//        val paddingLeft = dp2px(context, 60f)
//        val paddingRight = dp2px(context, 64f)
//        setPadding(paddingLeft, 0, paddingRight, 0)
//        setBackgroundResource(R.drawable.img_free_spins_slot_machine)

        mItem0 = buildItem()
        mItem1 = buildItem()
        mItem2 = buildItem()
        mItem3 = buildItem()
        addView(mItem0, buildLayoutParam(0))
        addView(mItem1, buildLayoutParam(1))
        addView(mItem2, buildLayoutParam(2))
        addView(mItem3, buildLayoutParam(3))
    }

    private fun buildLayoutParam(index: Int): LayoutParams {
        val params =
            LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT)

        val marginStartDp = when(index) {
            0 -> 1f
            1 -> 3.4f
            2 -> 4.6f
            3 -> 4f
            else -> 4f
        }

        return params.apply {
            marginStart = dp2px(context, marginStartDp)
        }
    }

    private fun buildItem(): SlotListView {
        val item = SlotListView(context)
        item.setOnTouchListener { _, _ ->
            true
        }
        item.setAutoScrollEndListener {
            mEndCount++
            if (mEndCount == childCount) {
                onEnd()
                mOnStopListener?.onStop()
//                debugLog("setAutoScrollEndListener ScrollEnd")
            }
        }
        return item
    }

    private fun onEnd() {
        mStarted = false
    }
}