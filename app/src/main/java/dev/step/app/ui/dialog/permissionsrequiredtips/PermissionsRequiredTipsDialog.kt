package dev.step.app.ui.dialog.permissionsrequiredtips

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.DirectionsRun
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import dev.step.app.R
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.RoundedCornerShape7Dp
import dev.step.app.ui.theme.bodyWidth

@Composable
fun PermissionsRequiredTipsDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {

    AppDefWithCloseDialog(
        onDismiss = onDismiss,
        onClose = onDismiss,
        onConfirm = onConfirm,
        confirmText = stringResource(R.string.text_confirm),
        topPainter = null,
        adPlace = null,
        adPlaceName = null,
        properties = DialogProperties(dismissOnBackPress = false, dismissOnClickOutside = false)
    ) {

        Box(modifier = Modifier.padding(horizontal = 16.dp)) {
            Column(
                modifier = Modifier.bodyWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(R.string.permissions_required_text_permission_required),
                    fontSize = 20.sp,
                    modifier = Modifier.bodyWidth(),
                )
                BlankSpacer(height = 8.dp)
                Text(
                    text = stringResource(R.string.permissions_required_tips),
                    textAlign = TextAlign.Center,
                    fontSize = 14.sp,
                )
                BlankSpacer(height = 8.dp)
                Surface(
                    shape = RoundedCornerShape7Dp,
                    color = AppColor.GrayBackground
                ) {
                    Row(
                        modifier = Modifier.padding(14.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.DirectionsRun,
                            contentDescription = null,
                            tint = AppColor.Primary,
                            modifier = Modifier.size(28.dp)
                        )

                        BlankSpacer(width = 8.dp)

                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(text = stringResource(R.string.permissions_required_text_physical_activity), fontSize = 15.sp)
                            BlankSpacer(height = 2.dp)
                            Text(text = stringResource(R.string.permissions_required_text_track_your_fitness_movements), fontSize = 12.sp)
                        }
                    }
                }

                BlankSpacer(height = 20.dp)
            }
        }


    }

}

@Preview
@Composable
private fun PermissionsRequiredTipsDialogPreview() {
    PermissionsRequiredTipsDialog(
        {}, {}
    )
}
