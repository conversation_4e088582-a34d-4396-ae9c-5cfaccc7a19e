package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeCap.Companion.Round
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcProfileGoal: ImageVector
    get() {
        if (_icProfileGoal != null) {
            return _icProfileGoal!!
        }
        _icProfileGoal = Builder(name = "IcProfileGoal", defaultWidth = 70.0.dp, defaultHeight =
                70.0.dp, viewportWidth = 70.0f, viewportHeight = 70.0f).apply {
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 1.0f)
                lineTo(35.0f, 1.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 69.0f, 35.0f)
                lineTo(69.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 69.0f)
                lineTo(35.0f, 69.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 1.0f, 35.0f)
                lineTo(1.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 1.0f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.5f, 17.45f)
                lineTo(35.5f, 25.55f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.5f, 44.45f)
                lineTo(35.5f, 52.55f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(52.5f, 35.5f)
                lineTo(45.0f, 35.5f)
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.5f, 35.5f)
                moveToRelative(-2.0f, 0.0f)
                arcToRelative(2.0f, 2.0f, 0.0f, true, true, 4.0f, 0.0f)
                arcToRelative(2.0f, 2.0f, 0.0f, true, true, -4.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(25.0f, 35.5f)
                lineTo(17.5f, 35.5f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 17.5f)
                curveTo(39.8325f, 17.5f, 44.2075f, 19.4588f, 47.3744f, 22.6256f)
                curveTo(50.5412f, 25.7925f, 52.5f, 30.1675f, 52.5f, 35.0f)
                curveTo(52.5f, 36.611f, 52.2826f, 38.1712f, 51.8751f, 39.6527f)
                curveTo(51.54f, 40.8711f, 51.0764f, 42.0362f, 50.4998f, 43.1327f)
                curveTo(49.0103f, 45.9652f, 46.7672f, 48.3395f, 44.0372f, 49.9892f)
                curveTo(41.3995f, 51.5833f, 38.3067f, 52.5f, 35.0f, 52.5f)
                curveTo(31.4244f, 52.5f, 28.0991f, 51.4281f, 25.3286f, 49.5872f)
                curveTo(22.4716f, 47.6889f, 20.2039f, 44.974f, 18.8594f, 41.7753f)
                curveTo(18.506f, 40.9345f, 18.2164f, 40.0603f, 17.9968f, 39.1586f)
                curveTo(17.672f, 37.8257f, 17.5f, 36.4329f, 17.5f, 35.0f)
                curveTo(17.5f, 30.1675f, 19.4588f, 25.7925f, 22.6256f, 22.6256f)
                curveTo(25.7925f, 19.4588f, 30.1675f, 17.5f, 35.0f, 17.5f)
                close()
            }
        }
        .build()
        return _icProfileGoal!!
    }

private var _icProfileGoal: ImageVector? = null
