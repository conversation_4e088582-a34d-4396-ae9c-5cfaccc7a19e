@file:Suppress("ObjectPropertyName")

package dev.step.app.ui.common

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.Column
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidViewBinding
import dev.step.app.R
import dev.step.app.androidplatform.kgToLb
import dev.step.app.databinding.LayoutWeightRulerBinding
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.bodyWidth

private val _maxKg by lazy { 270 }
private val _maxLb by lazy { kgToLb(_maxKg.toFloat()) }

@Composable
fun ProfileWeightSettingContentImperial(
    lb: Float,
    onLbChange: (Float) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier = modifier) {
        Text(
            text = stringResource(id = R.string.text_weight),
            modifier = Modifier.bodyWidth(),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )

        BlankSpacer(height = 12.dp)

        AndroidViewBinding(
            factory = { inflater: LayoutInflater, parent: ViewGroup, attachToParent: Boolean ->
                LayoutWeightRulerBinding.inflate(inflater, parent, attachToParent).apply {
                    rvWeight.setValue(0f, _maxLb, lb, .1f, 10)

                    rvWeight.setOnValueChangedListener(onLbChange)

                    tvLb.setTextColor(AppColor.Primary.toArgb())
                    tvLb.text = lb.toString()

                    tvUnit.text = "lbs"
                }
            },
            update = {
                tvLb.text = lb.toString()
            }
        )

    }


}

@Composable
fun ProfileWeightSettingContentMetric(
    kg: Float,
    onKgChange: (Float) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier = modifier) {
        Text(
            text = stringResource(id = R.string.text_weight),
            modifier = Modifier.bodyWidth(),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        )

        BlankSpacer(height = 12.dp)

        AndroidViewBinding(
            factory = { inflater: LayoutInflater, parent: ViewGroup, attachToParent: Boolean ->
                LayoutWeightRulerBinding.inflate(inflater, parent, attachToParent).apply {
                    rvWeight.setValue(0f, _maxKg.toFloat(), kg, .1f, 10)
                    rvWeight.setOnValueChangedListener(onKgChange)

                    tvLb.setTextColor(AppColor.Primary.toArgb())
                    tvLb.text = kg.toString()

                    tvUnit.text = "kg"
                }
            },
            update = {
                tvLb.text = kg.toString()
            }
        )

    }


}
