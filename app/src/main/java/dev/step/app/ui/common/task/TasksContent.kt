package dev.step.app.ui.common.task

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.ui.common.BlankSpacer


@Composable
fun TasksContent(
    tasks: List<TaskData>,
    modifier: Modifier = Modifier
) {
    Column(modifier) {
        Text(
            text = stringResource(id = R.string.daily_tasks_and_earn_title),
            fontSize = 15.sp,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )

        BlankSpacer(height = 10.dp)

        val itemWidth = (LocalConfiguration.current.screenWidthDp - 8 * 2 - 16 * 2) / 3

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.width(16.dp))

            tasks.forEachIndexed { index, taskData ->
                TaskItem(data = taskData, modifier = Modifier.width(itemWidth.dp))

                if (index == tasks.lastIndex) {
                    Spacer(modifier = Modifier.width(16.dp))
                } else {
                    Spacer(modifier = Modifier.width(8.dp))
                }

            }
        }
    }
}

@Preview
@Composable
private fun TasksContentPreview() {
    TasksContent(
        tasks = listOf(
            TaskData(1000, "lalalalalala", true, {}),
            TaskData(1000, "lalalalalala", false, {}),
            TaskData(1000, "lalalalalala", true, {}),
            TaskData(1000, "lalalalalala", true, {}),
        )
    )
}