package dev.step.app.ui.theme.appimg

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush.Companion.linearGradient
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcStepEdit: ImageVector
    get() {
        if (_icStepEdit != null) {
            return _icStepEdit!!
        }
        _icStepEdit = Builder(name = "IcStepEdit", defaultWidth = 60.0.dp, defaultHeight = 60.0.dp,
                viewportWidth = 60.0f, viewportHeight = 60.0f).apply {
            path(fill = linearGradient(0.0f to Color(0xFFFF844D), 1.0f to Color(0xFFF75927), start =
                    Offset(30.0f,2.0f), end = Offset(30.0f,56.565887f)), stroke =
                    SolidColor(Color(0x00000000)), strokeLineWidth = 1.0f, strokeLineCap = Butt,
                    strokeLineJoin = Miter, strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(30.0f, 30.0f)
                moveToRelative(-28.0f, 0.0f)
                arcToRelative(28.0f, 28.0f, 0.0f, true, true, 56.0f, 0.0f)
                arcToRelative(28.0f, 28.0f, 0.0f, true, true, -56.0f, 0.0f)
            }
            path(fill = SolidColor(Color(0xFFFFFFFF)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(38.2195f, 14.0f)
                curveTo(42.5226f, 14.0f, 45.9993f, 17.489f, 45.9993f, 21.804f)
                curveTo(46.0278f, 23.8547f, 45.2156f, 25.8484f, 43.7337f, 27.3009f)
                lineTo(34.9566f, 36.1018f)
                curveTo(30.8388f, 40.1889f, 25.7235f, 43.0798f, 20.1238f, 44.4896f)
                lineTo(18.4283f, 44.9169f)
                curveTo(16.3907f, 45.4153f, 14.5954f, 43.5925f, 15.0798f, 41.5845f)
                lineTo(15.5073f, 39.8899f)
                curveTo(16.9179f, 34.279f, 19.8104f, 29.1665f, 23.8855f, 25.0794f)
                lineTo(32.6911f, 16.2785f)
                curveTo(34.173f, 14.8117f, 36.1393f, 14.0f, 38.2195f, 14.0f)
                close()
                moveTo(30.91f, 23.7977f)
                lineTo(26.7494f, 27.9275f)
                lineTo(26.3702f, 28.3171f)
                curveTo(23.1172f, 31.7225f, 20.7466f, 35.8929f, 19.5254f, 40.4595f)
                curveTo(24.2844f, 39.2063f, 28.616f, 36.7141f, 32.0642f, 33.2394f)
                lineTo(32.0642f, 33.2394f)
                lineTo(36.2247f, 29.1095f)
                lineTo(35.8688f, 28.944f)
                curveTo(34.811f, 28.4253f, 33.8467f, 27.7139f, 33.0901f, 26.9449f)
                curveTo(32.2209f, 26.0762f, 31.4372f, 25.0082f, 30.91f, 23.7977f)
                lineTo(30.91f, 23.7977f)
                close()
                moveTo(38.2338f, 18.0302f)
                curveTo(37.2506f, 18.0302f, 36.2675f, 18.4289f, 35.5551f, 19.1267f)
                lineTo(35.5551f, 19.1267f)
                lineTo(34.2869f, 20.3941f)
                lineTo(34.2758f, 20.564f)
                curveTo(34.2434f, 21.5472f, 34.7421f, 22.8713f, 35.9398f, 24.0683f)
                curveTo(37.2079f, 25.3357f, 38.5758f, 25.8341f, 39.6017f, 25.706f)
                lineTo(39.6017f, 25.706f)
                lineTo(40.8698f, 24.4385f)
                lineTo(41.0255f, 24.2731f)
                curveTo(41.627f, 23.5937f, 41.9669f, 22.7099f, 41.9669f, 21.7897f)
                curveTo(41.9669f, 19.7106f, 40.3141f, 18.0302f, 38.2338f, 18.0302f)
                close()
            }
        }
        .build()
        return _icStepEdit!!
    }

private var _icStepEdit: ImageVector? = null
