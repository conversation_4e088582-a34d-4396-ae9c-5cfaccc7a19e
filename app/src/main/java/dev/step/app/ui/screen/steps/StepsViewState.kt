package dev.step.app.ui.screen.steps

import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.pojo.DayStepsData
import dev.step.app.data.pojo.remoteconfig.RewardedBubbles
import dev.step.app.data.pojo.remoteconfig.RewardBubblesShowState
import dev.step.app.ui.common.task.TaskData
//import dev.step.app.data.pojo.remoteconfig.RewardBubbles
//import dev.step.app.data.pojo.remoteconfig.RewardBubblesShowState
import kotlin.time.Duration

data class StepsViewState(
    val walletCoins: Int? = null,

    val mus: MeasurementUnit = MeasurementUnit.Imperial,
    val stepsGoal: Int = 6000,
    val distanceMi: Float = 0.0f,
    val distanceKm: Float = 0.0f,
    val kcal: Float = 0.0f,
    val duration: Duration = Duration.ZERO,
    val todayStepsData: DayStepsData = DayStepsData(nowInstant().todayStartInstant(), 0),
    val daysOfWeekStepDataList: List<DayStepsData> = emptyList(),

    val isNewUser: Boolean = false,
    val isNewUserDialogHasShow: Boolean = true,
    val isSignIn: Boolean = false,
    val isStageOneDone: Boolean = false,
    val isStageTwoDone: Boolean = false,

    val newUserRewardCoins: Int = 0,
    val signInRewardCoins: Int = 0,
    val stageOneRewardCoins: Int = 0,
    val stageTwoRewardCoins: Int = 0,

    val tasks: List<TaskData> = emptyList(),

    val rewardedBubbles: RewardedBubbles? = null,
    val rewardBubblesShowState: RewardBubblesShowState? = null,

    val withdrawEnable: Boolean = false,
) {
    companion object {
        val Empty = StepsViewState()
    }
}
