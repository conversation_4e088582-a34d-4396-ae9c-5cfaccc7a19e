package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeCap.Companion.Round
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcProfileStepLength: ImageVector
    get() {
        if (_icProfileStepLength != null) {
            return _icProfileStepLength!!
        }
        _icProfileStepLength = Builder(name = "IcProfileStepLength", defaultWidth = 70.0.dp,
                defaultHeight = 70.0.dp, viewportWidth = 70.0f, viewportHeight = 70.0f).apply {
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 1.0f)
                lineTo(35.0f, 1.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 69.0f, 35.0f)
                lineTo(69.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 69.0f)
                lineTo(35.0f, 69.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 1.0f, 35.0f)
                lineTo(1.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 1.0f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(46.6591f, 41.6314f)
                curveTo(47.9207f, 40.5357f, 53.3917f, 34.6775f, 49.5468f, 29.9754f)
                curveTo(44.936f, 24.3365f, 37.0856f, 31.6111f, 37.0856f, 31.6111f)
                curveTo(33.3003f, 34.888f, 29.741f, 39.4988f, 29.741f, 39.4988f)
                lineTo(34.0f, 46.552f)
                curveTo(37.0856f, 44.3625f, 43.5058f, 44.3625f, 46.6591f, 41.6314f)
                close()
                moveTo(22.0275f, 50.6871f)
                curveTo(24.8213f, 54.0554f, 30.3629f, 49.2888f, 30.3629f, 49.2888f)
                lineTo(26.3025f, 42.7492f)
                curveTo(26.3025f, 42.7492f, 18.1694f, 46.0303f, 22.0275f, 50.6871f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(15.8603f, 28.0312f)
                lineTo(21.1504f, 35.3123f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(33.026f, 15.9304f)
                lineTo(37.9634f, 22.7261f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(18.5053f, 31.6717f)
                lineTo(35.4947f, 19.3283f)
            }
        }
        .build()
        return _icProfileStepLength!!
    }

private var _icProfileStepLength: ImageVector? = null
