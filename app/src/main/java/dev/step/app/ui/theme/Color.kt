@file:Suppress("MemberVisibilityCanBePrivate")

package dev.step.app.ui.theme

import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

object AppColor {
    val OrangePrimary = Color(0xFFF75927)
    val OrangeLight = Color(0xFFFF844D)
    val Primary = OrangePrimary
    val PrimaryLight = OrangeLight
    val PrimaryLightAlpha8 = PrimaryLight.copy(alpha = .08f)

    val BackgroundDefault = Color.White

    val TextColorBlack = Color(0xFF2D3142)
    val TextColorWhite = Color.White

    val GrayTabTint = Color(0xFF9FA2A5)
    val GrayItemBackground = Color(0xFF9FA2A5)
    val GrayBackground = Color(0xFFF4F5F7)
    val TextColorGray = Color(0xFF71777E)
    val TextColorDarkGray = Color(0xFF7F7F7F)

    private val brushColors = listOf(PrimaryLight, Primary)

    val FadedPrimaryBrushDef = Brush.linearGradient(brushColors)

    val FadedPrimaryBrushVertical = Brush.verticalGradient(brushColors)

    val FadedPrimaryAlphaBrushVertical = Brush.verticalGradient(listOf(PrimaryLightAlpha8, PrimaryLightAlpha8))

    val FadedPrimaryBrushVerticalRevert = Brush.verticalGradient(brushColors.reversed())

    val Game1 = Color(0xFFFF4A3D)
    val Game2 = Color(0xFF4C8BEA)
    val Game3 = Color(0xFFFFC926)
}
