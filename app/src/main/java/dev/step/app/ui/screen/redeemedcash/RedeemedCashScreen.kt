package dev.step.app.ui.screen.redeemedcash

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.showToast
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAd
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.RewardedAdButton
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun RedeemedCashScreen(
    coinPrice: Int,
    amount: Int,
    navUp: () -> Unit,
    openSuccessfullyDialog: (cashText: String, accountAddress: String) -> Unit
) {
    val viewModel: RedeemedCashViewModel = koinViewModel()

    LaunchedEffect(Unit) {
        viewModel.configure(coinPrice, amount)
    }

    RedeemedGiftCardScreen(
        navUp = navUp,
        openSuccessfullyDialog = openSuccessfullyDialog,
        viewModel = viewModel,
    )
}

@Composable
private fun RedeemedGiftCardScreen(
    navUp: () -> Unit,
    openSuccessfullyDialog: (cashText: String, accountAddress: String) -> Unit,
    viewModel: RedeemedCashViewModel,
) {
    val viewState by viewModel.collectAsState()

    viewModel.collectSideEffect {
        handleRedeemedCashSideEffect(
            it = it,
            onRedeemed = openSuccessfullyDialog,
            onAccountAddressError = { showToast("invalid account") }
        )
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            RedeemedCashTopBar(navUp)
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(top = it.calculateTopPadding())
        ) {
            RedeemedCashMainContent(
                amount = viewState.amount,
                accountAddress = viewState.accountAddress,
                currencyText = viewState.currencyText,
                currencyTextPlacementIsStart = viewState.currencyTextPlacementIsStart,
                onAccountAddress = { textField ->
                    viewModel.onAccountAddressChange(textField)
                    if (!viewModel.isAccountAddressClicked) {
                        logEventRecord("click_withdraw_balance")
                        viewModel.isAccountAddressClicked = true
                    }
                },
                onRedeemNow = {
                    viewModel.onRedeemNow()
                    logEventRecord("click_withdraw_transfer")
                }
            )

            Spacer(modifier = Modifier.height(16.dp))


//            MRecAd(
//                placeholder = MRecAdPlaceholder.RedeemedScreen,
//                placeName = "withdraw_transfer"
//            )

            AdmobNativeAd(
                place = NativeAdPlace.RedeemCash,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
    }
}

@Composable
fun RedeemedCashMainContent(
    amount: Int,
    accountAddress: TextFieldValue,
    currencyText: String,
    currencyTextPlacementIsStart: Boolean,
    onAccountAddress: (TextFieldValue) -> Unit,
    onRedeemNow: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.redeemed_cash_text_please_enter_your_paypal_balance_details),
                fontSize = 15.sp,
            )

            BlankSpacer(height = 16.dp)

            Surface(
                shape = RoundedCornerShape(10.dp),
                color = AppColor.PrimaryLightAlpha8
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(20.dp)
                ) {
                    Image(
                        painter = paymentMethodImage(),
                        contentDescription = null,
                        modifier = Modifier.width(80.dp)
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    val cashText =
                        if (currencyTextPlacementIsStart) "$currencyText$amount" else "$amount $currencyText"
                    Text(text = cashText, fontSize = 18.sp, color = AppColor.Primary)
                }
            }

            BlankSpacer(height = 16.dp)

            Text(
                text = stringResource(R.string.redeemed_cash_text_transfer_money_to),
                fontSize = 15.sp,
            )

            BlankSpacer(height = 16.dp)

            val inputFocusRequester = remember { FocusRequester() }

            LaunchedEffect(Unit) {
                delay(200)
                inputFocusRequester.requestFocus()
            }

            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                TextField(
                    value = accountAddress,
                    onValueChange = onAccountAddress,
                    colors = TextFieldDefaults.textFieldColors(
                        textColor = AppColor.TextColorBlack,
                        disabledTextColor = Color.Transparent,
                        backgroundColor = AppColor.PrimaryLightAlpha8,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent,
                        disabledIndicatorColor = Color.Transparent
                    ),
                    textStyle = LocalTextStyle.current.copy(fontSize = 14.sp),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    shape = RoundedCornerShape(5.dp),
                    placeholder = {
                        Text(
                            text = stringResource(R.string.redeemed_cash_text_enter_your_paypal_balance_here),
                            color = AppColor.TextColorGray,
                            fontSize = 14.sp
                        )
                    },
                    maxLines = 1,
                    singleLine = true,
                    modifier = Modifier
                        .focusRequester(inputFocusRequester)
                        .fillMaxWidth()
                )

                BlankSpacer(height = 10.dp)

                Text(
                    text = stringResource(R.string.redeemed_cash_tips_please_check_info_),
                    color = AppColor.TextColorGray.copy(alpha = .7f),
                    modifier = Modifier.bodyWidth(),
                    fontSize = 12.sp
                )

                BlankSpacer(height = 36.dp)

                RewardedAdButton(
                    text = stringResource(R.string.redeemed_cash_title_transfer_money),
                    times = null,
                    onClick = onRedeemNow
                )

                BlankSpacer(height = 16.dp)
            }
        }
    }


}

@Composable
private fun paymentMethodImage(): Painter {
    val region = Locale.current.region.uppercase()

    return when (region) {
        "TR" -> painterResource(id = R.drawable.img_papara)
        "RU" -> painterResource(id = R.drawable.img_iomoney)
        else -> painterResource(id = R.drawable.img_paypal)
    }
}

@Composable
private fun RedeemedCashTopBar(
    navUp: () -> Unit,
) {
    val statusBarHeight = LocalContext.current.statusBarHeight


    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        BlankSpacer(height = statusBarHeight)

        Row(
            modifier = Modifier
                .padding(vertical = 8.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            IconButton(navUp) {
                Icon(
                    imageVector = Icons.Rounded.KeyboardArrowLeft,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = AppColor.TextColorBlack
                )
            }

            Text(
                text = stringResource(R.string.redeemed_cash_title_transfer_money_to_paypal),
                fontSize = 20.sp,
            )
        }
    }
}

@Preview
@Composable
private fun RedeemedCashScreenPreview() {
    AppTheme {
        RedeemedCashScreen(
            coinPrice = 1,
            amount = 1,
            navUp = { /*TODO*/ },
            openSuccessfullyDialog = { _, _ -> }
        )
    }
}
