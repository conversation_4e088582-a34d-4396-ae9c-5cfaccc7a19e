package dev.step.app.ui.common

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.sd.lib.compose.wheel_picker.FVerticalWheelPicker
import com.sd.lib.compose.wheel_picker.FWheelPickerFocusVertical
import com.sd.lib.compose.wheel_picker.rememberFWheelPickerState
import dev.step.app.ui.theme.AppColor

@Composable
fun NumberPicker(
    currentValue: Int,
    minValue: Int,
    maxValue: Int,
    onValueChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
    displayItemCount: Int = 3,
    selectedTextColor: Color = AppColor.Primary,
    unselectTextColor: Color = AppColor.TextColorGray,
) {


    val wheelPickerState = rememberFWheelPickerState()

    var updateCount by remember { mutableIntStateOf(0) }

    LaunchedEffect(currentValue) {
        wheelPickerState.scrollToIndex(currentValue - minValue)
    }

    LaunchedEffect(Unit) {
        snapshotFlow {
            wheelPickerState.currentIndex
        }.collect {
            ++updateCount

            // 1st update is collect -1 (default index)
            // 2nd update is collect ViewState.Empty value
            if (updateCount >= 3) {
                onValueChange(wheelPickerState.currentIndex + minValue)
            }
        }
    }

    FVerticalWheelPicker(
        count = maxValue - minValue + 1,
        state = wheelPickerState,
        modifier = Modifier.requiredWidthIn(max = 100.dp).padding(bottom = 3.dp),
        unfocusedCount = (displayItemCount - 1) / 2,
        focus = {
            FWheelPickerFocusVertical(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp),
                dividerSize = 2.dp,
                dividerColor = selectedTextColor
            )
        },
        itemHeight = 42.dp
    ) {
        val style =
            MaterialTheme.typography.h5.copy(color = if (it == wheelPickerState.currentIndex) selectedTextColor else unselectTextColor)
        Text(
            text = (it + minValue).toString(),
            style = style,
            textAlign = TextAlign.Center
        )
    }

}

@Preview
@Composable
private fun NumberPickerPreview() {
    NumberPicker(50, 1, 100, {})
}
