@file:OptIn(ExperimentalFoundationApi::class)

package dev.step.app.ui.common

import androidx.compose.animation.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.DoGlobalNavigate
import dev.step.app.WithdrawNode
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.sendGlobalNavigateEvent
import dev.step.app.ui.theme.AppColor
import kotlinx.coroutines.launch
import org.koin.compose.koinInject
import dev.step.app.R
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
import dev.step.app.ui.screen.redeempicture.RedeemPictureNode
import dev.step.app.ui.theme.noRippleClickable


@Suppress("AnimatedContentLabel")
@OptIn(ExperimentalMaterialApi::class)
@Composable
fun MyCoinsChip(
    from: String,
    coins: Int,
    onIncrease: () -> Unit,
    modifier: Modifier = Modifier,
    withdrawEnable: Boolean = false,
    color: Color = AppColor.PrimaryLightAlpha8
) {
    val scope = rememberCoroutineScope()
    val navigator = requireLocalNavigator()
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    val (OnNavTo, _) = interstitialAdRegister(navigator)

    Surface(
        modifier = modifier,
        onClick = {
            logEventRecord("click_${from}_my_coins")
            runCatching {
                if (withdrawEnable) {
                    OnNavTo {
                        push(WithdrawNode)
                    }
//                    interstitialAdHelper.tryToShowAd("click_${from}_coins", onAdHiddenOrSkip = {
//                        scope.launch {
//                            sendGlobalNavigateEvent(DoGlobalNavigate.NavNode(WithdrawNode))
//                        }
//                    })
                } else {
//                    interstitialAdHelper.tryToShowAd("click_${from}_coins", onAdHiddenOrSkip = {
//                        scope.launch {
//                            sendGlobalNavigateEvent(DoGlobalNavigate.NavNode(RedeemPictureNode))
//                        }
//                    })
                    OnNavTo {
                        push(RedeemPictureNode)
                    }
                }
            }
        },
        color = color,
        shape = RoundedCornerShape(percent = 50)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(start = 10.dp, end = 6.dp)
                .padding(vertical = 2.dp)
        ) {
            Text(text = stringResource(R.string.my_coins_chip_text_my_coins), fontSize = 14.sp)
            BlankSpacer(width = 2.dp)
            AnimatedContent(
                targetState = coins,
                transitionSpec = {
                    if (targetState > initialState) {
                        onIncrease()
                        (slideInVertically { height -> height } + fadeIn()).togetherWith(
                            slideOutVertically { height -> -height } + fadeOut())
                    } else {
                        (slideInVertically { height -> -height } + fadeIn()).togetherWith(
                            slideOutVertically { height -> height } + fadeOut())
                    }.using(
                        SizeTransform(clip = false)
                    )
                }
            ) { targetCount ->
                Text(
                    text = "$targetCount",
                    fontSize = 19.5.sp,
                    color = AppColor.Primary
                )
            }
            BlankSpacer(width = 3.dp)

            Image(
                painter = painterResource(id = R.drawable.ic_coin),
                contentDescription = null,
                modifier = Modifier
                    .size(23.dp)
                    .padding(bottom = 1.dp)
            )
        }
    }
}

@Suppress("AnimatedContentLabel")
@Composable
fun MyCoinsForGame3(
    from: String,
    coins: Int,
    onIncrease: () -> Unit,
    modifier: Modifier = Modifier,
    withdrawEnable: Boolean = false,
) {
    val scope = rememberCoroutineScope()
    val navigator = requireLocalNavigator()
    val (OnNavTo, _) = interstitialAdRegister(navigator)
//    val interstitialAdHelper: MaxInterstitialAdHelper = koinInject()

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = modifier
            .noRippleClickable {
                if (withdrawEnable) {
                    runCatching {
//                        interstitialAdHelper.tryToShowAd("click_${from}_coins", onAdHiddenOrSkip = {
//                            scope.launch {
//                                sendGlobalNavigateEvent(DoGlobalNavigate.NavNode(WithdrawNode))
//                            }
//                        })
                        OnNavTo { push(WithdrawNode) }

                        logEventRecord("click_${from}_my_coins")
                    }
                }
            }
    ) {
        BlankSpacer(width = 1.dp)
        Text(
            text = stringResource(R.string.my_coins_chip_text_my_coins),
            fontSize = if (Locale.current.language == "en") 18.sp else 13.sp,
            modifier = if (Locale.current.language != "en") Modifier.padding(bottom = 1.dp) else Modifier,
            color = Color(0xFF2C006F)
        )

        AnimatedContent(
            targetState = coins,
            transitionSpec = {
                if (targetState > initialState) {
                    onIncrease()
                    (slideInVertically { height -> height } + fadeIn()).togetherWith(
                        slideOutVertically { height -> -height } + fadeOut())
                } else {
                    (slideInVertically { height -> -height } + fadeIn()).togetherWith(
                        slideOutVertically { height -> height } + fadeOut())
                }.using(
                    SizeTransform(clip = false)
                )
            }
        ) { targetCount ->
            val numberSize = when {
                targetCount > 1000000 -> 16.sp
                targetCount > 100000 -> 18.sp
                targetCount > 10000 -> 20.sp
                else -> 21.sp
            }

            Text(
                text = "$targetCount",
                fontSize = numberSize,
                color = Color(0xFF7900FF),
                maxLines = 1,
                modifier = Modifier.basicMarquee()
            )
        }
    }
}

//@Composable
//fun CoinsAnimation(
//    modifier: Modifier = Modifier
//) {
//    val composition by rememberLottieComposition(
//        spec = LottieCompositionSpec.Asset("coin.zip"),
//    )
//
//    LottieAnimation(
//        composition = composition,
//        modifier = modifier
//    )
//
//}