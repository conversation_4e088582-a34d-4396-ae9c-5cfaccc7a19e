package dev.step.app.ui.common.game.sc

import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Card
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.ext.currentScreenRatio
import dev.step.app.ui.common.BlankSpacer

private val pattern_background_color = Color(0xFFE8ECF3)

/**
 *  Fixed height dp -> 160 * currentScreenRatio()
 */
@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun ScratchCard(
    scratchEnable: Boolean,
    painters: List<Painter>, // size = 6
    onLayout: (ScratchView) -> Unit,
    onTouch: () -> Unit,
    onScratchCompleted: () -> Unit,
    modifier: Modifier = Modifier,
) {

    val screenRatio = currentScreenRatio()

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height((230 * screenRatio).dp),
        contentAlignment = Alignment.Center
    ) {
        PatternGroup(
            painters = painters, modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 36.dp)
        )

        var erasurePercent by remember { mutableIntStateOf(0) }

        Box(
            modifier = Modifier
                .padding(vertical = (8 * screenRatio).dp, horizontal = (12 * screenRatio).dp)
                .pointerInteropFilter {
                    if (it.action == MotionEvent.ACTION_DOWN) {
                        onTouch()
                        return@pointerInteropFilter !scratchEnable
                    }
                    false
                }
        ) {
            AndroidView(
                factory = {
                    ScratchView(it).apply {
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        )

                        setMaskColor(Color(0xFFD4D6D8).toArgb())

                        setMaxPercent(57)
                        setEraserSize(81f)
                        setEraseStatusListener(object : ScratchView.EraseStatusListener {
                            override fun onProgress(percent: Int) {
                                erasurePercent = percent
                                debugLog("ScratchCard percent:$percent")

                            }

                            override fun onCompleted(view: View?) {
//                            onScratchCompleted(this@apply)
//                            debugLog("ScratchCard onCompleted")
//
//                            val sv = this@apply
//                            sv.clear()
                            }
                        })

                        setOnTouchListener { _, event ->
                            if (event.action == MotionEvent.ACTION_UP && erasurePercent >= 57) {
                                onScratchCompleted()
                                this.clear()
                                debugLog("ScratchCard onCompleted")
                            }
                            false
                        }

                    }.apply {
                        onLayout(this)
                    }
                }
            )
        }

    }
}

@Composable
private fun Pattern(
    painter: Painter, modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Image(
            painter = painterResource(id = R.drawable.img_sc_item_bg), contentDescription = null
        )
        Image(
            painter = painter,
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        )
    }
}

@Composable
private fun PatternGroup(
    painters: List<Painter>, // size = 6
    modifier: Modifier = Modifier
) {
    if (painters.isEmpty()) return

    Column(modifier = modifier) {

        val patternModifier = Modifier.size(80.dp)

        Row(Modifier.fillMaxWidth()) {
            Pattern(
                painters[0], modifier = patternModifier
            )
            Spacer(
                modifier = Modifier
                    .weight(1f)
                    .height(0.dp)
            )
            Pattern(
                painters[1], modifier = patternModifier
            )
            Spacer(
                modifier = Modifier
                    .weight(1f)
                    .height(0.dp)
            )
            Pattern(
                painters[2], modifier = patternModifier
            )
        }

        BlankSpacer(height = 16.dp)

        Row(Modifier.fillMaxWidth()) {
            Pattern(
                painters[3], modifier = patternModifier
            )
            Spacer(
                modifier = Modifier
                    .weight(1f)
                    .height(0.dp)
            )
            Pattern(
                painters[4], modifier = patternModifier
            )
            Spacer(
                modifier = Modifier
                    .weight(1f)
                    .height(0.dp)
            )
            Pattern(
                painters[5], modifier = patternModifier
            )
        }
    }
}

@Preview
@Composable
private fun ScratchCardPreview() {

    ScratchCard(
        true,
        listOf(
            painterResource(id = R.drawable.img_sc_bule),
            painterResource(id = R.drawable.img_sc_green),
            painterResource(id = R.drawable.img_sc_orange),
            painterResource(id = R.drawable.img_sc_red),
            painterResource(id = R.drawable.img_sc_purple),
            painterResource(id = R.drawable.img_sc_yellow),
        ), onLayout = {}, {}, {}, modifier = Modifier.padding(horizontal = 20.dp)
    )
}