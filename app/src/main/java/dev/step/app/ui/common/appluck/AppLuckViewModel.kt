package dev.step.app.ui.common.appluck

import androidx.lifecycle.ViewModel
import dev.step.app.androidplatform.androidcomponent.global.showToast
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.pojo.remoteconfig.AppLuckUrls
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@KoinViewModel
class AppLuckViewModel(
    private val placeholder: AppLuckPlaceholder,
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val userOperateDataKv: UserOperateDataKv,
) : ViewModel(), ContainerHost<AppLuckViewState, Unit> {

    override val container: Container<AppLuckViewState, Unit> = container(AppLuckViewState.Empty)

    fun onRefresh() = intent {
        val isOrganic = userOperateDataKv.tenjinAttr.isOrganic()
        val appLuckSwitch = remoteConfigHelper.appLuckSwitch(isOrganic)

        if (appLuckSwitch?.enabled != true) return@intent
        val appLuckUrls = remoteConfigHelper.appLuckUrls() ?: return@intent

        val firstTimeLaunchAppInstant = userOperateDataKv.firstTimeLaunchAppInstant ?: return@intent

        if (nowInstant() >= firstTimeLaunchAppInstant + appLuckSwitch.enable_after_of_minutes.toDuration(DurationUnit.MINUTES)) {
            val fullUrl = fullUrl(appLuckUrls)

            reduce {
                state.copy(fullUrl = fullUrl)
            }
        }

//        reduce {
//            state.copy(
//                fullUrl = fillUrlTemplate(
//                    "https://www.bing.com/search?q={gaid}",
//                    "gaid" to placeholder.id.toString()
//                )
//            )
//        }
    }

    private suspend fun fullUrl(
        appLuckUrls: AppLuckUrls
    ): String {
        val urlTemplate = when (placeholder) {
            AppLuckPlaceholder.NO_1 -> appLuckUrls.url1
            AppLuckPlaceholder.NO_2 -> appLuckUrls.url2
            AppLuckPlaceholder.NO_3 -> appLuckUrls.url3
            AppLuckPlaceholder.NO_4 -> appLuckUrls.url4
            AppLuckPlaceholder.NO_5 -> appLuckUrls.url5
        }

        val gaid = userOperateDataKv.gaid() ?: "null"

        return fillUrlTemplate(urlTemplate, "gaid" to gaid)
    }

    private fun fillUrlTemplate(urlTemplate: String, vararg params: Pair<String, String>): String {
        val regex = Regex("\\{\\w+\\}")
        return regex.replace(urlTemplate) { match ->
            val placeholder = match.value
            val key = placeholder.substring(1, placeholder.length - 1)
            params.find { it.first == key }?.second
                ?: throw IllegalArgumentException("Unknown placeholder: $placeholder")
        }
    }
}