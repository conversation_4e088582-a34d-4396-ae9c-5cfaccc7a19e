package dev.step.app.ui.common.appluck

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Lifecycle.Event.*
import coil.compose.rememberAsyncImagePainter
import com.sebaslogen.resaca.koin.koinViewModelScoped
import com.sebaslogen.resaca.viewModelScoped
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.global.openBrowser
import dev.step.app.androidplatform.androidcomponent.global.showToast
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.ui.theme.noRippleClickable
import org.koin.compose.getKoin
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState

enum class AppLuckPlaceholder(val id: Int, @DrawableRes val imgRes: Int) {
    NO_1(1, R.drawable.img_app_luck_item1),
    NO_2(2, R.drawable.img_app_luck_item5),
    NO_3(3, R.drawable.img_app_luck_item3),
    NO_4(4, R.drawable.img_app_luck_item4),
    NO_5(5, R.drawable.img_app_luck_item2)
}

@Composable
fun AppLuckItem(
    placeholder: AppLuckPlaceholder,
    modifier: Modifier = Modifier,
    size: Dp = 54.dp
) {
    val context = LocalContext.current
    val splashHelper: SplashHelper = koinInject()

    val viewModel: AppLuckViewModel =
        koinViewModelScoped(key = placeholder, parameters = { parametersOf(placeholder) })

    val viewState by viewModel.collectAsState()

    OnLifecycleEvent { _, event ->
        when (event) {
            ON_RESUME -> viewModel.onRefresh()
            else -> {}
        }
    }

    if (viewState.fullUrl.isNotEmpty()) {
        Box(modifier = modifier.noRippleClickable {
            splashHelper.doSkipSplash(true)
            context.openBrowser(viewState.fullUrl)

            logEventRecord("click_app_luck_item${placeholder.id}")
        }) {
            Image(
                painter = rememberAsyncImagePainter(
                    model = placeholder.imgRes,
                    imageLoader = koinInject(),
                ),
                contentDescription = null,
                modifier = Modifier.size(size)
            )
        }
    }
}

@Preview
@Composable
private fun AppLuckItemPreview() {
    AppLuckItem(placeholder = AppLuckPlaceholder.NO_1)
}