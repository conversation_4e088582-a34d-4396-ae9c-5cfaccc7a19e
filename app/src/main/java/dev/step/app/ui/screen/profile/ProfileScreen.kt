package dev.step.app.ui.screen.profile

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.eventFlow
import dev.step.app.BuildConfig
import dev.step.app.DoGlobalNavigate
import dev.step.app.MainActivity
import dev.step.app.R
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.androidcomponent.global.openBrowser
import dev.step.app.androidplatform.androidcomponent.global.openFeedbackMailto
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAd
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.data.adt.GenderSetting
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.adt.MotionSensorSensitivity
import dev.step.app.sendGlobalNavigateEvent
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.ProfileMainSettingItem
import dev.step.app.ui.common.ProfileOtherActionItem
import dev.step.app.ui.screen.permissionsmanager.IgnoreBatteryOptimizationDialog
import dev.step.app.ui.screen.privacypolicy.PrivacyPolicyScreenNode
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppColor.GrayBackground
import dev.step.app.ui.theme.AppImg
import dev.step.app.ui.theme.appimg.IcProfileGender
import dev.step.app.ui.theme.appimg.IcProfileGoal
import dev.step.app.ui.theme.appimg.IcProfileHeight
import dev.step.app.ui.theme.appimg.IcProfileMeasureUnit
import dev.step.app.ui.theme.appimg.IcProfileSens
import dev.step.app.ui.theme.appimg.IcProfileStepLength
import dev.step.app.ui.theme.appimg.IcProfileWeight
import dev.step.app.ui.theme.bodyWidth
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun ProfileScreenInHome(
    openStepGoalChangeDialog: () -> Unit,
    openMotionSensorSensitivityAdjustmentDialog: () -> Unit,
    openWeightDialog: () -> Unit,
    openHeightDialog: () -> Unit,
    openGenderDialog: () -> Unit,
    openStepLengthDialog: () -> Unit,
    openMeasureUnitDialog: () -> Unit,
    openPermissionsManager: () -> Unit,
    profileViewModel: ProfileViewModel,
) {
    val context = LocalContext.current
    var ignoringBatteryOptimizationDialogShowState: Boolean by remember { mutableStateOf(false) }

    val lifecycleOwner = LocalLifecycleOwner.current
    var lifecycleEvent by remember { mutableStateOf<Lifecycle.Event?>(null) }
    LaunchedEffect(Unit) {
        lifecycleOwner.lifecycle.eventFlow.onEach { event ->
            lifecycleEvent = event
        }.launchIn(this)
    }

    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()
    LaunchedEffect(lifecycleEvent) {
        delay(300)
        if (
            ignoringBatteryOptimizationRequester.canOpenBatteryOptimizationSettings(context.findActivity())
            && MainActivity.windowFocusChangedFlow.first() == true
        ) {
            ignoringBatteryOptimizationDialogShowState = true
        }
    }

    if (ignoringBatteryOptimizationDialogShowState) {
        IgnoreBatteryOptimizationDialog(
            onDismiss = { ignoringBatteryOptimizationDialogShowState = false },
            onRequest = {
                ignoringBatteryOptimizationRequester
                    .openSystemBatteryOptimizationSettings(context.findActivity())
                ignoringBatteryOptimizationDialogShowState = false
            },
            isForceOpen = false
        )
    }

    ProfileScreen(
        profileViewModel = profileViewModel,
        openStepGoalChangeDialog = {
            openStepGoalChangeDialog()
            logEventRecord("click_profile_goal")
        },
        openMotionSensorSensitivityAdjustmentDialog = {
            openMotionSensorSensitivityAdjustmentDialog()
            logEventRecord("click_profile_sensitivity")
        },
        openWeightDialog = {
            openWeightDialog()
            logEventRecord("click_profile_weight")
        },
        openHeightDialog = {
            openHeightDialog()
            logEventRecord("click_profile_height")
        },
        openGenderDialog = {
            openGenderDialog()
            logEventRecord("click_profile_gender")
        },
        openStepLengthDialog = openStepLengthDialog,
        openMeasureUnitDialog = {
            openMeasureUnitDialog()
            logEventRecord("click_profile_metric")
        },
        openPermissionsManager = openPermissionsManager,
    )
}

@Composable
private fun ProfileScreen(
    profileViewModel: ProfileViewModel,
    openStepGoalChangeDialog: () -> Unit,
    openMotionSensorSensitivityAdjustmentDialog: () -> Unit,
    openWeightDialog: () -> Unit,
    openHeightDialog: () -> Unit,
    openGenderDialog: () -> Unit,
    openStepLengthDialog: () -> Unit,
    openMeasureUnitDialog: () -> Unit,
    openPermissionsManager: () -> Unit,
) {
    val context = LocalContext.current
    val splashHelper: SplashHelper = koinInject()
//    val scope = rememberCoroutineScope()

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_RESUME -> profileViewModel.onRefreshUi()
            else -> {}
        }
    }

    val viewState by profileViewModel.collectAsState()

    Scaffold(
        topBar = {
            ProfileTopBar()
        },
        modifier = Modifier.fillMaxSize(), backgroundColor = AppColor.BackgroundDefault
    ) {
        ProfileContent(
            viewState = viewState,
            onStepGoalClick = openStepGoalChangeDialog,
            onSensitivityClick = openMotionSensorSensitivityAdjustmentDialog,
            onWeightClick = openWeightDialog,
            onHeightClick = openHeightDialog,
            onGenderClick = openGenderDialog,
            onStepLengthClick = openStepLengthDialog,
            onMeasureUnitClick = openMeasureUnitDialog,
            openFeedBackMailto = {
                logEventRecord("click_profile_feedback")

                profileViewModel.splashHelper.doSkipSplash(true)

                context.openFeedbackMailto(
                    email = "<EMAIL>",
                    subject = "Feedback & suggestion",
                )
            },
            openPrivacyPolicy = {
                logEventRecord("click_profile_privacy")

                splashHelper.doSkipSplash(true)
                context.openBrowser("https://sites.google.com/view/privacy-policy-for-e-pedometer/")
//                scope.launch {
//                    sendGlobalNavigateEvent(
//                        DoGlobalNavigate.NavNode(
//                            PrivacyPolicyScreenNode
//                        )
//                    )
//                }
            },
            openPermissionsManager = openPermissionsManager,
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
        )
    }
}

@Composable
private fun ProfileContent(
    viewState: ProfileViewState,
    onStepGoalClick: () -> Unit,
    onSensitivityClick: () -> Unit,
    onWeightClick: () -> Unit,
    onHeightClick: () -> Unit,
    onGenderClick: () -> Unit,
    onStepLengthClick: () -> Unit,
    onMeasureUnitClick: () -> Unit,
    openFeedBackMailto: () -> Unit,
    openPrivacyPolicy: () -> Unit,
    openPermissionsManager: () -> Unit,
    modifier: Modifier = Modifier
) {

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        ProfileUserSettingsContent(
            viewState = viewState,
            onStepGoalClick = onStepGoalClick,
            onSensitivityClick = onSensitivityClick,
            onWeightClick = onWeightClick,
            onHeightClick = onHeightClick,
            onGenderClick = onGenderClick,
            onStepLengthClick = onStepLengthClick,
            onMeasureUnitClick = onMeasureUnitClick,
            openPermissionsManager = openPermissionsManager
        )

        BlankSpacer(height = 10.dp)

//        MRecAd(
//            placeholder = MRecAdPlaceholder.HomeScreen,
//            placeName = "profile",
//        )
        AdmobNativeAd(
            place = NativeAdPlace.Profile,
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        Spacer(modifier = Modifier.height(10.dp))

        Surface(
            modifier.padding(horizontal = 16.dp),
            shape = RoundedCornerShape(10.dp),
            color = GrayBackground,
        ) {
            Column(modifier = Modifier.fillMaxWidth()) {
                ProfileOtherActionItem(
                    title = stringResource(id = R.string.text_feedback),
                    iconPainter = painterResource(id = R.drawable.ic_profile_setting_feedback),
                    onClick = openFeedBackMailto,
                )

                Divider(
                    modifier = Modifier.padding(horizontal = 14.dp),
                    color = AppColor.TextColorBlack.copy(alpha = 0.07f)
                )

                ProfileOtherActionItem(
                    title = stringResource(id = R.string.text_privacy_policy),
                    iconPainter = painterResource(id = R.drawable.ic_profile_setting_privacy),
                    onClick = openPrivacyPolicy,
                )


            }
        }

        BlankSpacer(height = 16.dp)

        Text(
            text = "${stringResource(id = R.string.text_version)}: ${BuildConfig.VERSION_NAME}",
            modifier = Modifier.bodyWidth(),
            color = AppColor.TextColorGray.copy(alpha = .5f),
            fontSize = 12.sp
        )

        BlankSpacer(height = 16.dp)

    }
}

@Composable
fun ProfileTopBar(modifier: Modifier = Modifier) {
    val statusBarHeight = LocalContext.current.statusBarHeight

    Column(modifier = modifier) {
        BlankSpacer(height = statusBarHeight)

        Text(
            text = stringResource(id = R.string.profile_title),
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            color = AppColor.TextColorBlack,
            fontSize = 22.sp,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun ProfileUserSettingsContent(
    viewState: ProfileViewState,
    onStepGoalClick: () -> Unit,
    onSensitivityClick: () -> Unit,
    onWeightClick: () -> Unit,
    onHeightClick: () -> Unit,
    onGenderClick: () -> Unit,
    onStepLengthClick: () -> Unit,
    onMeasureUnitClick: () -> Unit,
    openPermissionsManager: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_manage_permissions),
                iconPainter = painterResource(id = R.drawable.ic_profile_permissions_manager),
                value = "",
                onClick = openPermissionsManager,
                modifier = Modifier.padding(top = 4.dp)
            )

            BlankSpacer(height = 10.dp)

            val (weightValue, heightValue) = when (viewState.measureUnit) {
                null -> "" to ""
                MeasurementUnit.Metric -> "${viewState.bodyWeightKg} kg" to "${viewState.bodyHeightCm} cm"
                MeasurementUnit.Imperial -> "${viewState.bodyWeightLb} lbs" to "${viewState.bodyHeightFtIn?.first} ft ${viewState.bodyHeightFtIn?.second} in"
            }

            val (measureUnitValue, stepLengthValue) = when (viewState.measureUnit) {
                null -> "" to ""
                MeasurementUnit.Metric -> "kg / cm" to "${viewState.stepLengthCm} cm"
                MeasurementUnit.Imperial -> "lbs / ft" to "${viewState.stepLengthFtIn?.first} ft ${viewState.stepLengthFtIn?.second} in"
            }


            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_step_goal),
                iconPainter = rememberVectorPainter(AppImg.IcProfileGoal),
                value = viewState.stepGoal.toString(),
                onClick = onStepGoalClick,
            )

            BlankSpacer(height = 10.dp)

            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_sensitivity),
                iconPainter = rememberVectorPainter(AppImg.IcProfileSens),
                value = when (viewState.sensitivity) {
                    MotionSensorSensitivity.Lv0 -> stringResource(id = R.string.text_low)
                    MotionSensorSensitivity.Lv1 -> stringResource(id = R.string.text_medium)
                    MotionSensorSensitivity.Lv2 -> stringResource(id = R.string.text_medium)
                    MotionSensorSensitivity.Lv3 -> stringResource(id = R.string.text_high)
                    null -> ""
                },
                onClick = onSensitivityClick
            )

            BlankSpacer(height = 10.dp)

            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_weight),
                iconPainter = rememberVectorPainter(AppImg.IcProfileWeight),
                value = weightValue,
                onClick = onWeightClick
            )

            BlankSpacer(height = 10.dp)

            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_height),
                iconPainter = rememberVectorPainter(AppImg.IcProfileHeight),
                value = heightValue,
                onClick = onHeightClick
            )

            BlankSpacer(height = 10.dp)

            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_gender),
                iconPainter = rememberVectorPainter(AppImg.IcProfileGender),
                value = when (viewState.gender) {
                    null -> ""
                    GenderSetting.Male -> stringResource(id = R.string.text_male)
                    GenderSetting.Female -> stringResource(id = R.string.text_female)
                    GenderSetting.Others -> stringResource(id = R.string.text_others)
                },
                onClick = onGenderClick
            )

            BlankSpacer(height = 10.dp)

            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_step_length),
                iconPainter = rememberVectorPainter(AppImg.IcProfileStepLength),
                value = stepLengthValue,
                onClick = onStepLengthClick
            )

            BlankSpacer(height = 10.dp)

            ProfileMainSettingItem(
                title = stringResource(id = R.string.text_metric_and_imperial_unit),
                iconPainter = rememberVectorPainter(AppImg.IcProfileMeasureUnit),
                value = measureUnitValue,
                onClick = onMeasureUnitClick,
            )
        }
    }
}

@Preview
@Composable
fun ProfileContentPreview() {
    ProfileContent(
        viewState = ProfileViewState(
            stepGoal = 5000,
            measureUnit = MeasurementUnit.Imperial,
            bodyWeightLb = 123.4f,
            bodyHeightFtIn = 6 to 0,
            stepLengthFtIn = 1 to 3
        ),
        onStepGoalClick = { /*TODO*/ },
        onSensitivityClick = { /*TODO*/ },
        onWeightClick = { /*TODO*/ },
        onHeightClick = { /*TODO*/ },
        onGenderClick = { /*TODO*/ },
        onStepLengthClick = { /*TODO*/ },
        onMeasureUnitClick = { /*TODO*/ },
        {},
        {},
        {},
    )
}
