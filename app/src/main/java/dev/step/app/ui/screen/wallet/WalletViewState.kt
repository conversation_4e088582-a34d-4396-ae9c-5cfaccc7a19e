package dev.step.app.ui.screen.wallet

import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.pojo.remoteconfig.CoinsExchangeUnit
import dev.step.app.data.pojo.wallet.SignIn
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

data class WalletViewState(
    val coins: Int = 0,
    val signIn: SignIn = SignIn(),
    val todayOfWeek: DayOfWeek = nowInstant().toLocalDateTime(TimeZone.currentSystemDefault()).dayOfWeek,
    val signInRewardCoins: Int? = null,

    val coinsExchangeUnit: CoinsExchangeUnit? = null,

    val useWithdraw: Boolean = false,
) {
    companion object {
        val Empty = WalletViewState()
    }
}
