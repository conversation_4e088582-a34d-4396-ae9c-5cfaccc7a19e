package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcStatDuration: ImageVector
    get() {
        if (_icStatDuration != null) {
            return _icStatDuration!!
        }
        _icStatDuration = Builder(name = "IcStatDuration", defaultWidth = 30.0.dp, defaultHeight =
                30.0.dp, viewportWidth = 30.0f, viewportHeight = 30.0f).apply {
            path(fill = SolidColor(Color(0xFFFFFFFF)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(15.0f, 1.0f)
                curveTo(22.732f, 1.0f, 29.0f, 7.268f, 29.0f, 15.0f)
                curveTo(29.0f, 22.732f, 22.732f, 29.0f, 15.0f, 29.0f)
                curveTo(7.268f, 29.0f, 1.0f, 22.732f, 1.0f, 15.0f)
                curveTo(1.0f, 7.268f, 7.268f, 1.0f, 15.0f, 1.0f)
                close()
                moveTo(10.1419f, 8.1409f)
                curveTo(9.8751f, 7.9456f, 9.494f, 7.9542f, 9.238f, 8.1614f)
                curveTo(8.9821f, 8.3686f, 8.9252f, 8.7145f, 9.1031f, 8.9818f)
                lineTo(9.1031f, 8.9818f)
                lineTo(13.1929f, 15.1033f)
                curveTo(13.5109f, 15.5791f, 14.0429f, 15.9005f, 14.6449f, 15.9806f)
                curveTo(15.2468f, 16.0606f, 15.8564f, 15.891f, 16.3085f, 15.5177f)
                curveTo(16.7646f, 15.141f, 17.0171f, 14.5994f, 16.9991f, 14.0366f)
                curveTo(16.9811f, 13.4738f, 16.6943f, 12.9468f, 16.215f, 12.5953f)
                lineTo(16.215f, 12.5953f)
                close()
            }
        }
        .build()
        return _icStatDuration!!
    }

private var _icStatDuration: ImageVector? = null
