package dev.step.app.ui.screen.redeempicture

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.material.icons.rounded.Lock
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.rememberNavigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import dev.step.app.NoEnoughCoinsDialogNode
import dev.step.app.R
import dev.step.app.ScreenDestinationNode
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAd
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.ext.navigationBarHeight
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.data.pojo.wallet.RedeemPictureData
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.dialog.redeempicturesuccess.RedeemPictureSuccessDialogNode
import dev.step.app.ui.screen.redeempicturepreview.RedeemPicturePreviewNode
import dev.step.app.ui.screen.withdraw.RedeemedItem
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import dev.step.app.ui.theme.noRippleClickable
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Parcelize
object RedeemPictureNode : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        RedeemPicture(
            navigator = navigator
        )
    }
}

@Composable
fun RedeemPicture(
    navigator: Navigator,
) {
    val viewModel: RedeemPictureViewModel = koinViewModel()

    viewModel.collectSideEffect {
        when (it) {
            RedeemPictureSideEffect.InsufficientInCoins -> navigator.push(NoEnoughCoinsDialogNode)
            is RedeemPictureSideEffect.RedeemSuccess ->
                navigator.push(RedeemPictureSuccessDialogNode(it.id))

            is RedeemPictureSideEffect.NavToPreview -> navigator.push(RedeemPicturePreviewNode(it.drawableResId))
        }
    }

    val viewState by viewModel.collectAsState()

    RedeemPicture(
        navigator = navigator,
        viewState = viewState,
        viewModel = viewModel
    )
}

@Composable
private fun RedeemPicture(
    navigator: Navigator,
    viewState: RedeemPictureViewState,
    viewModel: RedeemPictureViewModel,
) {

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            else -> {}
        }
    }


    Scaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            RedeemPictureTopBar(
                coinBalance = viewState.coinBalance,
                navUp = navigator::pop
            )
        }
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {

//            MRecAd(
//                placeholder = MRecAdPlaceholder.WithdrawScreen,
//                placeName = "reward"
//            )
            AdmobNativeAd(
                place = NativeAdPlace.RedeemPicture,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            RedeemPictureContent(
                viewState = viewState,
                viewModel = viewModel
            )

            BlankSpacer(height = 16.dp)
        }
    }
}

@Composable
private fun RedeemPictureTopBar(
    coinBalance: Int?,
    navUp: () -> Unit,
) {
    val statusBarHeight = LocalContext.current.statusBarHeight

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        BlankSpacer(height = statusBarHeight)

        Row(
            modifier = Modifier
                .padding(vertical = 8.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            IconButton(navUp) {
                Icon(
                    imageVector = Icons.Rounded.KeyboardArrowLeft,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = AppColor.TextColorBlack
                )
            }

            Text(
                text = stringResource(R.string.text_reward),
                fontSize = 20.sp,
            )
        }

        Surface(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(bottom = 18.dp),
            shape = RoundedCornerShape(10.dp),
            color = AppColor.PrimaryLightAlpha8
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp, vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_coin),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )

                BlankSpacer(width = 4.dp)

                Text(text = coinBalance.toString(), color = AppColor.Primary, fontSize = 18.sp)

                BlankSpacer(width = 4.dp)

                Spacer(modifier = Modifier.weight(1f))
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun RedeemPictureContent(
    viewState: RedeemPictureViewState,
    viewModel: RedeemPictureViewModel,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = stringResource(R.string.text_unlock_atlas_with_coins),
            modifier = Modifier.padding(vertical = 8.dp, horizontal = 16.dp)
        )

        FlowRow(
            maxItemsInEachRow = 2,
            modifier = Modifier
                .bodyWidth()
                .padding(horizontal = 8.dp),
        ) {
            viewState.redeemPictures.list.forEach {
                RedeemPictureItem(
                    data = it,
                    coins = viewState.picturePriceByCoins,
                    onRedeemed = viewModel::onRedeemed,
                    modifier = Modifier
                        .weight(1f)
                        .padding(6.dp)
                )
            }
        }

        BlankSpacer(height = LocalContext.current.navigationBarHeight)
    }
}


@Composable
private fun RedeemPictureItem(
    data: RedeemPictureData,
    coins: Int,
    onRedeemed: (RedeemPictureData) -> Unit,
    modifier: Modifier = Modifier
) {
    RedeemedItemInternal(
        coins = coins,
        onRedeemed = { onRedeemed(data) },
        content = {
            Box(
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .padding(top = 20.dp, bottom = 34.dp)
            ) {
                Image(
                    painter = painterResource(id = data.drawableRes),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .width(128.dp)
                )
            }

        },
        modifier = modifier,
        unlock = data.unlock
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun RedeemedItemInternal(
    coins: Int,
    onRedeemed: () -> Unit,
    content: @Composable BoxScope.() -> Unit,
    modifier: Modifier = Modifier,
    unlock: Boolean = true,
) {
    Box(modifier.noRippleClickable(onRedeemed)) {
        Surface(
            color = AppColor.PrimaryLightAlpha8,
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(bottom = 22.dp)
        ) {
            Box(modifier = Modifier.fillMaxWidth()) {
                content(this)

                if (!unlock) {
                    Box(
                        modifier = Modifier
                            .matchParentSize()
                            .background(Color(0x992D3142))
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.Lock,
                            contentDescription = null,
                            modifier = Modifier
                                .align(Alignment.Center)
                                .size(38.dp),
                            tint = Color.White
                        )
                    }
                }
            }
        }

        if (!unlock) {
            Surface(
                onClick = onRedeemed,
                shape = CircleShape,
                modifier = Modifier.align(Alignment.BottomCenter)
            ) {
                Row(
                    modifier = Modifier.background(AppColor.FadedPrimaryBrushVertical),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    BlankSpacer(width = 10.dp)
                    Image(
                        painter = painterResource(id = R.drawable.ic_video),
                        contentDescription = null,
                        modifier = Modifier.size(17.dp)
                    )
                    BlankSpacer(width = 4.dp)
                    Image(
                        painter = painterResource(id = R.drawable.ic_coin),
                        contentDescription = null,
                        modifier = Modifier
                            .size(17.dp)
                            .padding(top = 2.dp)
                    )

                    BlankSpacer(width = 4.dp)

                    Text(
                        text = coins.toString(),
                        modifier = Modifier.padding(vertical = 5.dp),
                        color = Color.White,
                        fontSize = 13.sp
                    )

                    BlankSpacer(width = 12.dp)
                }
            }
        } else {
            Surface(
                onClick = onRedeemed,
                shape = CircleShape,
                modifier = Modifier.align(Alignment.BottomCenter),
                color = AppColor.TextColorBlack,
            ) {
                Row {
                    BlankSpacer(width = 20.dp)

                    Text(
                        text = stringResource(R.string.text_download),
                        modifier = Modifier.padding(vertical = 5.dp),
                        color = Color.White,
                        fontSize = 13.sp
                    )

                    BlankSpacer(width = 20.dp)
                }

            }
        }
    }
}

@Preview
@Composable
private fun RedeemPictureContentPreview() {
    AppTheme {
        RedeemPicture(navigator = rememberNavigator())
    }
}