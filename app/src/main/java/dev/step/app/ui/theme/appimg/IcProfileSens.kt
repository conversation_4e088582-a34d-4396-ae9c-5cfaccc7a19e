package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.EvenOdd
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeCap.Companion.Round
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcProfileSens: ImageVector
    get() {
        if (_icProfileSens != null) {
            return _icProfileSens!!
        }
        _icProfileSens = Builder(name = "IcProfileSens", defaultWidth = 70.0.dp, defaultHeight =
                70.0.dp, viewportWidth = 70.0f, viewportHeight = 70.0f).apply {
            path(fill = SolidColor(Color(0xFFFFDAC8)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(35.0f, 1.0f)
                lineTo(35.0f, 1.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 69.0f, 35.0f)
                lineTo(69.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 69.0f)
                lineTo(35.0f, 69.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 1.0f, 35.0f)
                lineTo(1.0f, 35.0f)
                arcTo(34.0f, 34.0f, 0.0f, false, true, 35.0f, 1.0f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFF2D3142)),
                    strokeLineWidth = 3.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(42.4731f, 19.2678f)
                lineTo(50.6716f, 27.4662f)
                curveTo(52.2337f, 29.0283f, 52.2337f, 31.561f, 50.6716f, 33.1231f)
                lineTo(33.1837f, 50.6109f)
                curveTo(31.6216f, 52.173f, 29.089f, 52.173f, 27.5269f, 50.6109f)
                lineTo(19.3284f, 42.4124f)
                curveTo(17.7663f, 40.8503f, 17.7663f, 38.3177f, 19.3284f, 36.7556f)
                lineTo(36.8163f, 19.2678f)
                curveTo(38.3783f, 17.7057f, 40.911f, 17.7057f, 42.4731f, 19.2678f)
                close()
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(49.5f, 44.4393f)
                lineTo(41.5f, 52.4393f)
            }
            path(fill = SolidColor(Color(0x00000000)), stroke = SolidColor(Color(0xFFF75927)),
                    strokeLineWidth = 3.0f, strokeLineCap = Round, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(28.5f, 17.4393f)
                lineTo(20.5f, 25.4393f)
            }
            path(fill = SolidColor(Color(0xFFF75927)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = EvenOdd) {
                moveTo(29.0f, 40.9393f)
                moveToRelative(-2.5f, 0.0f)
                arcToRelative(2.5f, 2.5f, 0.0f, true, true, 5.0f, 0.0f)
                arcToRelative(2.5f, 2.5f, 0.0f, true, true, -5.0f, 0.0f)
            }
        }
        .build()
        return _icProfileSens!!
    }

private var _icProfileSens: ImageVector? = null
