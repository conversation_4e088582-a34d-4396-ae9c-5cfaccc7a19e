package dev.step.app.ui.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.zIndex
import dev.step.app.R
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAd
import dev.step.app.androidplatform.biz.ad.admob.nat1ve.AdmobNativeAdInDialog
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.ui.theme.bodyWidth

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun BigBadgeDialog(
    onDismiss: () -> Unit,
    onClose: () -> Unit,
    onConfirm: () -> Unit,
    confirmText: String?,
    singleRewardTimes: Int?,
    bigBadgePainter: Painter?,
    bigBadgeTitle: String?,
    adPlace: NativeAdPlace?,
    adPlaceName: String?,
    isShowRewardedAdVideoIcon: Boolean = true,
    content: @Composable () -> Unit,
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnClickOutside = false
        )
    ) {
        Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
            Box {
                Surface(
                    onClick = onClose,
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = 8.dp),
                    shape = CircleShape,
                    color = Color.LightGray.copy(.8f)
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Close,
                        contentDescription = null,
                        modifier = Modifier.size(22.dp),
                        tint = Color.DarkGray.copy(.8f)
                    )
                }

                val bigBadgeSize = 202.dp

                bigBadgePainter?.let {
                    Image(
                        painter = painterResource(id = R.drawable.bg_badge_light),
                        contentDescription = null,
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .bodyWidth()
                            .padding(horizontal = 22.dp)
                    )
                }

                Box(modifier = Modifier.padding(top = 72.dp)) {


                    val dialogCardPaddingTop =
                        if (bigBadgePainter == null) 0.dp else bigBadgeSize / 2 + 16.dp

                    Surface(
                        shape = RoundedCornerShape(22.dp),
                        modifier = Modifier
                            .padding(top = dialogCardPaddingTop)
                            .padding(horizontal = 26.dp)
                            .zIndex(1f)
                    ) {
                        Column(
                            modifier = Modifier.bodyWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            BlankSpacer(height = 40.dp)

                            content()

                            confirmText?.let {
                                RewardedAdButton(
                                    text = confirmText,
                                    times = singleRewardTimes,
                                    onClick = onConfirm,
                                    modifier = Modifier.padding(horizontal = 16.dp),
                                    isShowRewardedAdVideoIcon = isShowRewardedAdVideoIcon
                                )
                            }

                            BlankSpacer(height = 26.dp)
                        }
                    }

                    bigBadgePainter?.let {
                        Box(
                            modifier = Modifier
                                .align(Alignment.TopCenter)
                                .zIndex(1f),
                        ) {
                            Image(
                                painter = bigBadgePainter,
                                contentDescription = null,
                                modifier = Modifier
                                    .width(bigBadgeSize)
                                    .align(Alignment.TopCenter)
                            )
                        }
                    }

                    bigBadgeTitle?.let {
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier
                                .align(Alignment.TopCenter)
                                .padding(top = 89.dp)
                                .zIndex(2f)
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.bg_badge_title),
                                contentDescription = null,
                                modifier = Modifier.width(600.dp)
                            )

                            Text(
                                text = it,
                                color = Color.White,
                                fontSize = 20.sp,
                                modifier = Modifier.padding(bottom = 5.dp)
                            )
                        }
                    }
                }
            }

            adPlace?.let {
                BlankSpacer(height = 12.dp)

                Surface(
                    shape = RoundedCornerShape(22.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 26.dp)
                ) {
//                    MRecAd(
//                        placeholder = adPlaceholder,
//                        placeName = adPlaceName!!,
//                        modifier = Modifier.padding(top = 14.dp)
//                    )

                    AdmobNativeAdInDialog(
                        place = adPlace,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun BigBadgeDialogPreview() {
    BigBadgeDialog(
        onDismiss = {},
        onClose = {},
        onConfirm = {},
        confirmText = "llala",
        singleRewardTimes = 4,
        bigBadgePainter = painterResource(id = R.drawable.img_badge_rewarded),
        bigBadgeTitle = "lalala",
        adPlace = null,
        adPlaceName = null
    ) {
        Column(
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            BlankSpacer(height = 12.dp)
            BlankSpacer(height = 4.dp)
            Text(text = "lalala")
            Text(text = "lalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalalala")
            BlankSpacer(height = 32.dp)
        }
    }
}
