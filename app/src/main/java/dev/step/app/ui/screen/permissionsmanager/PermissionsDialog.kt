package dev.step.app.ui.screen.permissionsmanager

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.intl.Locale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.DialogProperties
import com.roudikk.guia.core.toDialogProperties
import com.roudikk.guia.extensions.localDialog
import dev.step.app.R
import dev.step.app.ui.common.AppDefDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.bodyWidth

@Composable
fun PermissionsDialog(
    onDismiss: () -> Unit,
    onRequest: () -> Unit,
    mainPainter: Painter,
    title: String,
    content1: String,
    content2: String,
) {
    AppDefDialog(
        onDismiss = onDismiss,
        onCancel = onDismiss,
        onConfirm = onRequest,
        confirmText = stringResource(R.string.text_allow),
        properties = DialogProperties(dismissOnClickOutside = false)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .bodyWidth()
                .padding(horizontal = 12.dp)
        ) {
            Box(modifier = Modifier.padding(end = 10.dp)) {
                Image(
                    painter = mainPainter,
                    contentDescription = null,
                    modifier = Modifier.width(110.dp)
                )
            }

            BlankSpacer(height = 14.dp)

            Text(text = title, fontSize = 21.sp, textAlign = TextAlign.Center)

            BlankSpacer(height = 3.dp)

            Text(
                text = content1,
                fontSize = if (Locale.current.toLanguageTag() == "ru-RU") 13.5.sp else 15.sp,
                textAlign = TextAlign.Center
            )

            BlankSpacer(height = 12.dp)

            Text(
                text = content2,
                fontSize = 11.sp,
                fontWeight = FontWeight.Thin,
                textAlign = TextAlign.Center
            )

            BlankSpacer(height = 20.dp)
        }
    }
}
