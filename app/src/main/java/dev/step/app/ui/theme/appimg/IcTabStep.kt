package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcTabStep: ImageVector
    get() {
        if (_icTabStep != null) {
            return _icTabStep!!
        }
        _icTabStep = Builder(name = "IcTabStep", defaultWidth = 60.0.dp, defaultHeight = 60.0.dp,
                viewportWidth = 60.0f, viewportHeight = 60.0f).apply {
            path(fill = SolidColor(Color(0xFF9FA2A5)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(30.2433f, 15.7303f)
                curveTo(29.5919f, 12.7666f, 30.2629f, 9.6669f, 32.0826f, 7.2347f)
                curveTo(33.9023f, 4.8024f, 36.6915f, 3.277f, 39.7273f, 3.0539f)
                curveTo(49.5549f, 2.2441f, 52.961f, 10.7314f, 51.7736f, 17.1942f)
                lineTo(48.9768f, 34.8073f)
                curveTo(48.9768f, 34.8073f, 48.1019f, 42.8897f, 40.8991f, 42.9987f)
                curveTo(34.1494f, 43.0921f, 31.7589f, 38.1866f, 33.087f, 32.0041f)
                curveTo(35.4306f, 21.43f, 31.6652f, 20.8227f, 30.2433f, 15.7303f)
                lineTo(30.2433f, 15.7303f)
                close()
            }
            path(fill = SolidColor(Color(0xFF9FA2A5)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(28.8906f, 27.9321f)
                curveTo(28.997f, 24.881f, 27.7821f, 21.933f, 25.5592f, 19.8478f)
                curveTo(23.3363f, 17.7626f, 20.3227f, 16.7441f, 17.295f, 17.0549f)
                curveTo(7.4545f, 17.9823f, 5.5741f, 27.0204f, 7.8932f, 33.2292f)
                lineTo(13.7537f, 50.1738f)
                curveTo(13.7537f, 50.1738f, 16.0414f, 58.033f, 23.1555f, 56.8856f)
                curveTo(29.8464f, 55.801f, 31.335f, 50.4881f, 28.9062f, 44.5937f)
                curveTo(24.6911f, 34.5182f, 28.3421f, 33.2292f, 28.8906f, 27.9321f)
                close()
            }
        }
        .build()
        return _icTabStep!!
    }

private var _icTabStep: ImageVector? = null
