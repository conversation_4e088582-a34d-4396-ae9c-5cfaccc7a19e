package dev.step.app.ui.common

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp

@Composable
fun RowScope.BlankSpacer(width: Dp) {
    Spacer(modifier = Modifier.width(width))
}

@Composable
fun ColumnScope.BlankSpacer(height: Dp) {
    Spacer(modifier = Modifier.height(height))
}

@Composable
fun ColumnScope.BlankHeightIn(min: Dp, max: Dp) {
    Spacer(modifier = Modifier.heightIn(min, max))
}

@Composable
fun ColumnScope.BlankWidthIn(min: Dp, max: Dp) {
    Spacer(modifier = Modifier.widthIn(min, max))
}
