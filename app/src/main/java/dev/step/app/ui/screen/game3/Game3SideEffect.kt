package dev.step.app.ui.screen.game3

import dev.step.app.androidplatform.biz.game.GameADT


sealed interface Game3SideEffect {
    data object ResetScratchCard : Game3SideEffect

    data object DisableScratchCard : Game3SideEffect

    data class ToRewardedDialog(
        val gameADT: GameADT, val coins: Int, val times: Int
    ) : Game3SideEffect

    data class ToNoRemainingTimeDialog(val gameADT: GameADT) : Game3SideEffect
}
