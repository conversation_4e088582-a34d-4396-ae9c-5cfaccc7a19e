package dev.step.app.ui.screen.home

import androidx.lifecycle.ViewModel
import dev.step.app.HomeNode
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

@KoinViewModel
internal class HomeViewModel(
    @InjectedParam private val args: HomeNode.HomeArgs,
) : ViewModel(), ContainerHost<HomeViewState, Unit> {

    override val container: Container<HomeViewState, Unit> = container(HomeViewState.Empty)

    init {
        if (args.tabIndex > -1) {
            onNavTabChange(args.tabIndex)
        }
    }

    fun onNavTabChange(index: Int) = intent {
        if (index in HomeNavigationItems.indices) {
            reduce { state.copy(selectedNavigationItemIndex = index) }
        } else {
            reduce { state.copy(selectedNavigationItemIndex = 1) }
        }
    }
}
