package dev.step.app.ui.screen.home

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AccountBalanceWallet
import androidx.compose.material.icons.rounded.Wallet
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.ext.navigationBarHeight
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppImg
import dev.step.app.ui.theme.appimg.IcTabProfile
import dev.step.app.ui.theme.appimg.IcTabStat
import dev.step.app.ui.theme.appimg.IcTabStep
import dev.step.app.ui.theme.noRippleClickable

internal sealed interface HomeNavigationInsideScreen {
    data object Profile : HomeNavigationInsideScreen
    data object Steps : HomeNavigationInsideScreen
    data object Reports : HomeNavigationInsideScreen
    data object Wallet : HomeNavigationInsideScreen
}

internal sealed class HomeNavigationItem(
    val insideScreen: HomeNavigationInsideScreen,
    @StringRes val labelResId: Int,
    @StringRes val contentDescriptionResId: Int,
) {
    class ResourceIcon(
        insideScreen: HomeNavigationInsideScreen,
        @StringRes labelResId: Int,
        @StringRes contentDescriptionResId: Int,
        @DrawableRes val iconResId: Int,
        @DrawableRes val selectedIconResId: Int,
    ) : HomeNavigationItem(insideScreen, labelResId, contentDescriptionResId)

    class ImageVectorIcon(
        insideScreen: HomeNavigationInsideScreen,
        @StringRes labelResId: Int,
        @StringRes contentDescriptionResId: Int,
        val iconImageVector: ImageVector,
        val selectedImageVector: ImageVector,
    ) : HomeNavigationItem(insideScreen, labelResId, contentDescriptionResId)
}

internal val HomeNavigationItems = listOf(
    HomeNavigationItem.ImageVectorIcon(
        HomeNavigationInsideScreen.Profile,
        labelResId = R.string.profile_title,
        contentDescriptionResId = R.string.cd_profile_title,
        iconImageVector = AppImg.IcTabProfile,
        selectedImageVector = AppImg.IcTabProfile
    ),
    HomeNavigationItem.ImageVectorIcon(
        HomeNavigationInsideScreen.Steps,
        labelResId = R.string.steps_title,
        contentDescriptionResId = R.string.cd_steps_title,
        iconImageVector = AppImg.IcTabStep,
        selectedImageVector = AppImg.IcTabStep
    ),
    HomeNavigationItem.ImageVectorIcon(
        HomeNavigationInsideScreen.Wallet,
        labelResId = R.string.wallet_title,
        contentDescriptionResId = R.string.cd_wallet_title,
        iconImageVector = Icons.Rounded.AccountBalanceWallet,
        selectedImageVector = Icons.Rounded.AccountBalanceWallet
    ),
    HomeNavigationItem.ImageVectorIcon(
        HomeNavigationInsideScreen.Reports,
        labelResId = R.string.reports_title,
        contentDescriptionResId = R.string.cd_reports_title,
        iconImageVector = AppImg.IcTabStat,
        selectedImageVector = AppImg.IcTabStat
    ),
)

@Composable
internal fun HomeBottomNavigation(
    selectedNavigationItem: HomeNavigationItem,
    onNavigationSelected: HomeNavigationItem.(Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(modifier, elevation = 8.dp) {
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 20.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                HomeNavigationItems.forEachIndexed { index, item ->
                    val selected = selectedNavigationItem == item

                    NavigationItem(
                        onClick = { clickItemIndex ->
                            onNavigationSelected(this, clickItemIndex)
                        },
                        item = item,
                        itemIndex = index,
                        selected = selected,
                        modifier = Modifier.weight(if (selected) 1.4f else 1f)
                    )
                }
            }

            val navigationBarHeight = LocalContext.current.navigationBarHeight

            BlankSpacer(height = navigationBarHeight)
        }
    }
}

@Composable
internal fun NavigationItem(
    onClick: HomeNavigationItem.(Int) -> Unit,
    item: HomeNavigationItem,
    itemIndex: Int,
    selected: Boolean,
    modifier: Modifier = Modifier
) {
    val painter = when (item) {
        is HomeNavigationItem.ResourceIcon -> painterResource(item.iconResId)
        is HomeNavigationItem.ImageVectorIcon -> rememberVectorPainter(item.iconImageVector)
    }
    val selectedPainter = when (item) {
        is HomeNavigationItem.ResourceIcon -> painterResource(item.selectedIconResId)
        is HomeNavigationItem.ImageVectorIcon -> rememberVectorPainter(
            item.selectedImageVector
        )
    }

    val showPainter = if (selected) selectedPainter else painter

    Box(
        modifier = modifier.noRippleClickable { onClick(item, itemIndex) },
        contentAlignment = Alignment.Center
    ) {
        if (selected) {
            Surface(shape = CircleShape, modifier = Modifier.padding(vertical = 8.dp)) {
                Row(
                    modifier = Modifier
                        .background(AppColor.FadedPrimaryBrushVertical)
                        .padding(vertical = 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    BlankSpacer(width = 14.dp)

                    Icon(
                        painter = showPainter,
                        contentDescription = null,
                        modifier = Modifier.size(28.dp),
                        tint = Color.White
                    )

                    BlankSpacer(width = 6.dp)
                    Text(
                        text = stringResource(item.labelResId),
                        color = Color.White,
                        fontSize = 14.sp
                    )

                    BlankSpacer(width = 18.dp)
                }
            }


        } else {
            Icon(
                painter = showPainter,
                contentDescription = null,
                modifier = Modifier.size(28.dp),
                tint = AppColor.GrayTabTint
            )
        }
    }
}


@Preview
@Composable
private fun HomeBottomNavigationPreview() {
    HomeBottomNavigation(
        selectedNavigationItem = HomeNavigationItems[0],
        onNavigationSelected = {

        }
    )
}
