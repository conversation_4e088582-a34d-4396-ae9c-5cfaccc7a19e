@file:Suppress("CrossfadeLabel")

package dev.step.app.ui.screen.home

import android.annotation.SuppressLint
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.roudikk.guia.containers.NavContainer
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.push
import dev.step.app.HandleGlobalNavigate
import dev.step.app.HomeNode
import dev.step.app.RewardedLoadingDialogNode
import dev.step.app.SplashNode
import dev.step.app.androidplatform.androidcomponent.global.debugLog
//import dev.step.app.androidplatform.biz.LoadingDialogEvent
//import dev.step.app.androidplatform.biz.MaxRewardedAdHelper
import dev.step.app.androidplatform.biz.NotificationPermissionRequester
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedLoadingDialogEvent
import dev.step.app.androidplatform.biz.ad.rewarded.rewardedLoadingDialogEventFlow
//import dev.step.app.androidplatform.biz.RewardedAdWithTipsDialogEvent
//import dev.step.app.androidplatform.biz.rewardedAdWithTipsDialogEventFlow
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.appNavigation
import dev.step.app.data.kvstore.WalletBizKv
//import dev.step.app.destinations.AdLoadingDialogDestination
//import dev.step.app.destinations.RewardAdLoadingTipsDialogDestination
import dev.step.app.globalNavigator
import dev.step.app.ui.screen.permissionsmanager.PermissionsManagerNode
import dev.step.app.ui.screen.profile.ProfileScreenInHome
import dev.step.app.ui.screen.profile.ProfileViewModel
import dev.step.app.ui.screen.redeempicture.RedeemPictureNode
import dev.step.app.ui.screen.reports.ReportsScreenInHome
import dev.step.app.ui.screen.reports.ReportsViewModel
import dev.step.app.ui.screen.steps.StepsScreenInHome
import dev.step.app.ui.screen.steps.StepsViewModel
import dev.step.app.ui.screen.steps.backToStepsScreenDoNotShowInterAd
import dev.step.app.ui.screen.wallet.WalletScreenInHome
import dev.step.app.ui.screen.wallet.WalletViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
//import dev.step.app.ui.screen.wallet.WalletViewModel
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState

object SystemBarsColor {
    const val systemUseDarkIcons = true

    val setSystemBarsColorDarkIconsStateFlow = MutableStateFlow(systemUseDarkIcons)
    private var previousSystemBarsColorUseDarkIcons = systemUseDarkIcons

    fun setSystemBarsColorDarkIcons(useDarkIcons: Boolean) {
        previousSystemBarsColorUseDarkIcons = setSystemBarsColorDarkIconsStateFlow.value
        setSystemBarsColorDarkIconsStateFlow.update { useDarkIcons }
    }

    fun resetSystemBarsColorDarkIcons() {
        setSystemBarsColorDarkIconsStateFlow.update { previousSystemBarsColorUseDarkIcons }
    }

    @Composable
    fun DisposableEffectSystemBarsColorUseDarkIcons(useDarkIcons: Boolean) {
        DisposableEffect(Unit) {
            setSystemBarsColorDarkIcons(useDarkIcons)
            onDispose { resetSystemBarsColorDarkIcons() }
        }
    }
}

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun Home() {
    val context = LocalContext.current
    val systemUiController = rememberSystemUiController()

    LaunchedEffect(Unit) {
        systemUiController.setSystemBarsColor(
            color = Color.Transparent,
            darkIcons = SystemBarsColor.systemUseDarkIcons
        )

        SystemBarsColor.setSystemBarsColorDarkIconsStateFlow.onEach { darkIcons ->
            systemUiController.setSystemBarsColor(
                color = Color.Transparent,
                darkIcons = darkIcons
            )
        }.launchIn(this)
    }

    val navigator = appNavigation()
    globalNavigator = navigator

    navigator.NavContainer(modifier = Modifier.fillMaxSize())

    HandleGlobalNavigate(navigator = navigator)

    // register about rewarded ad dialog screen
    LaunchedEffect(Unit) {
        rewardedLoadingDialogEventFlow.onEach {
            if (it is RewardedLoadingDialogEvent.StartShow) {
                debugLog("rewardedAdWithTipsDialogEventFlow navigate to tips dialog")

                navigator.push(RewardedLoadingDialogNode(instantlyLoad = it.instantlyLoad))
            }
        }.launchIn(this)
    }

    // try to show notification permission requester
    val notificationPermissionRequester: NotificationPermissionRequester = koinInject()
//    val walletBizKv: WalletBizKv = koinInject()
    LaunchedEffect(navigator.currentKey) {
//        if (navigator.currentKey is HomeNode && walletBizKv.isNewUserDialogHasShow()) {

        delay(50)
        val currentKey = navigator.currentKey

        val currentInDialog =
            navigator.currentKey?.tag()?.contains("dialog", ignoreCase = true) == true

        if (
            currentKey !is SplashNode
            && !currentInDialog
        ) {
            notificationPermissionRequester.requestIfNeeded(
                activity = context.findActivity(),
                navigator = navigator,
            )
        }
    }
}

@Composable
fun HomeScreen(
    args: HomeNode.HomeArgs,
    navigator: Navigator,
    openStepGoalChangeDialog: () -> Unit,
    openMotionSensorSensitivityAdjustmentDialog: () -> Unit,
    openWeightDialog: () -> Unit,
    openHeightDialog: () -> Unit,
    openGenderDialog: () -> Unit,
    openStepLengthDialog: () -> Unit,
    openMeasureUnitDialog: () -> Unit,
    openWithdraw: () -> Unit,
    openGame1: () -> Unit,
    openGame2: () -> Unit,
    openGame3: () -> Unit,
    openSignInRewardedDialog: () -> Unit,
    openExchangeCoinsRewardedDialog: () -> Unit,
    openNewUserRewardedDialog: (adBizEnable: Boolean) -> Unit,
    openNoEnoughStepsDialog: () -> Unit,
    openStageOneRewardedDialog: () -> Unit,
    openStageTwoRewardedDialog: () -> Unit,
    openRewardedDialog: (from: String, coins: Int, times: Int) -> Unit,
) {
    val context = LocalContext.current

    val viewModel: HomeViewModel = koinViewModel { parametersOf(args) }
    val profileViewModel: ProfileViewModel = koinViewModel()
    val stepsViewModel: StepsViewModel = koinViewModel()
    val walletViewModel: WalletViewModel = koinViewModel()
    val reportViewModel: ReportsViewModel = koinViewModel()

    val viewState by viewModel.collectAsState()

    val currentSelectedItem = HomeNavigationItems[viewState.selectedNavigationItemIndex]

    val scope = rememberCoroutineScope()

    Scaffold(
        bottomBar = {
            HomeBottomNavigation(
                selectedNavigationItem = currentSelectedItem,
                onNavigationSelected = {
                    scope.launch {
                        backToStepsScreenDoNotShowInterAd.emit(Unit)
                        viewModel.onNavTabChange(it)
                    }
                },
            )
        },
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
        ) {
            Crossfade(targetState = currentSelectedItem.insideScreen) { insideScreen ->
                when (insideScreen) {
                    HomeNavigationInsideScreen.Profile -> ProfileScreenInHome(
                        openStepGoalChangeDialog = openStepGoalChangeDialog,
                        openMotionSensorSensitivityAdjustmentDialog = openMotionSensorSensitivityAdjustmentDialog,
                        openWeightDialog = openWeightDialog,
                        openHeightDialog = openHeightDialog,
                        openGenderDialog = openGenderDialog,
                        openStepLengthDialog = openStepLengthDialog,
                        openMeasureUnitDialog = openMeasureUnitDialog,
                        openPermissionsManager = {
                            navigator.push(PermissionsManagerNode)
                        },
                        profileViewModel = profileViewModel,
                    )

                    HomeNavigationInsideScreen.Steps -> StepsScreenInHome(
                        navigator = navigator,
                        openGoalStepChangeDialog = openStepGoalChangeDialog,
                        openSignInRewardedDialog = openSignInRewardedDialog,
                        openExchangeCoinsRewardedDialog = openExchangeCoinsRewardedDialog,
                        openNewUserRewardedDialog = openNewUserRewardedDialog,
                        openNoEnoughStepsDialog = openNoEnoughStepsDialog,
                        openStageOneRewardedDialog = openStageOneRewardedDialog,
                        openStageTwoRewardedDialog = openStageTwoRewardedDialog,
                        openGame1 = openGame1,
                        openGame3 = openGame3,
                        openGame2 = openGame2,
                        openRewardedDialog = openRewardedDialog,
                        stepsViewModel = stepsViewModel,
                    )

                    HomeNavigationInsideScreen.Reports -> ReportsScreenInHome(
                        reportsViewModel = reportViewModel,
                    )

                    HomeNavigationInsideScreen.Wallet -> WalletScreenInHome(
                        openWithdraw = openWithdraw,
                        openRedeemPicture = {
                            navigator.push(RedeemPictureNode)
                        },
                        openGame1 = openGame1,
                        openGame2 = openGame2,
                        openGame3 = openGame3,
                        openRewardedDialog = openRewardedDialog,
                        walletViewModel = walletViewModel,
                    )
                }
            }

        }
    }
}
