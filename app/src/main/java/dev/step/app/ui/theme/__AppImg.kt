package dev.step.app.ui.theme

import androidx.compose.ui.graphics.vector.ImageVector
import dev.step.app.ui.theme.appimg.IcProfileGender
import dev.step.app.ui.theme.appimg.IcProfileGoal
import dev.step.app.ui.theme.appimg.IcProfileHeight
import dev.step.app.ui.theme.appimg.IcProfileMeasureUnit
import dev.step.app.ui.theme.appimg.IcProfileSens
import dev.step.app.ui.theme.appimg.IcProfileStepLength
import dev.step.app.ui.theme.appimg.IcProfileWeight
import dev.step.app.ui.theme.appimg.IcStatDistance
import dev.step.app.ui.theme.appimg.IcStatDuration
import dev.step.app.ui.theme.appimg.IcStatKcal
import dev.step.app.ui.theme.appimg.IcStatStep
import dev.step.app.ui.theme.appimg.IcStepDistance
import dev.step.app.ui.theme.appimg.IcStepEdit
import dev.step.app.ui.theme.appimg.IcStepKcal
import dev.step.app.ui.theme.appimg.IcTabProfile
import dev.step.app.ui.theme.appimg.IcTabStat
import dev.step.app.ui.theme.appimg.IcTabStep
import dev.step.app.ui.theme.appimg.ImgNotiTips
import kotlin.collections.List as ____KtList

public object AppImg

private var __AllImgs: ____KtList<ImageVector>? = null

public val AppImg.AllImgs: ____KtList<ImageVector>
  get() {
    if (__AllImgs != null) {
      return __AllImgs!!
    }
    __AllImgs= listOf(IcTabStat, IcStatDuration, IcProfileWeight, IcStepKcal, ImgNotiTips,
        IcStepEdit, IcProfileHeight, IcStepDistance, IcTabProfile, IcProfileGender, IcStatKcal,
        IcProfileSens, IcProfileMeasureUnit, IcProfileGoal, IcStatDistance, IcTabStep,
        IcStatStep, IcProfileStepLength)
    return __AllImgs!!
  }
