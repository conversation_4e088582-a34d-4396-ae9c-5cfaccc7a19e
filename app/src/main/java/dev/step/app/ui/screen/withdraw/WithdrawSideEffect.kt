package dev.step.app.ui.screen.withdraw

sealed interface WithdrawSideEffect {
    data class ToRedeemedCoupon(val coupon: RedeemedPrize.Coupon) : WithdrawSideEffect
    data class ToRedeemedCash(val cash: RedeemedPrize.Cash) : WithdrawSideEffect
    data object InsufficientInCoins : WithdrawSideEffect
}

fun handleWithdrawSideEffect(
    it: WithdrawSideEffect,
    onToRedeemedCoupon: (coupon: RedeemedPrize.Coupon) -> Unit,
    onToRedeemedCash: (cash: RedeemedPrize.Cash) -> Unit,
    onInsufficientInCoins: () -> Unit
) {
    when (it) {
        is WithdrawSideEffect.ToRedeemedCoupon -> onToRedeemedCoupon(it.coupon)
        is WithdrawSideEffect.ToRedeemedCash -> onToRedeemedCash(it.cash)
        is WithdrawSideEffect.InsufficientInCoins -> onInsufficientInCoins()
    }
}