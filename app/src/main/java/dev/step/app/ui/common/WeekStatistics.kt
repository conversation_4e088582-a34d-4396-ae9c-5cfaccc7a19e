package dev.step.app.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.pojo.DayStepsData
import dev.step.app.ui.theme.*
import kotlinx.datetime.*
import dev.step.app.R
import dev.step.app.androidplatform.ext.time.displayName

@Composable
fun WeekStatistics(
    dayStepsDataList: List<DayStepsData>,
    todayStepsData: DayStepsData,
    dailyStepGoal: Int,
    modifier: Modifier = Modifier,
) {
    var weekAllSteps = 0
    var avgCalculateDay = 0

    dayStepsDataList.forEach { (instant, step) ->
        weekAllSteps += if (instant.epochSeconds == todayStepsData.dayInstant.epochSeconds) {
            todayStepsData.steps
        } else {
            step
        }

        if (instant.epochSeconds <= todayStepsData.dayInstant.epochSeconds) {
            avgCalculateDay++
        }
    }

    val avgSteps = try {
        weekAllSteps / avgCalculateDay

    } catch (e: Exception) {
        0
    }

    Box(modifier = modifier) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Row(verticalAlignment = Alignment.Bottom) {
                Text(
                    text = stringResource(id = R.string.text_seven_day_avg) + ":",
                    fontSize = 15.sp,
                )
                BlankSpacer(width = 6.dp)
                Text(text = avgSteps.toString(), fontSize = 17.sp, fontWeight = FontWeight.Bold)
            }
            BlankSpacer(height = 16.dp)
            Row(modifier = Modifier.bodyWidth(), horizontalArrangement = Arrangement.Center) {
                dayStepsDataList.forEach { (instant, step) ->
                    WeekOfDaysStatistics(
                        todayInstant = todayStepsData.dayInstant,
                        dayInstant = instant,
                        steps = if (todayStepsData.dayInstant.epochSeconds == instant.epochSeconds) todayStepsData.steps else step,
                        stepGoal = dailyStepGoal,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

@Composable
fun WeekOfDaysStatistics(
    todayInstant: Instant,
    dayInstant: Instant,
    steps: Int,
    stepGoal: Int,
    modifier: Modifier = Modifier,
) {
    val barHeight = 142.dp

    val goalHeight =
        if (steps >= stepGoal) barHeight else barHeight.times((steps / stepGoal.toFloat()))

    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(
                text = steps.toString(),
                fontSize = 13.sp,
                fontWeight = FontWeight.Bold,
                overflow = TextOverflow.Visible
            )
            BlankSpacer(height = 8.dp)

            Surface(
                modifier = Modifier.size(width = 20.dp, height = barHeight),
                shape = CircleShape,
                color = AppColor.PrimaryLightAlpha8
            ) {
                Box(contentAlignment = Alignment.BottomCenter) {
                    Surface(
                        modifier = Modifier.size(width = 20.dp, height = goalHeight),
                        shape = CircleShape,
                    ) {
                        Box(
                            modifier = Modifier
                                .background(AppColor.FadedPrimaryBrushVertical)
                        )
                    }
                }
            }

            BlankSpacer(height = 10.dp)

            val (dayOfWeekTextColor, dayOfWeekBgColor) = if (todayInstant.epochSeconds == dayInstant.epochSeconds) {
                AppColor.TextColorWhite to AppColor.FadedPrimaryBrushDef
            } else {
                AppColor.TextColorBlack to Brush.horizontalGradient(
                    listOf(
                        AppColor.PrimaryLightAlpha8,
                        AppColor.PrimaryLightAlpha8
                    )
                )
            }

            Surface(
                shape = RoundedCornerShape(4.dp),
            ) {
                Box(modifier = Modifier.background(brush = dayOfWeekBgColor)) {
                    val dayOfWeekText = dayInstant
                        .toLocalDateTime(TimeZone.currentSystemDefault())
                        .dayOfWeek.displayName()

                    Text(
                        text = dayOfWeekText,
                        color = dayOfWeekTextColor,
                        fontSize = 12.5.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 4.dp),
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                    )
                }
            }
        }
    }
}


@Preview
@Composable
fun WeekOfDaysStatisticsPreview() {
    val now = nowInstant()
    WeekOfDaysStatistics(todayInstant = now, dayInstant = now, steps = 5000, stepGoal = 6000)
}

@Preview
@Composable
fun WeekStatisticsPreview() {
    val now = nowInstant()

    WeekStatistics(
        dayStepsDataList = listOf(
            DayStepsData(now.minus(5, DateTimeUnit.DAY, TimeZone.currentSystemDefault()), 4321),
            DayStepsData(now, 345),
            DayStepsData(now, 789),
            DayStepsData(now, 44414),
            DayStepsData(now, 6666),
            DayStepsData(now, 0),
            DayStepsData(now, 0)
        ),
        todayStepsData = DayStepsData(now, 321),
        dailyStepGoal = 5000,
    )
}
