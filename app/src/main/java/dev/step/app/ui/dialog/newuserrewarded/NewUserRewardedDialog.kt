@file:Suppress("LocalVariableName")

package dev.step.app.ui.dialog.newuserrewarded

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.data.kvstore.WalletBizKv
import dev.step.app.ui.common.BigBadgeDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppTheme
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

// new update
// 1. 用户首次启动，弹窗按钮为Get coins，点击关闭不获取金币，但首页保留入口可以重新进入
// 2. 用户点击首页new user，触发激励广告，广告结束之后，弹窗按钮文案为ok，不设置加倍了
@Composable
fun NewUserRewardedDialog(
    adBizEnable: Boolean,
    navUp: () -> Unit,
    remoteConfigHelper: FirebaseRemoteConfigHelper = koinInject(),
//    rewardedAdHelper: MaxRewardedAdHelper = koinInject(),
    rewardedAdManager: RewardedAdManager = koinInject(),
//    interstitialAdHelper: MaxInterstitialAdHelper = koinInject(),
    walletBizKv: WalletBizKv = koinInject(),
    viewModel: NewUserRewardedDialogViewModel = koinViewModel { parametersOf(adBizEnable) },
) {
    val viewState by viewModel.collectAsState()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    viewModel.collectSideEffect {
        when (it) {
            NewUserRewardedDialogSideEffect.NavUp -> navUp()
        }
    }

    val dailyTask = remoteConfigHelper.getDailyTask()

    val _directNavUp = {
        debugLog("_navUp MaxInterstitialAdShowEvent adBizEnable: $adBizEnable")

        if (adBizEnable) { // first
            logEventRecord("exit_dialog_newuser_first")

            navUp()
        } else { // no first

            walletBizKv.setCoinBalance(
                walletBizKv.getCoinBalance() + (dailyTask?.new_user_rewarded_coins ?: 0)
            )

//            interstitialAdHelper.tryToShowAd("exit_dialog_newuser")
            OnBack()
        }
    }


    if (!viewState.hasClickGetCoinsMultiply) {
        BigBadgeDialog(
            onDismiss = {
                _directNavUp()
            },
            onClose = {
                _directNavUp()
            },
            onConfirm = {
                if (adBizEnable) {
                    viewModel.doClickGetCoinsMultiply()
                    rewardedAdManager.tryToShowRewardedLoadingDialog("newuser_first")

                    logEventRecord("click_dialog_newuser_first_button")
                } else {
                    _directNavUp()
                }
            },
            confirmText = if (adBizEnable) {
                stringResource(id = R.string.title_get_coins)
            } else {
                stringResource(id = R.string.text_ok)
            },
            singleRewardTimes = null,
            bigBadgePainter = painterResource(id = R.drawable.img_badge_new_user),
            bigBadgeTitle = "+ ${dailyTask?.new_user_rewarded_coins} ${stringResource(id = R.string.text_coins)}",
            adPlace = NativeAdPlace.Dialog,
            adPlaceName = if (adBizEnable) "newuser_first" else "newuser",
            isShowRewardedAdVideoIcon = adBizEnable
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                BlankSpacer(height = 22.dp)

                Text(text = stringResource(R.string.new_user_rewarded_title), fontSize = 20.sp)

                BlankSpacer(height = 32.dp)
            }
        }
    }
}


@Preview
@Composable
private fun NewUserRewardedDialogPreview() {
    AppTheme {
        NewUserRewardedDialog(adBizEnable = false, navUp = { /*TODO*/ })
    }
}