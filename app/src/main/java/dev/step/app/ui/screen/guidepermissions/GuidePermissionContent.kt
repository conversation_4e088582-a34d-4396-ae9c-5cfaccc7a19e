package dev.step.app.ui.screen.guidepermissions

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.NotificationsActive
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.ext.statusBarHeight
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.common.FancyFadedBgButton
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme

@Composable
fun GuidePermissionContent(
    icon: Painter,
    title: String,
    description: String,
    onGrant: () -> Unit,
    onNext: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        BlankSpacer(height = context.statusBarHeight)
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.End) {
            TextButton(onClick = onNext) {
                Text(text = stringResource(id = R.string.text_next), fontSize = 15.5.sp)
            }
        }

        Spacer(modifier = Modifier.weight(0.6f))

        Image(
            painter = painterResource(id = R.drawable.ic_app_launcher),
            contentDescription = null,
            modifier = Modifier.size(72.dp)
        )

        BlankSpacer(height = 26.dp)

        Text(
            text = stringResource(id = R.string.app_name),
            style = MaterialTheme.typography.h5.copy(color = AppColor.TextColorBlack)
        )

        BlankSpacer(height = 30.dp)

        Surface(
            shape = RoundedCornerShape(10.dp),
            color = AppColor.PrimaryLightAlpha8,
            modifier = Modifier.padding(horizontal = 10.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                BlankSpacer(height = 14.dp)

                Icon(
                    painter = icon,
                    contentDescription = null,
                    modifier = Modifier.size(32.dp),
                    tint = AppColor.Primary
                )

                BlankSpacer(height = 8.dp)

                Text(
                    text = title,
                    style = MaterialTheme.typography.h6.copy(color = AppColor.TextColorBlack),
                    modifier = Modifier
                        .scale(.98f)
                        .padding(horizontal = 16.dp),
                    textAlign = TextAlign.Center
                )
                BlankSpacer(height = 8.dp)

                Text(
                    text = description,
                    modifier = Modifier.padding(horizontal = 20.dp),
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Normal,
                    fontSize = 14.6.sp
                )

                BlankSpacer(height = 20.dp)
            }
        }

        BlankSpacer(height = 36.dp)

        FancyFadedBgButton(
            text = stringResource(R.string.text_allow),
            onClick = onGrant,
            modifier = Modifier
                .padding(horizontal = 84.dp)
                .fillMaxWidth(),
            fontSize = 15.sp
        )

        Spacer(modifier = Modifier.weight(1f))
    }
}


@Preview(
    device = Devices.PIXEL_4
)
@Composable
private fun GuidePermissionContentPreview() {
    AppTheme {
        GuidePermissionContent(
            icon = rememberVectorPainter(image = Icons.Rounded.NotificationsActive),
            title = "Permission Required for Notifications",
            description = "We need permission to keep you in the loop about your money reward and step goals. Grant us access to level up your experience.",
            onGrant = { /*TODO*/ },
            onNext = { /*TODO*/ },
            modifier = Modifier.fillMaxSize()
        )
    }
}