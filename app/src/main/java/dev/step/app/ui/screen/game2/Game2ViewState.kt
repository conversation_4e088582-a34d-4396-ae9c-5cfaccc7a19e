package dev.step.app.ui.screen.game2

import androidx.annotation.DrawableRes
import dev.step.app.data.pojo.wallet.GameRemaining

data class SlotItemPattern(
    @DrawableRes val patternResId: Int,
)

data class Game2ViewState(
    val walletCoins: Int? = null,
    val remaining: GameRemaining = GameRemaining(),

    val isPlaying: Boolean = false,

    val withdrawEnable: Boolean = false,
) {
    companion object {
        val Empty = Game2ViewState()
    }
}
