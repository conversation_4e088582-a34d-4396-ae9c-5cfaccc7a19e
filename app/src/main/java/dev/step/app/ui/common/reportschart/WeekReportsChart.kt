package dev.step.app.ui.common.reportschart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowLeft
import androidx.compose.material.icons.rounded.KeyboardArrowRight
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.ext.scale
import dev.step.app.androidplatform.ext.time.displayName
import dev.step.app.androidplatform.ext.time.monthAndDayDisplayName
import dev.step.app.androidplatform.ext.time.toHHmm
import dev.step.app.androidplatform.stepsToDistance
import dev.step.app.androidplatform.stepsToDurationSeconds
import dev.step.app.androidplatform.stepsToKcal
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.pojo.*
import dev.step.app.ui.screen.reports.ReportsStatisticsMode
import dev.step.app.ui.theme.*
import kotlinx.datetime.*
import kotlin.time.DurationUnit
import kotlin.time.toDuration

object XAxisDayOfWeekValueFormatter : ValueFormatter() {

    override fun getFormattedValue(value: Float): String {
        val ins = Instant.fromEpochSeconds(value.toLong())

        val ldt = ins.toLocalDateTime(TimeZone.currentSystemDefault())

        debugLog("WeekValueFormatter: ldt -> $ldt")

        return ldt.dayOfWeek.displayName()
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun WeekReportsChart(
    weekReportsData: WeekReportsData,
    stepLengthCm: Float,
    weightKg: Float,
    rsm: ReportsStatisticsMode,
    mus: MeasurementUnit,
    onWeekReportChange: (Instant, Instant) -> Unit,
    modifier: Modifier = Modifier,
) {
    val totalSteps = weekReportsData.dayStepsDataList.sumOf { it.steps }
    val avgSteps =
        if (weekReportsData.dayStepsDataList.isEmpty()) 0 else (totalSteps / weekReportsData.dayStepsDataList.size)
    val yMaxSteps = ((weekReportsData.dayStepsDataList.maxOfOrNull { it.steps }) ?: 0)
    val yOffsetSteps = yMaxSteps.toString().length * 20


    val (total, avg) = when (rsm) {
        ReportsStatisticsMode.Calories -> stepsToKcal(totalSteps, weightKg) to stepsToKcal(
            avgSteps,
            weightKg
        )

        ReportsStatisticsMode.Distance -> stepsToDistance(
            totalSteps,
            stepLengthCm,
            mus
        ) to stepsToDistance(avgSteps, stepLengthCm, mus)

        ReportsStatisticsMode.Steps -> totalSteps.toFloat() to avgSteps.toFloat()
        ReportsStatisticsMode.Time -> stepsToDurationSeconds(totalSteps) to stepsToDurationSeconds(
            avgSteps
        )
    }

    val (yMax, yOffset) = when (rsm) {
        ReportsStatisticsMode.Calories -> stepsToKcal(yMaxSteps, weightKg) to stepsToKcal(
            yOffsetSteps,
            weightKg
        ) + 1

        ReportsStatisticsMode.Distance -> stepsToDistance(
            yMaxSteps,
            stepLengthCm,
            mus
        ) to stepsToDistance(yOffsetSteps, stepLengthCm, mus)

        ReportsStatisticsMode.Steps -> yMaxSteps.toFloat() to yOffsetSteps.toFloat()
        ReportsStatisticsMode.Time -> stepsToDurationSeconds(yMaxSteps) to stepsToDurationSeconds(
            yOffsetSteps
        )
    }

    val lineChartPoints = weekReportsData.dayStepsDataList.map {
        val yValue = when (rsm) {
            ReportsStatisticsMode.Calories -> stepsToKcal(it.steps, weightKg)
            ReportsStatisticsMode.Distance -> stepsToDistance(it.steps, stepLengthCm, mus)
            ReportsStatisticsMode.Steps -> it.steps.toFloat()
            ReportsStatisticsMode.Time -> stepsToDurationSeconds(it.steps)
        }

        Entry(it.dayInstant.epochSeconds.toFloat(), yValue)
    }

    val dataSet = LineDataSet(lineChartPoints, "").apply {
        mode = LineDataSet.Mode.HORIZONTAL_BEZIER
        setDrawCircles(false)
        color = AppColor.Primary.toArgb()
        lineWidth = 2.5f
    }

    val chartData = LineData(dataSet).apply {
        setDrawValues(false)
    }

    var lineChart by remember { mutableStateOf<LineChart?>(null) }
    lineChart?.apply {
        axisLeft.apply {
            valueFormatter = when (rsm) {
                ReportsStatisticsMode.Time -> {
                    YAxisDurationValueFormatter
                }

                ReportsStatisticsMode.Steps -> {
                    YAxisIntValueFormatter
                }

                else -> {
                    YAxisFloatValueFormatter
                }
            }
            axisMaximum = yMax + yOffset
            axisMinimum = if (axisMaximum >= 1) -(axisMaximum * 0.01f) else -0.001f
        }

        xAxis.apply {
            axisMinimum =
                weekReportsData.weekStartDayInstant.epochSeconds.toFloat()
            axisMaximum = axisMinimum + 518400

            debugLog("week xAxis axisMinimumL:${weekReportsData.weekStartDayInstant.epochSeconds}")
            debugLog("week xAxis axisMaximumL:${weekReportsData.weekEndDayInstant.epochSeconds}")


            debugLog("week xAxis axisMinimum:${axisMinimum.toDouble().toLong()}")
            debugLog("week xAxis axisMaximum:${axisMaximum.toDouble().toLong()}")

            debugLog("week xAxis axisMaximum - axisMinimum:${axisMaximum - axisMinimum}")


        }



        data = chartData
        notifyDataSetChanged()
        invalidate()
    }

    Box(modifier = modifier) {

        Column(modifier = Modifier.bodyWidth()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.text_week),
                    modifier = Modifier.weight(1f),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                )

                val (totalText, avgText) = when (rsm) {
                    ReportsStatisticsMode.Time -> {
                        Pair(
                            total.toInt().toDuration(DurationUnit.SECONDS).toHHmm(),
                            avg.toInt().toDuration(DurationUnit.SECONDS).toHHmm()
                        )
                    }

                    ReportsStatisticsMode.Steps -> {
                        total.toInt().toString() to avg.toInt().toString()
                    }

                    else -> {
                        total.scale(3).toString() to avg.scale(3).toString()
                    }
                }

                Text(
                    text = "${stringResource(id = R.string.text_total)}: $totalText    " +
                            "${stringResource(id = R.string.text_avg)}: $avgText"
                )
            }

            Column(modifier = Modifier.background(AppColor.PrimaryLightAlpha8)) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    Surface(
                        onClick = {
                            onWeekReportChange(
                                weekReportsData.weekStartDayInstant.minus(
                                    DateTimePeriod(days = 7),
                                    TimeZone.currentSystemDefault()
                                ),
                                weekReportsData.weekEndDayInstant.minus(
                                    DateTimePeriod(days = 7),
                                    TimeZone.currentSystemDefault()
                                )
                            )
                        },
                        shape = CircleShape,
                        color = Color.Black,
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.KeyboardArrowLeft,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = Color.White,
                        )
                    }


                    val ldtStart =
                        weekReportsData.weekStartDayInstant.toLocalDateTime(TimeZone.currentSystemDefault())

                    val ldtEnd =
                        weekReportsData.weekEndDayInstant.toLocalDateTime(TimeZone.currentSystemDefault())

                    val ldtRangeText =
                        ldtStart.monthAndDayDisplayName() + " - " + ldtEnd.monthAndDayDisplayName()

                    Text(
                        text = ldtRangeText,
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Bold,
                    )

                    Surface(
                        onClick = {
                            onWeekReportChange(
                                weekReportsData.weekStartDayInstant.plus(
                                    DateTimePeriod(days = 7),
                                    TimeZone.currentSystemDefault()
                                ),
                                weekReportsData.weekEndDayInstant.plus(
                                    DateTimePeriod(days = 7),
                                    TimeZone.currentSystemDefault()
                                )
                            )
                        },
                        shape = CircleShape,
                        color = Color.Black,
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.KeyboardArrowRight,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp),
                            tint = Color.White,
                        )
                    }
                }

                AndroidView(
                    factory = {
                        LineChart(it).apply {
                            basicConfigure(rsm, yMax, yOffset)

                            xAxis.apply {
                                valueFormatter = XAxisDayOfWeekValueFormatter
                                setLabelCount(7, true)

                                axisMinimum =
                                    weekReportsData.weekStartDayInstant.epochSeconds.toFloat()
                                axisMaximum = axisMinimum + 518400
                            }


                            data = chartData
                            invalidate()
                            lineChart = this
                        }
                    }, modifier = Modifier
                        .height(200.dp)
                        .padding(horizontal = 4.dp)
                )
            }
        }
    }
}

@Preview
@Composable
fun WeekReportsChartPreview() {
    val tz = TimeZone.currentSystemDefault()
    val startInstant = LocalDateTime(
        year = 2022,
        monthNumber = 10,
        dayOfMonth = 23,
        hour = 0,
        minute = 0
    ).toInstant(tz)

    val endInstant = LocalDateTime(
        year = 2022,
        monthNumber = 10,
        dayOfMonth = 29,
        hour = 0,
        minute = 0
    ).toInstant(tz)

    val dataList = listOf(

        DayStepsData(
            LocalDateTime(
                year = 2022,
                monthNumber = 10,
                dayOfMonth = 23,
                hour = 0,
                minute = 0
            ).toInstant(tz), 2000
        ),
        DayStepsData(
            LocalDateTime(
                year = 2022,
                monthNumber = 10,
                dayOfMonth = 24,
                hour = 0,
                minute = 0
            ).toInstant(tz), 7900
        ),
        DayStepsData(
            LocalDateTime(
                year = 2022,
                monthNumber = 10,
                dayOfMonth = 25,
                hour = 0,
                minute = 0
            ).toInstant(tz), 6260
        ),
        DayStepsData(
            LocalDateTime(
                year = 2022,
                monthNumber = 10,
                dayOfMonth = 26,
                hour = 0,
                minute = 0
            ).toInstant(tz), 16000
        ),
        DayStepsData(
            LocalDateTime(
                year = 2022,
                monthNumber = 10,
                dayOfMonth = 27,
                hour = 0,
                minute = 0
            ).toInstant(tz), 6000
        ),
        DayStepsData(
            LocalDateTime(
                year = 2022,
                monthNumber = 10,
                dayOfMonth = 28,
                hour = 0,
                minute = 0
            ).toInstant(tz), 1200
        ),
        DayStepsData(
            LocalDateTime(
                year = 2022,
                monthNumber = 10,
                dayOfMonth = 29,
                hour = 0,
                minute = 0
            ).toInstant(tz), 600
        ),
    )

    WeekReportsChart(
        weekReportsData = WeekReportsData(
            weekStartDayInstant = startInstant,
            weekEndDayInstant = endInstant,
            dayStepsDataList = dataList,
        ),
        2f,
        3f,
        rsm = ReportsStatisticsMode.Steps,
        mus = MeasurementUnit.Imperial,
        onWeekReportChange = { s, n -> },
    )
}
