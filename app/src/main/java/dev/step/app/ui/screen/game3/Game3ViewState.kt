package dev.step.app.ui.screen.game3

import androidx.annotation.DrawableRes
import dev.step.app.R
import dev.step.app.data.pojo.wallet.GameRemaining

data class ScratchPattern(
    @DrawableRes val patternResId: Int,
)

sealed interface ScratchState {
    data object Doing : ScratchState
    data object Finish : ScratchState
    data object None : ScratchState
    data object Disable : ScratchState
}

data class Game3ViewState(
    val scratchState: ScratchState = ScratchState.None,
    val scratchPatterns: List<ScratchPattern> = listOf(
        ScratchPattern(R.drawable.img_sc_bule),
        ScratchPattern(R.drawable.img_sc_green),
        ScratchPattern(R.drawable.img_sc_orange),
        ScratchPattern(R.drawable.img_sc_red),
        ScratchPattern(R.drawable.img_sc_purple),
        ScratchPattern(R.drawable.img_sc_yellow)
    ),

    val remaining: GameRemaining = GameRemaining(),
    val remainingLimit: Int = 10,


    val walletCoins: Int? = null,

    val withdrawEnable: Boolean = false,
) {
    companion object {
        val Empty = Game3ViewState()
    }
}