package dev.step.app.ui.common

import androidx.compose.animation.core.*
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import dev.step.app.R
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.data.pojo.remoteconfig.RewardedBubbles
import dev.step.app.data.pojo.remoteconfig.RewardBubblesShowState
import dev.step.app.ui.theme.noRippleClickable

@Composable
fun FloatingBubbleAd(
    bubbleSize: Dp,
    floatingDurationMillis: Int,
    onBubbleClick: () -> Unit,
    modifier: Modifier = Modifier,
    imagePainter: Painter = painterResource(id = R.drawable.img_floating_rewarded_bubble),
) {

    val floatingPadding by rememberInfiniteTransition(label = "").animateFloat(
        initialValue = 0f,
        targetValue = 20f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = floatingDurationMillis
            ),
            repeatMode = RepeatMode.Reverse
        ), label = ""
    )

    Column(modifier = modifier) {
        BlankSpacer(height = floatingPadding.dp)

        Image(
            painter = imagePainter,
            contentDescription = null,
            modifier = Modifier
                .size(bubbleSize)
                .noRippleClickable { onBubbleClick() }
        )
    }
}

@Composable
fun FloatingBubbleAds(
    rewardBubblesShowState: RewardBubblesShowState?,
    rewardedBubbles: RewardedBubbles?,
    onBubbleAdClick: (id: Int) -> Unit,
    modifier: Modifier = Modifier,
) {

    Box(modifier = modifier) {
        if (rewardBubblesShowState == null || rewardedBubbles == null) {
            // do not render reward bubble ad
        } else {
            if (rewardBubblesShowState.bubble1NeedShow) {
                FloatingBubbleAd(
                    bubbleSize = 42.dp,
                    floatingDurationMillis = 1900,
                    onBubbleClick = {
                        onBubbleAdClick(1)
                        logEventRecord("click_step_bubble1")
                    },
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(start = 48.dp, top = 166.dp)
                )
            }

            if (rewardBubblesShowState.bubble2NeedShow) {
                FloatingBubbleAd(
                    bubbleSize = 42.dp,
                    floatingDurationMillis = 1900,
                    onBubbleClick = {
                        onBubbleAdClick(2)
                        logEventRecord("click_step_bubble2")
                    },
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(start = 32.dp, top = 16.dp)
                )
            }

            if (rewardBubblesShowState.bubble3NeedShow) {
                FloatingBubbleAd(
                    bubbleSize = 42.dp,
                    floatingDurationMillis = 1900,
                    onBubbleClick = {
                        onBubbleAdClick(3)
                        logEventRecord("click_step_bubble3")
                    },
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = 20.dp, top = 20.dp)
                )
            }

            if (rewardBubblesShowState.bubble4NeedShow) {
                FloatingBubbleAd(
                    bubbleSize = 42.dp,
                    floatingDurationMillis = 1900,
                    onBubbleClick = {
                        onBubbleAdClick(4)
                        logEventRecord("click_step_bubble4")
                    },
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(end = 28.dp, top = 152.dp)
                )
            }
        }
    }
}

@Preview
@Composable
private fun FloatingBubbleAdPreview() {
    FloatingBubbleAd(
        bubbleSize = 64.dp,
        2000,
        onBubbleClick = {},
    )
}

@Preview
@Composable
private fun FloatingBubbleAdsPreview() {
    FloatingBubbleAds(
        rewardBubblesShowState = RewardBubblesShowState(
            bubble1NeedShow = true,
            bubble2NeedShow = true,
            bubble3NeedShow = true,
            bubble4NeedShow = true
        ),
        rewardedBubbles = RewardedBubbles.Default,
        onBubbleAdClick = {

        },
        modifier = Modifier.fillMaxWidth()
    )
}