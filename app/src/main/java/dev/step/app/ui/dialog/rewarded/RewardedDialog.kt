package dev.step.app.ui.dialog.rewarded

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.R
import dev.step.app.RewardedDialogNode
import dev.step.app.androidplatform.biz.RatingHelper
import dev.step.app.androidplatform.biz.ad.interstitialAdRegister
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.rewarded.RewardedAdManager
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.ui.common.BigBadgeDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppTheme
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect


@Composable
fun RewardedDialog(
    rewardedDialogArgs: RewardedDialogNode.RewardedDialogArgs,
    navUp: () -> Unit,
//    rewardedAdHelper: MaxRewardedAdHelper = koinInject(),
    rewardedAdManager: RewardedAdManager = koinInject(),
    ratingHelper: RatingHelper = koinInject(),
    viewModel: RewardedDialogViewModel = koinViewModel { parametersOf(rewardedDialogArgs) },
) {
    val viewState by viewModel.collectAsState()

    val navigator = requireLocalNavigator()
    val (_, OnBack) = interstitialAdRegister(navigator, false)

    viewModel.collectSideEffect {
        when (it) {
            is RewardedDialogSideEffect.NavUp -> {
                if (rewardedDialogArgs.from == "step_bubbles") {
                    navUp()
                    ratingHelper.tryToOpenReviewDialog()
                } else {
                    navUp()
                }
            }
        }
    }
    val onClose = {
        viewModel.walletBizKv.setCoinBalance(
            viewModel.walletBizKv.getCoinBalance() + rewardedDialogArgs.coins
        )

        OnBack()
        logEventRecord("exit_dialog_rewarded")
    }

    if (!viewState.hasClickGetCoinsMultiply) {
        BigBadgeDialog(
            onDismiss = onClose,
            onClose = onClose,
            onConfirm = {
                viewModel.doClickGetCoinsMultiply()

                rewardedAdManager.tryToShowRewardedLoadingDialog(rewardedDialogArgs.from + "bonus")
                if (rewardedDialogArgs.from.isNotEmpty()) {
                    logEventRecord("click_${rewardedDialogArgs.from}_button")
                }
            },
            confirmText = if (rewardedDialogArgs.times > 1)
                stringResource(id = R.string.title_get_coins)
            else
                stringResource(id = R.string.text_ok),
            singleRewardTimes = if (rewardedDialogArgs.times > 1) rewardedDialogArgs.times else null,
            bigBadgePainter = painterResource(id = R.drawable.img_badge_rewarded),
            bigBadgeTitle = "+ ${rewardedDialogArgs.coins} ${stringResource(id = R.string.text_coins)}",
            adPlace = NativeAdPlace.Dialog,
            adPlaceName = rewardedDialogArgs.from,
            isShowRewardedAdVideoIcon = rewardedDialogArgs.times > 1
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                BlankSpacer(height = 22.dp)

                Text(text = stringResource(id = R.string.text_reward_), fontSize = 20.sp)

                BlankSpacer(height = 32.dp)
            }
        }
    }
}

@Preview
@Composable
private fun RewardedDialogPreview() {
    AppTheme {
        RewardedDialog(
            RewardedDialogNode.RewardedDialogArgs(
                from = "",
                coins = 1000,
                times = 2
            ),
            navUp = { /*TODO*/ },
        )
    }
}