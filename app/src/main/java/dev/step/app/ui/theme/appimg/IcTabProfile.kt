package dev.step.app.ui.theme.appimg

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.PathFillType.Companion.NonZero
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeCap.Companion.Butt
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.StrokeJoin.Companion.Miter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.ImageVector.Builder
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp
import dev.step.app.ui.theme.AppImg

public val AppImg.IcTabProfile: ImageVector
    get() {
        if (_icTabProfile != null) {
            return _icTabProfile!!
        }
        _icTabProfile = Builder(name = "IcTabProfile", defaultWidth = 60.0.dp, defaultHeight =
                60.0.dp, viewportWidth = 60.0f, viewportHeight = 60.0f).apply {
            path(fill = SolidColor(Color(0xFF9FA2A5)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(29.9919f, 34.519f)
                curveTo(27.005f, 34.519f, 23.3512f, 33.0419f, 19.0305f, 30.0877f)
                lineTo(19.0305f, 30.0877f)
                curveTo(18.1496f, 29.4854f, 16.9511f, 29.6808f, 16.307f, 30.5316f)
                curveTo(12.1123f, 36.0732f, 10.01f, 40.6505f, 10.0f, 44.2634f)
                curveTo(10.0f, 49.6177f, 14.5033f, 54.0f, 20.0f, 54.0f)
                lineTo(40.0f, 54.0f)
                curveTo(45.4967f, 54.0f, 50.0f, 49.6177f, 50.0f, 44.2634f)
                curveTo(49.995f, 40.4931f, 47.8894f, 35.9038f, 43.6831f, 30.4955f)
                lineTo(43.6831f, 30.4955f)
                curveTo(43.034f, 29.6608f, 41.8472f, 29.4757f, 40.9747f, 30.073f)
                curveTo(36.6446f, 33.037f, 32.9837f, 34.519f, 29.9919f, 34.519f)
                close()
            }
            path(fill = SolidColor(Color(0xFF9FA2A5)), stroke = SolidColor(Color(0x00000000)),
                    strokeLineWidth = 1.0f, strokeLineCap = Butt, strokeLineJoin = Miter,
                    strokeLineMiter = 4.0f, pathFillType = NonZero) {
                moveTo(18.0f, 18.0f)
                curveTo(18.0f, 24.6241f, 23.3684f, 30.0f, 30.0f, 30.0f)
                curveTo(36.6316f, 30.0f, 42.0f, 24.6316f, 42.0f, 18.0f)
                curveTo(42.0f, 11.3759f, 36.6316f, 6.0f, 30.0f, 6.0f)
                curveTo(23.3763f, 6.0f, 18.0f, 11.3684f, 18.0f, 18.0f)
            }
        }
        .build()
        return _icTabProfile!!
    }

private var _icTabProfile: ImageVector? = null
