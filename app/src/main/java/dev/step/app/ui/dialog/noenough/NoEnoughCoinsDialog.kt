package dev.step.app.ui.dialog.noenough

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.step.app.R
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.ui.common.AppDefWithCloseDialog
import dev.step.app.ui.common.BlankSpacer
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth

@Composable
fun NoEnoughCoinsDialog(
    navUp: () -> Unit,
) {
    AppDefWithCloseDialog(
        onDismiss = navUp,
        onClose = navUp,
        onConfirm = navUp,
        confirmText = stringResource(R.string.text_ok),
        topPainter = painterResource(id = R.drawable.img_bigmoji_sad),
        adPlace = NativeAdPlace.Dialog,
        adPlaceName = "no_step"
    ) {
        Column(
            modifier = Modifier
                .bodyWidth()
                .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            BlankSpacer(height = 32.dp)

            Text(
                text = stringResource(R.string.no_enough_coins_title),
                fontSize = 20.sp
            )

            BlankSpacer(height = 14.dp)

            Text(
                text = stringResource(R.string.no_enough_coins_tips).trimMargin(),
                fontSize = 14.sp,
            )

            BlankSpacer(height = 20.dp)
        }
    }
}

@Preview
@Composable
fun NoEnoughCoinsDialogPreview() {
    AppTheme {
        NoEnoughCoinsDialog({})
    }
}