//package dev.step.app.androidplatform.biz.ad.mrec
//
//import android.view.ViewGroup
//import android.widget.FrameLayout
//import androidx.compose.animation.animateContentSize
//import androidx.compose.foundation.background
//import androidx.compose.foundation.layout.height
//import androidx.compose.runtime.*
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.graphics.toArgb
//import androidx.compose.ui.platform.LocalContext
//import androidx.compose.ui.unit.dp
//import androidx.compose.ui.viewinterop.AndroidView
//import androidx.core.view.ViewCompat
//import androidx.lifecycle.Lifecycle
//import dev.step.app.androidplatform.OnLifecycleEvent
//import dev.step.app.androidplatform.androidcomponent.global.debugLog
//import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
//import dev.step.app.androidplatform.biz.SplashHelper
//import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.ext.time.nowInstant
//import dev.step.app.ui.theme.bodyWidth
//import kotlinx.coroutines.delay
//import org.koin.compose.koinInject
//import kotlin.time.DurationUnit
//import kotlin.time.toDuration
//
//@Composable
//fun MRecAd(
//    placeholder: MRecAdPlaceholder,
//    placeName: String,
//    modifier: Modifier = Modifier,
//    mRecAdHelper: MaxMRecAdHelper = koinInject(),
//    splashHelper: SplashHelper = koinInject(),
//    remoteConfigHelper: FirebaseRemoteConfigHelper = koinInject(),
//) {
//
//    val context = LocalContext.current
//
//    val mRecAd = mRecAdHelper.getMRecAd(context, placeholder, placeName, splashHelper)
//
//    val hasCache by mRecAd.hasCacheFlow.collectAsState()
//
//    OnLifecycleEvent { _, event ->
//        when (event) {
//            Lifecycle.Event.ON_START -> if (ViewCompat.isAttachedToWindow(mRecAd.adView)) mRecAd.startAutoRefresh()
//            Lifecycle.Event.ON_STOP -> if (ViewCompat.isAttachedToWindow(mRecAd.adView)) mRecAd.stopAutoRefresh()
//            else -> {}
//        }
//    }
//
//    var adPlaceholderLayout by remember { mutableStateOf<FrameLayout?>(null) }
//
//    LaunchedEffect(Unit) {
//        if (placeholder == MRecAdPlaceholder.Dialog || placeholder == MRecAdPlaceholder.HomeScreen) {
//            val reloadIntervalSeconds = remoteConfigHelper.getMRecAdRefreshIntervalSecondsInDialog()
//
//            val lastImpressInstant = mRecAd.getLastImpressInstant()
//            if (lastImpressInstant != null) {
//                val loadIntervalSeconds =
//                    nowInstant().epochSeconds - lastImpressInstant.epochSeconds
//                if (loadIntervalSeconds >= reloadIntervalSeconds) {
//                    mRecAd.loadAd()
//                } else {
//                    delay((reloadIntervalSeconds - loadIntervalSeconds).toDuration(DurationUnit.SECONDS))
//                    mRecAd.loadAd()
//                }
//            }
//        }
//    }
//
//    DisposableEffect(Unit) {
//        logEventRecord("ad_${placeName}_native_show")
//        logEventRecord("ad_native_show")
//
//        onDispose {
//            debugLog("MRecAd onDispose adPlaceholderLayout ${adPlaceholderLayout.hashCode()}")
//            adPlaceholderLayout?.removeAllViews()
//        }
//    }
//
//    val adaptiveModifier = if (placeholder == MRecAdPlaceholder.Dialog || hasCache)
//        modifier.height(262.dp)
//    else
//        modifier.height(0.dp)
//
//    AndroidView(
//        factory = {
//            FrameLayout(it).apply {
//                layoutParams = ViewGroup.LayoutParams(
//                    ViewGroup.LayoutParams.WRAP_CONTENT,
//                    ViewGroup.LayoutParams.WRAP_CONTENT
//                )
//                setBackgroundColor(Color.Transparent.toArgb())
//
//                runCatching {
//                    (mRecAd.adView.parent as? ViewGroup)?.removeAllViews()
//
//                    debugLog("MRecAd addView adPlaceholderLayout ${this.hashCode()}")
//                    debugLog("MRecAd addView(mRecAd.adView)")
//                    addView(mRecAd.adView)
//                }
//
//                adPlaceholderLayout = this
//            }
//        },
//        modifier = adaptiveModifier
//            .animateContentSize()
//            .background(Color.Transparent)
//            .bodyWidth(),
//    )
//}