//@file:Suppress("ObjectPropertyName")
//
//package dev.step.app.androidplatform.biz.ad.appopen
//
//import android.app.Activity
//import com.applovin.mediation.MaxAd
//import com.applovin.mediation.MaxAdListener
//import com.applovin.mediation.MaxAdRevenueListener
//import com.applovin.mediation.MaxError
//import com.applovin.mediation.ads.MaxAppOpenAd
//import com.google.firebase.analytics.FirebaseAnalytics
//import dev.step.app.BuildConfig
//import dev.step.app.androidplatform.EventFlow
//import dev.step.app.androidplatform.androidcomponent.global.debugLog
//import dev.step.app.androidplatform.biz.SplashHelper
//import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
//import dev.step.app.androidplatform.biz.analytics.logEventAdRevenueRecord
//import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.ext.time.nowInstant
//import dev.step.app.androidplatform.send
//import dev.step.app.androidplatform.sendBlock
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.GlobalScope
//import kotlinx.coroutines.delay
//import kotlinx.coroutines.flow.MutableStateFlow
//import kotlinx.coroutines.flow.first
//import kotlinx.coroutines.flow.update
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.withContext
//
//private const val TAG = "MaxAppOpenAdHelper"
//
//private const val _adReloadIntervalSeconds = 55 * 60
//
//sealed interface TryToShowExecuteResult {
//    data object DoNotShow : TryToShowExecuteResult
//    data object ShowFinish : TryToShowExecuteResult
//    data object Error : TryToShowExecuteResult
//}
//
//class MaxAppOpenAdHelper(
//    splashHelper: SplashHelper
//) {
//
//    private var _appOpenAd: MaxAppOpenAd? = null
//
//    private val _lastCacheAdInstantSeconds = MutableStateFlow(0L)
//    private val _isLoadingAdFlow = MutableStateFlow(false)
//
//    val tryToShowExecuteResultEventFlow = EventFlow<TryToShowExecuteResult>()
//
//    private val maxAdOpenAdListener = object : MaxAdListener, MaxAdRevenueListener {
//        override fun onAdLoaded(p0: MaxAd) {
//            debugLog("$TAG maxAdOpenAdListener onAdLoaded")
//            _isLoadingAdFlow.update { false }
//            _closeSplashEventFlow.sendBlock {
//                CloseSplashEvent().apply {
//                    debugLog("loadAd send closeSplashEvent when onAdLoaded(): $this")
//                }
//            }
//            _lastCacheAdInstantSeconds.update { nowInstant().epochSeconds }
//        }
//
//        override fun onAdDisplayed(p0: MaxAd) {
//            debugLog("$TAG maxAdOpenAdListener onAdDisplayed")
//
//            splashHelper.doSkipSplash(true)
//
//            logEventRecord("ad_app_open_impress")
//        }
//
//        override fun onAdHidden(p0: MaxAd) {
//            debugLog("$TAG maxAdOpenAdListener onAdHidden")
//
//            logEventRecord("ad_app_open_close")
//
//            tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.ShowFinish)
//            loadAppOpenAd()
//        }
//
//        override fun onAdClicked(p0: MaxAd) {
//            logEventRecord("ad_app_open_click")
//        }
//
//        override fun onAdLoadFailed(p0: String, p1: MaxError) {
//            debugLog("$TAG maxAdOpenAdListener onAdLoadFailed: ${p1.toString()}")
//
//            logEventRecord("ad_app_open_load_failed")
//
//            _isLoadingAdFlow.update { false }
//        }
//
//        override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
//            debugLog("$TAG maxAdOpenAdListener onAdDisplayFailed")
//
//            logEventRecord("ad_app_open_display_failed")
//            tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.Error)
//        }
//
//        override fun onAdRevenuePaid(ad: MaxAd) {
//            if (!BuildConfig.DEBUG) {
//                ad?.let {
//                    logEventAdRevenueRecord("Ad_Impression_Revenue") {
//                        putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                        putString(FirebaseAnalytics.Param.CURRENCY, "USD")
//                        putString("adNetwork", ad.networkName)
//                        putString("adFormat", ad.format.label)
//                    }
//
//                    AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(
//                        adFormat = ad.format.label,
//                        adValue = ad.revenue,
//                        adNetwork = ad.networkName,
//                        adUnitId = ad.adUnitId
//                    )
//
//                    logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
//                        putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
//                        putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
//                        putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
//                        putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
//                        putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                        putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
//                    }
//
//                    AnalyticsLogEvent.roasReport("appOpen", ad)
//                }
//            }
//        }
//
//    }
//
//    fun initIfNeed(activity: Activity) {
//        if (_appOpenAd == null) {
//            debugLog("$TAG init")
//            _appOpenAd = MaxAppOpenAd(BuildConfig.MAX_APP_OPEN_ID, activity).apply {
//                setListener(maxAdOpenAdListener)
//                setRevenueListener(maxAdOpenAdListener)
//            }
//            loadAppOpenAd()
//        }
//    }
//
//    private fun loadAppOpenAd() {
//        _appOpenAd?.let {
//            debugLog("$TAG loadAppOpenAd()")
//            it.loadAd()
//            _isLoadingAdFlow.update { true }
//        }
//    }
//
//    private fun showAppOpenAd() {
//        _appOpenAd?.showAd()
//        debugLog("$TAG _appOpenAd?.showAd()")
//    }
//
//    fun tryToLoadAd() {
//        debugLog("$TAG tryToLoadAd()")
//
//        GlobalScope.launch(Dispatchers.Main) {
////            _closeSplashEventFlow.update { null } // clean closeSplashEvent, because other event( in onAdLoaded() ) may have been sent before
//
//            val now = nowInstant()
//
//            if (
//                _appOpenAd?.isReady == true
//                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
//            ) { // has available ad cache
//                debugLog("$TAG tryToLoadAd() has available ad cache")
//
////                delay(600)
//                _closeSplashEventFlow.sendBlock {
//                    CloseSplashEvent().apply {
//                        debugLog("loadAd send closeSplashEvent when has ad cache: $this")
//                    }
//                }
//            } else {
//                val isLoadingAd = _isLoadingAdFlow.first()
//                debugLog("$TAG tryToLoadAd() isLoadingAd: $isLoadingAd")
//
//                if (!isLoadingAd) {
//                    loadAppOpenAd()
//                }
//
//                launch {
//                    delay(5000)
//                    if (_appOpenAd?.isReady != true) {
//                        _closeSplashEventFlow.sendBlock {
//                            CloseSplashEvent().apply {
//                                debugLog("loadAd send closeSplashEvent when timeout: $this")
//                            }
//                        }
//                    }
//                }
//            }
//
//        }
//    }
//
//    fun tryToShowAd(
////        tryToShowFinishedBlock: (() -> Unit)? = null
//    ) {
//        debugLog("$TAG tryToShowAd()")
//        GlobalScope.launch {
//            logEventRecord("ad_app_open_show")
//
//            val now = nowInstant()
//
//            if (
//                _appOpenAd?.isReady == true
//                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
//            ) {
//                debugLog("$TAG cache available invoke showAppOpenAd()")
//                withContext(Dispatchers.Main) {
//                    showAppOpenAd()
////                    tryToShowFinishedBlock?.invoke()
//                }
//            } else {
////                withContext(Dispatchers.Main) {
////                    tryToShowFinishedBlock?.invoke()
////                }
//                tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.DoNotShow)
//                val isLoadingAd = _isLoadingAdFlow.first()
//                debugLog("$TAG cache unavailable isLoadingAd: $isLoadingAd")
//                if (!isLoadingAd) {
//                    loadAppOpenAd()
//                }
//            }
//
//        }
//    }
//}
