package dev.step.app.androidplatform.biz.ad.admob.nat1ve

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import com.google.android.gms.ads.nativead.NativeAd
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.isDialogPlace
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.findActivity
import dev.step.app.databinding.LayoutNativeAdBinding
import dev.step.app.ui.theme.AppColor
import dev.step.app.ui.theme.AppTheme
import dev.step.app.ui.theme.bodyWidth
import org.koin.compose.koinInject

val defaultNativeAdBorderStroke = BorderStroke(
    width = 1.dp,
    color = AppColor.TextColorBlack.copy(alpha = 0.07f)
)

val emptyNativeAdBorderStroke = BorderStroke(
    width = 0.dp,
    color = Color.Transparent
)


@Composable
fun AdmobNativeAdInDialog(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
    borderStroke: BorderStroke? = emptyNativeAdBorderStroke
) {
    AdmobNativeAd(
        place = place,
        modifier = modifier,
        borderStroke = borderStroke
    )
}

@Composable
fun AdmobNativeAd(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
    borderStroke: BorderStroke? = defaultNativeAdBorderStroke
) {
    val localContext = LocalContext.current

    val nativeAdManager: AdmobNativeAdManager = koinInject()

    val nativeAd by nativeAdManager.getAdFlow(place).collectAsState()

    debugLog(tag = "NATIVE_AD") { "nativeAd: $nativeAd" }

    var adContainer by remember { mutableStateOf<FrameLayout?>(null) }
    val nativeAdContentBinding by remember { mutableStateOf(localContext.nativeAdContentBinding()) }

    if (!place.isDialogPlace()) {
        OnLifecycleEvent { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    debugLog(tag = "NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }

                    nativeAdManager.destroy(place)
                }

                else -> {}
            }
        }
    } else {
        DisposableEffect(Unit) {
            onDispose {
                debugLog(tag = "NATIVE_AD") { "DisposableEffect onDispose call destroy() place: ${place.name}" }
                nativeAdManager.destroy(place)
            }
        }
    }


    LaunchedEffect(nativeAd) {
        nativeAd?.let {
            debugLog(tag = "NATIVE_AD") { "nativeAdContentBinding.configure(it)" }
            nativeAdContentBinding.configure(it)
            adContainer?.removeAllViews()
            adContainer?.addView(nativeAdContentBinding.root)
        }
    }

    LaunchedEffect(Unit) {
        logEventRecord("ad_native_show")

        if (nativeAd == null) {
            debugLog(tag = "NATIVE_AD") { "nativeAdManager.buildAd(place)" }
            nativeAdManager.buildAd(place)
        }
    }
    val adaptiveModifier = if (place == NativeAdPlace.Dialog || nativeAd != null)
        modifier.height(262.dp)
    else
        modifier.height(0.dp)

    AndroidView(
        factory = { context ->
            FrameLayout(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            }.apply {
                adContainer = this
            }
        },
        modifier = adaptiveModifier
            .border(borderStroke ?: emptyNativeAdBorderStroke)
            .animateContentSize()
            .background(Color.Transparent)
            .bodyWidth(),
    )
}


private fun Context.nativeAdContentBinding(): LayoutNativeAdBinding {
    val layoutInflater = this.findActivity().layoutInflater

    return LayoutNativeAdBinding.inflate(layoutInflater)
}

private fun Context.nativeAdContentWithoutMediaBinding(): LayoutNativeAdBinding {
    val layoutInflater = this.findActivity().layoutInflater

    return LayoutNativeAdBinding.inflate(layoutInflater)
}

private fun LayoutNativeAdBinding.configure(nativeAd: NativeAd) {
    val nativeAdView = this.nativeAd

    nativeAdView.mediaView = this.adMedia
    nativeAdView.headlineView = this.adHeadline
    nativeAdView.bodyView = this.adBody
    nativeAdView.callToActionView = this.adCallToAction
    nativeAdView.iconView = this.adAppIcon
    nativeAdView.advertiserView = this.adAdvertiser

    this.adHeadline.text = nativeAd.headline
    nativeAd.mediaContent?.let { this.adMedia.mediaContent = it }
    if (nativeAd.body == null) {
        debugLog(tag = "NATIVE_AD") { "this.adBody.visibility = View.INVISIBLE" }

        this.adBody.visibility = View.INVISIBLE
    } else {
        this.adBody.visibility = View.VISIBLE
        this.adBody.text = nativeAd.body
    }

    if (nativeAd.callToAction == null) {
        debugLog(tag = "NATIVE_AD") { "this.adCallToAction.visibility = View.INVISIBLE" }

        this.adCallToAction.visibility = View.INVISIBLE
    } else {
        this.adCallToAction.visibility = View.VISIBLE
        this.adCallToAction.text = nativeAd.callToAction
    }

    if (nativeAd.icon == null) {
        debugLog(tag = "NATIVE_AD") { "this.adAppIcon.visibility = View.INVISIBLE" }

        this.adAppIcon.visibility = View.INVISIBLE
    } else {
        this.adAppIcon.setImageDrawable(nativeAd.icon?.drawable)
        this.adAppIcon.visibility = View.VISIBLE
    }

    if (nativeAd.advertiser == null) {
        debugLog(tag = "NATIVE_AD") { "this.adAdvertiser.visibility = View.INVISIBLE" }

        this.adAdvertiser.visibility = View.INVISIBLE
    } else {
        this.adAdvertiser.text = nativeAd.advertiser
        this.adAdvertiser.visibility = View.VISIBLE
    }

    nativeAdView.setNativeAd(nativeAd)
}

@Preview
@Composable
private fun AdmobNativeAdPreview() {
    AppTheme {
        AdmobNativeAd(place = NativeAdPlace.Test)
    }
}
