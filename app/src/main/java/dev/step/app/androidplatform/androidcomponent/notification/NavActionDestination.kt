package dev.step.app.androidplatform.androidcomponent.notification

import dev.step.app.Game1Node
import dev.step.app.Game2Node
import dev.step.app.Game3Node
import dev.step.app.HomeNode
import dev.step.app.ScreenDestinationNode
import dev.step.app.WithdrawNode
import dev.step.app.ui.screen.redeempicture.RedeemPictureNode

object NavActionDestination {
    fun destinationNodeFromNavId(
        navId: Int,
        useWithdraw: <PERSON>olean,
    ): ScreenDestinationNode {
        return when (navId) {
            1 -> HomeNode(args = HomeNode.HomeArgs(1))
            2 -> HomeNode(args = HomeNode.HomeArgs(3))
            3 -> Game1Node(args = Game1Node.Game1Args(true))
            4 -> Game2Node(args = Game2Node.Game2Args(true))
            5 -> Game3Node
            6 -> HomeNode(args = HomeNode.HomeArgs(2))
            7 -> if (useWithdraw) WithdrawNode else RedeemPictureNode
            else -> HomeNode(args = HomeNode.HomeArgs(1))
        }
    }
}