package dev.step.app.androidplatform.androidcomponent.global

import android.content.Context
import android.content.res.Configuration
import android.os.Build

@Suppress("MemberVisibilityCanBePrivate")
object DeviceInfo {
    val manufacturer: String? = Build.MANUFACTURER
    val model: String? = Build.MODEL

    val isSamsungDevice
        get() = manufacturer.equals("Samsung", true)
                || model?.startsWith("SM-") == true

    fun isDarkMode(context: Context): Boolean {
        val nightModeFlags =
            context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK

        return (nightModeFlags == Configuration.UI_MODE_NIGHT_YES)
    }
}

