package dev.step.app.androidplatform

import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import androidx.annotation.DrawableRes
import androidx.annotation.RequiresApi
import androidx.core.net.toUri
import dev.step.app.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.util.UUID

// https://medium.com/@thuat26/how-to-save-file-to-external-storage-in-android-10-and-above-a644f9293df2
// https://medium.com/@wanxiao1994/android-storage-permission-adaptation-and-reading-writing-media-files-6b85121c9461

suspend fun Context.saveDrawableToExternalStorage(
    @DrawableRes drawableResId: Int,
    fileName: String = UUID.randomUUID().toString(),
    mimeType: String = "image/png"
) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        saveDrawableToExternalStorageApi29(drawableResId, fileName, mimeType)
    } else {
        saveDrawableToExternalStorageOld(drawableResId, fileName)
    }
}

suspend fun Context.saveDrawableToExternalStorageOld(
    @DrawableRes drawableResId: Int,
    fileName: String = UUID.randomUUID().toString(),
) = withContext(Dispatchers.IO) {
    val bm = BitmapFactory.decodeResource(resources, drawableResId)

    try {
        val fileUri = saveImageToFile(fileName, bm)

        fileUri?.let {
            sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, it))
        }
    } catch (e: FileNotFoundException) {
        e.printStackTrace()
    } catch (e: IOException) {
        e.printStackTrace()
    }
}

@RequiresApi(Build.VERSION_CODES.Q)
suspend fun Context.saveDrawableToExternalStorageApi29(
    @DrawableRes drawableResId: Int,
    fileName: String = UUID.randomUUID().toString(),
    mimeType: String
) = suspendCoroutineWithTimeout { continuation ->
    val contentValues = ContentValues().apply {
        put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
        put(MediaStore.MediaColumns.MIME_TYPE, mimeType)
        put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DOWNLOADS)
    }
    val resolver = contentResolver
    val uri = resolver.insert(MediaStore.Downloads.EXTERNAL_CONTENT_URI, contentValues)
    if (uri != null) {
        val bm = BitmapFactory.decodeResource(resources, drawableResId)

        resolver.openOutputStream(uri).use { output ->
            if (output != null) {
                bm.compress(Bitmap.CompressFormat.PNG, 100, output)
                output.flush()
                output.close()
            }

            continuation.resumeIfActive(Unit)
        }
    }
}

fun saveImageToFile(displayName: String, bitmap: Bitmap): Uri? {
    val directory = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
    val imageFile = File(directory, "$displayName.png")
    if (!directory.isDirectory) {
        directory.mkdir()
    }
    return if (directory.isDirectory) {
        try {
            imageFile.outputStream().use {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, it)
            }
            imageFile.toUri()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    } else {
        null
    }
}