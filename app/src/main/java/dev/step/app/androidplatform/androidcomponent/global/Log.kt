package dev.step.app.androidplatform.androidcomponent.global

import co.touchlab.kermit.Logger
import dev.step.app.BuildConfig


@Suppress("NOTHING_TO_INLINE", "MoveLambdaOutsideParentheses")
inline fun debugLog(
    msg: String?,
    tag: String? = null,
) {
    if (BuildConfig.DEBUG) {
        Logger.d(tag = tag ?: "Kermit", throwable = null, { "lalala -> $msg" })
    }
}

@Suppress("NOTHING_TO_INLINE")
inline fun debugLog(
    throwable: Throwable? = null,
    tag: String? = null,
    noinline message: () -> String
) {
    if (BuildConfig.DEBUG) {
        if (tag == null) {
            Logger.d(throwable = throwable, message = message)
        } else {
            Logger.d(throwable, tag, message)
        }
    }
}

@Suppress("NOTHING_TO_INLINE")
inline fun debugLog(
    message: String,
    throwable: Throwable? = null,
    tag: String? = null
) {
    if (BuildConfig.DEBUG) {
        if (tag == null) {
            Logger.d(throwable = throwable, message = { message })
        } else {
            Logger.d(throwable, tag) { message }
        }
    }
}