package dev.step.app.androidplatform.biz.ad.tradplus.interstitial

import android.app.Activity
import android.content.Context
import com.tradplus.ads.open.TradPlusSdk
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.interstitial.InterstitialAdListener
import com.tradplus.ads.open.interstitial.TPInterstitial
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.FullscreenAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.TradplusAdUnitIds
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.time.Duration.Companion.seconds

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L
private const val AD_LOADING_TIMEOUT_SECONDS = 8

@Single
class TradplusInterstitialAdManager(
  private val splashController: SplashHelper,
  private val fullscreenAdManager: FullscreenAdManager
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "TradplusInterstitialAdManager"

  private val adKey: String = TradplusAdUnitIds.INTERSTITIAL

  private var tpInterstitial: TPInterstitial? = null
  private val isLoadingAd = AtomicBoolean(false)
  private var loadingStartTime: Long = 0
  private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

  private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

  sealed interface AdLoadingStateEvent {
    data object TimeOut : AdLoadingStateEvent
    data object Loaded : AdLoadingStateEvent
    data object FailedToLoad : AdLoadingStateEvent
  }

  sealed interface AdShowStateEvent {
    data object Finish : AdShowStateEvent
    data object Showing : AdShowStateEvent
    data object FailedToShow : AdShowStateEvent
    data object SkipToShow : AdShowStateEvent
  }

  val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<AdShowStateEvent>()

  val adShowStateFlow = MutableStateFlow(false)

  private var sendLoadingTimeOutJob: Job? = null

  fun tryToLoadAd(context: Context) {
    debugLog(tag = TAG) { "tryToLoadAd" }

    // 兜底检查：如果加载时间过长，强制重置
    if (isLoadingTooLong()) {
      debugLog(tag = TAG) { "Loading too long, force reset" }
      resetLoadingState()
    }

    if (isLoadingAd.get()) return

    if (isAdAvailable()) {
      debugLog(tag = TAG) { "hasAdAvailable" }
    } else {
      debugLog(tag = TAG) { "noAdAvailable" }
      loadAd(context)
    }
  }

  private fun loadAd(context: Context) {
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
    sendLoadingTimeOutJob = GlobalScope.launch(Dispatchers.Default) {
      delay(AD_LOADING_TIMEOUT_SECONDS.seconds)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
      withContext(Dispatchers.Main.immediate) { resetLoadingState() }
      adLoadingStateEventFlow.send(AdLoadingStateEvent.TimeOut)
    }

    if (isLoadingAd.get()) {
      debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
      return
    }

    // Check if TradPlus SDK is initialized
    if (!TradPlusSdk.getIsInit()) {
      debugLog(tag = TAG) { "TradPlus SDK not initialized yet, cannot load ad" }
      resetLoadingState()
      adLoadingStateEventFlow.send(AdLoadingStateEvent.FailedToLoad)
      return
    }

    debugLog(tag = TAG) { "loadAd" }

    startLoading()

    // Initialize TPInterstitial
    tpInterstitial = TPInterstitial(context, adKey)

    // Set ad scenario entry (optional)
    latestActiveAdPlaceNameFlow.value?.let { scenarioId ->
      tpInterstitial?.entryAdScenario(scenarioId)
    }

    // Set listener
    tpInterstitial?.setAdListener(object : InterstitialAdListener {
      override fun onAdLoaded(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdLoaded" }
        resetLoadingState()
        latestLoadAdSuccessInstant = nowInstant()

        adLoadingStateEventFlow.send(AdLoadingStateEvent.Loaded)
        debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

        logEventRecord("ad_interstitial_load_success")
      }

      override fun onAdFailed(error: TPAdError?) {
        debugLog(tag = TAG) { "onAdFailed: ${error?.errorMsg}" }
        resetLoadingState()
        adLoadingStateEventFlow.send(AdLoadingStateEvent.FailedToLoad)
      }

      override fun onAdClicked(adInfo: TPAdInfo?) {
        splashController.doSkipSplash(true)
        logEventRecord("ad_interstitial_click")
        debugLog(tag = TAG) { "onAdClicked" }
      }

      override fun onAdImpression(adInfo: TPAdInfo?) {
        logEventRecord("ad_interstitial_impress")
        debugLog(tag = TAG) { "onAdImpression" }

        fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
        adShowStateEventFlow.send(AdShowStateEvent.Showing)
        adShowStateFlow.update { true }

        // Record revenue analytics
        adInfo?.let { info ->
          val adSourceName = info.adSourceName
          val adFormat = "interstitial"
          val adUnitId = adKey

          // TODO: TradPlus revenue tracking - need to implement when revenue data is available
          // For now, just track impression
          AnalyticsLogEvent.recordAdImpression(
            null, // adValue not available in TradPlus callback
            adSourceName,
            adFormat,
            adUnitId
          )
        }
      }


      override fun onAdVideoStart(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdVideoStart" }

      }

      override fun onAdVideoEnd(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdVideoEnd" }
      }

      override fun onAdVideoError(adInfo: TPAdInfo?, error: TPAdError?) {
        debugLog(tag = TAG) { "onAdVideoError: ${error?.errorMsg}" }
        tpInterstitial = null
        loadAd(context)
        adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
        adShowStateFlow.update { false }
      }

      override fun onAdClosed(adInfo: TPAdInfo?) {
        debugLog(tag = TAG) { "onAdClosed" }
        tpInterstitial?.onDestroy()
        tpInterstitial = null
        loadAd(context)
        adShowStateEventFlow.send(AdShowStateEvent.Finish)
        GlobalScope.launch {
          delay(2000)
          adShowStateFlow.emit(false)
        }
      }
    })

    // Load the ad
    tpInterstitial?.loadAd()

    logEventRecord("ad_interstitial_load")
  }

  private fun isAdAvailable(): Boolean {
    return tpInterstitial?.isReady() == true && checkAdIsValidAtCachePeriod()
  }

  private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
    val secondsDifference: Long =
      nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
    return secondsDifference < adCachePeriodSeconds
  }

  private fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd" }

    logEventRecord("ad_interstitial_show")

    if (tpInterstitial?.isReady() == true) {
      tpInterstitial?.showAd(activity, "")
    } else {
      debugLog(tag = TAG) { "showAd failed: ad not ready" }
      adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
      adShowStateFlow.update { false }
    }
  }

  suspend fun tryToShowAd(
    activity: Activity,
    adPlaceName: String? = null,
    onReadyShowAd: (() -> Unit)? = null
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd" }

    adPlaceName?.let {
      latestActiveAdPlaceNameFlow.update { "inter_$adPlaceName" }
    }

    if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
      debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
      adShowStateEventFlow.send(AdShowStateEvent.SkipToShow)
    } else { // over the show interval, need to show ad
      onReadyShowAd?.invoke()
      debugLog(tag = TAG) { "over the show interval, need to show ad" }
      if (isAdAvailable()) { // cache available
        debugLog(tag = TAG) { "cache available" }
        delay(1_000)
        showAd(activity)
      } else { // cache not available
        debugLog(tag = TAG) { "cache not available" }
        loadAd(activity)
      }
    }
  }

  private fun startLoading() {
    loadingStartTime = System.currentTimeMillis()
    isLoadingAd.set(true)
  }

  private fun resetLoadingState() {
    loadingStartTime = 0
    isLoadingAd.set(false)
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
  }

  private fun isLoadingTooLong(): Boolean {
    return isLoadingAd.get() && 
           loadingStartTime > 0 && 
           (System.currentTimeMillis() - loadingStartTime) >= 60_000L
  }

  fun onDestroy() {
    debugLog(tag = TAG) { "onDestroy" }
    resetLoadingState()
    tpInterstitial?.onDestroy()
    tpInterstitial = null
  }
}