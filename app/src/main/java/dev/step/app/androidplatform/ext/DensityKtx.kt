package dev.step.app.androidplatform.ext

import android.util.TypedValue
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import dev.step.app.androidplatform.androidcomponent.global.globalContext

val Dp.px: Float
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        this.value,
        globalContext.resources.displayMetrics
    )

val TextUnit.px: Float
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_SP,
        this.value,
        globalContext.resources.displayMetrics
    )


private const val STANDARD_SCREEN_WIDTH_DP = 392f

@Composable
fun currentScreenRatio() =
    LocalConfiguration.current.screenWidthDp / STANDARD_SCREEN_WIDTH_DP
