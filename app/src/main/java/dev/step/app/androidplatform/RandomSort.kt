package dev.step.app.androidplatform

import java.util.UUID

fun <T : Any> Collection<T>.randomSelection(selectionSum: Int): List<T> {
    return this.randomSort().subList(0, selectionSum)
}

fun <T : Any> Collection<T>.randomSort(): List<T> {
    val randomSortableList = this.map { RandomSortable(data = it) }

    return hashSetOf<RandomSortable>().also {
        it.addAll(randomSortableList)
    }.map { it.data as T }
}

data class RandomSortable(
    val data: Any,
    private val uuid: String = UUID.randomUUID().toString()
)
