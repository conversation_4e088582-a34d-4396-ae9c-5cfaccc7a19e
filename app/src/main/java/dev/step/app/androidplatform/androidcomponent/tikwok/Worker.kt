package dev.step.app.androidplatform.androidcomponent.tikwok

import android.content.Context
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequest
import androidx.work.WorkManager
import dev.step.app.BuildConfig
import java.util.concurrent.TimeUnit

object TikWok {
    fun startWorker(context: Context) {
        //todo https://developer.android.com/topic/libraries/architecture/workmanager#deferrable 官方文档

        //每15分钟，执行任务一次
        val workRequest = PeriodicWorkRequest.Builder(
            TickWorker::class.java,
            15,
            TimeUnit.MINUTES
        ) //                .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .build()
        WorkManager.getInstance(context)
            .enqueueUniquePeriodicWork(
                "lalala_" + BuildConfig.APPLICATION_ID,
                ExistingPeriodicWorkPolicy.KEEP,
                workRequest
            )
    }
}