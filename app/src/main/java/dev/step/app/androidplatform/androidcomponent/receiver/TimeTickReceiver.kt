package dev.step.app.androidplatform.androidcomponent.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import dev.step.app.androidplatform.androidcomponent.notification.MonoConfigPollingMessageNotification
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.kvstore.WalletBizKv
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object TimeTickReceiver : BroadcastReceiver(), KoinComponent {

    private val firebaseRemoteConfigHelper: FirebaseRemoteConfigHelper by inject()

//    private val walletBizKv: WalletBizKv by inject()

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == Intent.ACTION_TIME_TICK) {
            handleTimeTick()
        }
    }

    fun handleTimeTick() {
        val instant = nowInstant()

        firebaseRemoteConfigHelper.tryToUpdateConfig(instant)

        MonoConfigPollingMessageNotification.tryToShowNotification(instant)

//            val signInTimes = walletBizKv.getSignInTimes()
//            if (signInTimes >= 3) {
//                RewardWhenSignIn3dayNotification.tryToShowNotification(instant, true)
//            }
    }
}

fun registerTimeTickReceiver(context: Context) {
    context.registerReceiver(TimeTickReceiver, IntentFilter(Intent.ACTION_TIME_TICK))
}
