@file:Suppress("ObjectPropertyName")

package dev.step.app.androidplatform.biz

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.annotation.Keep
import com.google.android.gms.ads.AdValue
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.tenjin.android.TenjinSDK
import com.tenjin.android.config.TenjinConsts
import com.tenjin.android.params.TenjinParams
import com.tenjin.android.store.SharedPrefsStore
import dev.step.app.BuildConfig
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalContext
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.biz.analytics.logEventRecordWithTenjin
import dev.step.app.androidplatform.biz.analytics.logEventRecordWithoutAttribution
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.androidplatform.resumeIfActive
import dev.step.app.androidplatform.suspendCoroutineWithTimeout
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.serialization.Serializable
import org.json.JSONObject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val TAG = "TenjinHelper"

class TenjinHelper(
    private val userOperateDataKv: UserOperateDataKv
) {

    fun initAndStoreAttributionIfNeed(activity: Activity) {
        GlobalScope.launch(Dispatchers.Main) {
            val tz = TimeZone.UTC

            val lastTenjinInitSuccessStoreInstant =
                userOperateDataKv.lastTenjinInitSuccessInstant

            val nowInstant = nowInstant()

            if (lastTenjinInitSuccessStoreInstant
                    .todayStartInstant(tz)
                    .epochSeconds == nowInstant.todayStartInstant(tz).epochSeconds
            ) {
                // 和上次初始化相比还在同一天内, 不需要触发初始化
                debugLog("$TAG not need to do anything")
            } else {
                init(activity)
                userOperateDataKv.storeTenjinInitSuccessInstant(nowInstant())

                delay(2000)

                getOrFetchAttributionInfo(activity)
            }
        }
    }

    private fun init(activity: Activity): TenjinSDK? {
        debugLog("$TAG init")

        return TenjinSDK.getInstance(
            activity.applicationContext ?: globalContext.applicationContext,
            BuildConfig.TENJIN_SDK_KEY
        ).apply {
            setCacheEventSetting(true)
            setAppStore(TenjinSDK.AppStoreType.googleplay)
            connect()
//            AppLovinSdk.getInstance(globalContext).userIdentifier = this.analyticsInstallationId

            debugLog("test_tenjin connect()")
            logEventRecord("tenjin_init")
        }
    }

    fun analyticsInstallation(context: Context): String? {
        val tenjinParams = TenjinParams(SharedPrefsStore(context))
        return tenjinParams.analyticsInstallationID
    }

    fun getLocalTenjin() = userOperateDataKv.tenjinAttr

    private var latestCallFetchAttributionInfoEpochSeconds = MutableStateFlow(0L)

    suspend fun getOrFetchAttributionInfo(activity: Activity?): TenjinAttribution {
        if (activity == null) return getLocalTenjin()

        val localAttribution = getLocalTenjin()

        return if (localAttribution.isEmpty()) {
            debugLog("test_tenjin localAttribution isEmpty")
            val nowEpochSeconds = nowInstant().epochSeconds

            if (nowEpochSeconds - latestCallFetchAttributionInfoEpochSeconds.first() < 30) {
                return getLocalTenjin().apply {
                    debugLog("$TAG call FetchAttributionInfo too close and return localAttribution: $this")
                }
            } else {
                latestCallFetchAttributionInfoEpochSeconds.update { nowEpochSeconds }
            }
//            val retryIntervalDuration =
//                nowInstant().epochSeconds - lastRetryInstant.first().epochSeconds
//
//            debugLog("$TAG retryDuration: ${30 - retryIntervalDuration}")
//
//            if (retryIntervalDuration <= 30) {
//                delay((30 - retryIntervalDuration).toDuration(DurationUnit.SECONDS))
//            }

            debugLog("$TAG call fetchAttr")

            val networkAttribution =
                TenjinSDK.getInstance(activity.applicationContext, BuildConfig.TENJIN_SDK_KEY)
                    .apply {
                        setCacheEventSetting(true)
                    }
                    ?.fetchAttributionInfo(activity.localClassName)
                    ?: TenjinAttribution.Empty

            if (!networkAttribution.isEmpty()) {
                userOperateDataKv.doneTenjinRequestResultForEventRecord {
                    logEventRecordWithTenjin(
                        eventName = "tenjin_request_result",
                        tenjinAttribution = networkAttribution
                    )
                    debugLog("$TAG doneTenjinRequestResultForEventRecord")
                }
                saveAttributionInfo(networkAttribution)
            }

            networkAttribution
        } else {
            debugLog("test_tenjin localAttribution isNotEmpty")

            localAttribution
        }
    }

    private var _tenjin: TenjinSDK? = null

    private fun getTenjin(): TenjinSDK? {
        return if (_tenjin == null) {
            val tenjinInstance =
                TenjinSDK.getInstance(globalContext, BuildConfig.TENJIN_SDK_KEY).apply {
                    setCacheEventSetting(true)
                }
            _tenjin = tenjinInstance
            tenjinInstance
        } else {
            _tenjin
        }
    }

//    fun roasApplovin(maxAd: MaxAd) {
//        getTenjin()?.eventAdImpressionAppLovin(maxAd)
//    }

    fun roasAdmob(adValue: AdValue, ad: Any?) {
        getTenjin()?.eventAdImpressionAdMob(adValue, ad)
    }

//    fun roasBigoAppOpenAd(ad: SplashAd) {
//        try {
//            val eventJson = JSONObject().apply {
//                put("revenue", ad.bid?.price?.let { it / 1000 } ?: 0.0)
//                put("revenue_precision", "exact")
//                put("ad_revenue_currency", "USD")
//                put("country", "RU")
//                put("format", "APP_OPEN")
//                put("creative_id", ad.creativeId ?: "")
//                put("style", ad.style.ordinal)
//                put("skippable", ad.isSkippable)
//                put("is_expired", ad.isExpired)
//            }
//            getTenjin()?.eventAdImpression("bigo", eventJson)
//        } catch (e: Exception) {
//            Firebase.crashlytics.recordException(e)
//        }
//    }

    private suspend fun saveAttributionInfo(attributionInfo: TenjinAttribution) {
        debugLog("$TAG saveAttributionInfo() attributionInfo: $attributionInfo")
        debugLog("test_tenjin save AttributionInfo: ${attributionInfo.adNetwork}")
        userOperateDataKv.saveTenjinAttribution(attributionInfo)
    }

    private suspend fun TenjinSDK.fetchAttributionInfo(from: String) =
        suspendCoroutineWithTimeout<TenjinAttribution?>(10.toDuration(DurationUnit.SECONDS)) { continuation ->

            runCatching {
                // 获取归因
                debugLog("test_tenjin fetch AttributionInfo")

                getAttributionInfo { attributionInfoResult ->
//                    lastRetryInstant.update {
//                        nowInstant().also { now ->
//                            debugLog("$TAG lastRetryInstant.update{ ${now.epochSeconds} }")
//                        }
//                    }

                    debugLog("$TAG network TenjinSDK.getAttributionInfo{} from:$from")

                    userOperateDataKv.doingTenjinRequestForEventRecord {
                        logEventRecordWithoutAttribution("tenjin_request")
                        debugLog("$TAG doingTenjinRequestForEventRecord")
                    }

                    var buildAttributionInfo = TenjinAttribution.Empty
                    if (attributionInfoResult?.containsKey(TenjinConsts.ATTR_PARAM_AD_NETWORK) == true) {
                        val adNetwork =
                            attributionInfoResult.getOrElse(TenjinConsts.ATTR_PARAM_AD_NETWORK) { null }
                        if (!adNetwork.isNullOrEmpty()) {
                            buildAttributionInfo = buildAttributionInfo.copy(adNetwork = adNetwork)

//                            userOperateDataStore.doneTenjinRequestResultForEventRecord {
//                                logEventRecord("tenjin_request_result")
//                                debugLog("$TAG doneTenjinRequestResultForEventRecord")
//                            }
                        }
                    }

                    if (attributionInfoResult?.containsKey(TenjinConsts.ATTR_PARAM_CAMPAIGN_NAME) == true) {
                        val campaignName =
                            attributionInfoResult.getOrElse(TenjinConsts.ATTR_PARAM_CAMPAIGN_NAME) { null }
                        if (!campaignName.isNullOrEmpty()) {
                            buildAttributionInfo =
                                buildAttributionInfo.copy(campaignName = campaignName)
                        }
                    }

                    if (attributionInfoResult?.containsKey(TenjinConsts.ATTR_PARAM_CAMPAIGN_ID) == true) {
                        val campaignId =
                            attributionInfoResult.getOrElse(TenjinConsts.ATTR_PARAM_CAMPAIGN_ID) { null }
                        if (!campaignId.isNullOrEmpty()) {
                            buildAttributionInfo =
                                buildAttributionInfo.copy(campaignId = campaignId)
                        }
                    }

                    continuation.resumeIfActive(buildAttributionInfo.also {
                        debugLog("$TAG attributionInfo: $it")
                        debugLog("test_tenjin fetch AttributionInfo finish")
                        debugLog("test_tenjin fetch AttributionInfo network: ${it.adNetwork}")
                    })
                }
            }.onFailure {
                Firebase.crashlytics.recordException(it)
                it.printStackTrace()
            }
        }
}

@Keep
@Serializable
data class TenjinAttribution(
    val adNetwork: String = "",
    val campaignName: String = "",
    val campaignId: String = "",
) {
    fun isEmpty() = adNetwork.trim().isEmpty()

    fun isOrganic() = adNetwork.lowercase() == "organic" || adNetwork.isEmpty()

    companion object {
        val Empty get() = TenjinAttribution()
    }
}