@file:Suppress("ObjectPropertyName", "LocalVariableName")

package dev.step.app.androidplatform

import si.uom.SI
import systems.uom.ucum.UCUM
import tech.units.indriya.quantity.Quantities
import kotlin.math.roundToInt

fun kgToLb(kg: Float): Float {
    val _kg = Quantities.getQuantity(kg, SI.KILOGRAM)
    return _kg.to(UCUM.POUND).value.toFloat()
}

fun lbToKg(lb: Float): Float {
    val _lb = Quantities.getQuantity(lb, UCUM.POUND)
    return _lb.to(SI.KILOGRAM).value.toFloat()
}

fun kmToMile(km: Float): Float {
    val _m = Quantities.getQuantity(km * 1000, UCUM.METER)
    return _m.to(UCUM.MILE_INTERNATIONAL).value.toFloat()
}

fun mileToKm(mi: Float): Float {
    val _mi = Quantities.getQuantity(mi, UCUM.MILE_INTERNATIONAL)
    return _mi.to(UCUM.METER).value.toFloat()
}

fun cmToFtIn(cm: Float): Pair<Int, Int> {
    val height = cm / 2.54
    val ft = height / 12
    val `in` = height % 12

    return ft.toInt() to `in`.roundToInt()
}

fun ftInToCm(ftIn: Pair<Int, Int>): Float {
    val (ft, `in`) = ftIn

    val _ft = Quantities.getQuantity(ft, UCUM.FOOT_INTERNATIONAL)
    val _in = Quantities.getQuantity(`in`, UCUM.INCH_INTERNATIONAL)

    val add = _ft.add(_in)

    return add.to(UCUM.METER).value.toFloat() * 100
}
