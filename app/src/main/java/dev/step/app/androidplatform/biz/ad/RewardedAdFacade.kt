package dev.step.app.androidplatform.biz.ad

import android.app.Activity
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.rewarded.AdmobRewardedAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.rewarded.TradPlusRewardedAdManager
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class RewardedAdFacade(
    private val admobManager: AdmobRewardedAdManager,
    private val tradplusManager: TradPlusRewardedAdManager,
    private val remoteConfig: FirebaseRemoteConfigHelper
) {
    companion object {
        private val DEFAULT_PROVIDER = AdProvider.TRADPLUS
    }

    // Cache to avoid frequent remote config reads
    private var cachedProvider: AdProvider? = null

    fun getCurrentProvider(): AdProvider {
        return cachedProvider ?: remoteConfig.getAdProvider().also { cachedProvider = it }
    }

    fun refreshProvider() {
        cachedProvider = null
    }

    fun tryToLoadAd(activity: Activity) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.tryToLoadAd(activity)
            AdProvider.TRADPLUS -> tradplusManager.tryToLoadAd(activity)
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        adPlaceName: String? = null,
        skipDelay: Boolean = false
    ) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.tryToShowAd(activity, adPlaceName, skipDelay)
            AdProvider.TRADPLUS -> tradplusManager.tryToShowAd(activity, adPlaceName, skipDelay)
        }
    }

    fun isAdAvailable(): Boolean {
        return when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.isAdAvailable()
            AdProvider.TRADPLUS -> tradplusManager.isAdAvailable()
        }
    }



    val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AdShowStateEvent>()

    init {
        // Set up event flow adapters
        setupEventFlowAdapters()
    }

    private fun setupEventFlowAdapters() {
        GlobalScope.launch(Dispatchers.Main) {
            // Adapt AdMob events
            admobManager.adLoadingStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    AdmobRewardedAdManager.AdLoadingStateEvent.TimeOut -> AdLoadingStateEvent.TimeOut
                    AdmobRewardedAdManager.AdLoadingStateEvent.Loaded -> AdLoadingStateEvent.Loaded
                    AdmobRewardedAdManager.AdLoadingStateEvent.FailedToLoad -> AdLoadingStateEvent.FailedToLoad
                }
                if (getCurrentProvider() == AdProvider.ADMOB) {
                    adLoadingStateEventFlow.send(commonEvent)
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // Adapt TradPlus events
            tradplusManager.adLoadingStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    TradPlusRewardedAdManager.AdLoadingStateEvent.TimeOut -> AdLoadingStateEvent.TimeOut
                    TradPlusRewardedAdManager.AdLoadingStateEvent.Loaded -> AdLoadingStateEvent.Loaded
                    TradPlusRewardedAdManager.AdLoadingStateEvent.FailedToLoad -> AdLoadingStateEvent.FailedToLoad
                }
                if (getCurrentProvider() == AdProvider.TRADPLUS) {
                    adLoadingStateEventFlow.send(commonEvent)
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // Adapt AdMob show events
            admobManager.adShowStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    AdmobRewardedAdManager.AdShowStateEvent.Finish -> AdShowStateEvent.Finish
                    AdmobRewardedAdManager.AdShowStateEvent.Showing -> AdShowStateEvent.Showing
                    AdmobRewardedAdManager.AdShowStateEvent.FailedToShow -> AdShowStateEvent.FailedToShow
                }
                if (getCurrentProvider() == AdProvider.ADMOB) {
                    adShowStateEventFlow.send(commonEvent)
                }
            }
        }

        GlobalScope.launch(Dispatchers.Main) {
            // Adapt TradPlus show events
            tradplusManager.adShowStateEventFlow.collect { event ->
                val commonEvent = when (event) {
                    TradPlusRewardedAdManager.AdShowStateEvent.Finish -> AdShowStateEvent.Finish
                    TradPlusRewardedAdManager.AdShowStateEvent.Showing -> AdShowStateEvent.Showing
                    TradPlusRewardedAdManager.AdShowStateEvent.FailedToShow -> AdShowStateEvent.FailedToShow
                }
                if (getCurrentProvider() == AdProvider.TRADPLUS) {
                    adShowStateEventFlow.send(commonEvent)
                }
            }
        }
    }

    val adEarnedRewardEventFlow: EventFlow<Boolean>
        get() = when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.adEarnedRewardEventFlow
            AdProvider.TRADPLUS -> tradplusManager.adEarnedRewardEventFlow
        }

    fun onDestroy() {
        when (getCurrentProvider()) {
            AdProvider.TRADPLUS -> tradplusManager.onDestroy()
            AdProvider.ADMOB -> { /* AdmobRewardedAdManager doesn't have onDestroy */ }
        }
    }
}