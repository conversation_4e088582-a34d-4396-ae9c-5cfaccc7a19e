package dev.step.app.androidplatform.biz.ad

sealed interface NativeAdEvent {
    data class LoadingStateEvent(val place: NativeAdPlace, val event: AdLoadingStateEvent) : NativeAdEvent
    data class ShowStateEvent(val place: NativeAdPlace, val event: AdShowStateEvent) : NativeAdEvent
    data class ImpressionEvent(val place: NativeAdPlace) : NativeAdEvent
    data class ClickEvent(val place: NativeAdPlace) : NativeAdEvent
}