@file:Suppress("LocalVariableName")

package dev.step.app.androidplatform.biz.analytics

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import com.google.android.gms.ads.AdValue
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import dev.step.app.BuildConfig
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalContext
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.TenjinAttribution
import dev.step.app.androidplatform.biz.TenjinHelper
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.withId
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

@SuppressLint("StaticFieldLeak")
object AnalyticsLogEvent : KoinComponent {

    private const val TAG = "AnalyticsLogEvent"

    private val context: Context = globalContext
    private val tenjinHelper: TenjinHelper by inject()

    private val mmkv = AppMMKV.withId(TAG)

    fun record(eventName: String, args: Bundle? = Bundle()) {
        if (BuildConfig.DEBUG) return

        GlobalScope.launch {
            if (args == null) {
                debugLog("$TAG eventName:{$eventName}")
            }

            Firebase.analytics.logEvent(eventName, args?.apply {
                val tenjinAttribution = tenjinHelper.getOrFetchAttributionInfo(globalMainActivity)

                putString("tenjin_network", tenjinAttribution.adNetwork)
                putString("tenjin_campaign", tenjinAttribution.campaignName)

                debugLog("$TAG eventName:{$eventName}, tenjin_network:{${tenjinAttribution.adNetwork}}, tenjin_campaign:{${tenjinAttribution.campaignName}}")
            })
        }
    }

    fun recordWithInjectTenjin(eventName: String, tenjinAttribution: TenjinAttribution?) {
        if (BuildConfig.DEBUG) return

        if (tenjinAttribution == null) return

        GlobalScope.launch {
            debugLog("$TAG eventName:{$eventName}")

            Firebase.analytics.logEvent(eventName, Bundle().apply {

                putString("tenjin_network", tenjinAttribution.adNetwork)
                putString("tenjin_campaign", tenjinAttribution.campaignName)

                debugLog("$TAG eventName:{$eventName}, tenjin_network:{${tenjinAttribution.adNetwork}}, tenjin_campaign:{${tenjinAttribution.campaignName}}")
            })
        }
    }

    fun revenueRecord(eventName: String, args: Bundle? = Bundle()) {
        if (BuildConfig.DEBUG) return

        GlobalScope.launch {
            if (args == null) {
                debugLog("$TAG eventName:{$eventName}")
            }

            Firebase.analytics.logEvent(eventName, args?.apply {
                val tenjinAttribution = tenjinHelper.getOrFetchAttributionInfo(globalMainActivity)

                putString("attri_network", tenjinAttribution.adNetwork)
                putString("attri_campain", tenjinAttribution.campaignId)
                putString("attri_campaignname", tenjinAttribution.campaignName)

                debugLog("$TAG eventName:{$eventName}, attri_network:{${tenjinAttribution.adNetwork}}, attri_campain:{${tenjinAttribution.campaignId}}, attri_campaignname:{${tenjinAttribution.campaignName}}")
            })
        }
    }

    private const val KEY_TOTAL_ADS_REVENUE = "${TAG}_KEY_TOTAL_ADS_REVENUE"
    fun tryToRecordTotalAdsRevenue001(
        adFormat: String,
        adValue: Double,
        adNetwork: String,
        adUnitId: String,
    ) {
        val total = mmkv.decodeDouble(KEY_TOTAL_ADS_REVENUE, 0.0)

        val newTotal = total + adValue

        if (newTotal >= 0.01) {
            logEventAdRevenueRecord("Total_Ads_Revenue_001") {
                putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
                putString(FirebaseAnalytics.Param.CURRENCY, "USD")
                putString("adNetwork", adNetwork)
                putString("adFormat", adFormat)
            }

            mmkv.encode(KEY_TOTAL_ADS_REVENUE, 0.0)
        } else {
            mmkv.encode(KEY_TOTAL_ADS_REVENUE, newTotal)
        }
    }

    fun tryToRecordTotalAdsRevenue001(
        adValue: AdValue?,
        adSourceName: String?,
    ) {
        val valueMicros = adValue?.valueMicros ?: return

        val revenue = valueMicros / (1_000_000).toDouble()

        val total = mmkv.decodeDouble(KEY_TOTAL_ADS_REVENUE, 0.0)
        val newTotal = total + revenue

        if (newTotal >= 0.01) {
            logEventAdRevenueRecord("Total_Ads_Revenue_001") {
                putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
                putString(FirebaseAnalytics.Param.CURRENCY, "USD")
                putString("adNetwork", adSourceName ?: "")
                putString("precisionType", adValue.precisionType.toString())
            }

            mmkv.encode(KEY_TOTAL_ADS_REVENUE, 0.0)
        } else {
            mmkv.encode(KEY_TOTAL_ADS_REVENUE, newTotal)
        }
    }

    fun recordAdImpressionRevenue(
        adValue: AdValue?,
        adSourceName: String?,
        adFormat: String,
        adPlacement: String,
    ) {
        val valueMicros = adValue?.valueMicros ?: return

        val revenue = valueMicros / (1_000_000).toDouble()

        logEventAdRevenueRecord("Ad_Impression_Revenue") {
            putDouble(FirebaseAnalytics.Param.VALUE, revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
            putString("adNetwork", adSourceName ?: "")
            putString("precisionType", adValue.precisionType.toString())
        }

    }

    fun recordAdImpression(
        adValue: AdValue?,
        adSourceName: String?,
        adFormat: String,
        adUnit: String,
    ) {
        val valueMicros = adValue?.valueMicros ?: return

        val revenue = valueMicros / (1_000_000).toDouble()

        logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
            putString(FirebaseAnalytics.Param.AD_SOURCE, adSourceName)
            putString(FirebaseAnalytics.Param.AD_FORMAT, adFormat)
            putString(FirebaseAnalytics.Param.AD_UNIT_NAME, adUnit)
            putDouble(FirebaseAnalytics.Param.VALUE, revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
        }
    }

    fun tenjinEventAdImpressionAdMob(adValue: AdValue, ad: Any?) {
        tenjinHelper.roasAdmob(adValue, ad)
    }

//    fun roasReport(
//        adType: String,
//        maxAd: MaxAd
//    ) {
//        tenjinHelper.roasApplovin(maxAd)
//        debugLog("$adType roasReport -> maxAd:$maxAd ")
//    }
//
//    fun roasReportBigoAppOpenAd(
//        ad: SplashAd
//    ) {
//        tenjinHelper.roasBigoAppOpenAd(ad)
//    }
}

fun logEventRecordWithoutAttribution(eventName: String) {
    AnalyticsLogEvent.record(eventName, null)
}

fun logEventRecord(eventName: String) {
    AnalyticsLogEvent.record(eventName)
}

fun logEventRecord(eventName: String, argsBlock: Bundle.() -> Unit) {
    AnalyticsLogEvent.record(eventName, Bundle().apply(argsBlock))
}

fun logEventRecordWithTenjin(eventName: String, tenjinAttribution: TenjinAttribution?) {
    AnalyticsLogEvent.recordWithInjectTenjin(
        eventName = eventName,
        tenjinAttribution = tenjinAttribution
    )
}

fun logEventAdRevenueRecord(eventName: String, argsBlock: Bundle.() -> Unit) {
    AnalyticsLogEvent.revenueRecord(eventName, Bundle().apply(argsBlock))
}
