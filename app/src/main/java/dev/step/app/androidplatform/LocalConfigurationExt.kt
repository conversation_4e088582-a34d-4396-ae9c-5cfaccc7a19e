package dev.step.app.androidplatform

import android.content.res.Configuration
import android.os.Build
import java.util.Locale

fun Configuration.appLocale(): Locale {
    return try {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            locales[0]
        } else {
            locale
        }
    } catch (e: Exception) {
        e.printStackTrace()
        val current = androidx.compose.ui.text.intl.Locale.current
        Locale(current.language, current.region)
    }

}