package dev.step.app.androidplatform.biz.ad.tradplus.nat1ve

import android.content.Context
import com.tradplus.ads.open.TradPlusSdk
import com.tradplus.ads.base.bean.TPAdError
import com.tradplus.ads.base.bean.TPAdInfo
import com.tradplus.ads.open.nativead.NativeAdListener
import com.tradplus.ads.open.nativead.TPNative
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.NativeAdPlace
import dev.step.app.androidplatform.biz.ad.tradplus.TradplusAdUnitIds
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class TradPlusNativeAdManager(
    private val context: Context,
    private val splashController: SplashHelper,
) {
    @Suppress("PrivatePropertyName")
    private val TAG = "TradPlusNativeAdManager"
    private val adUnitNameLowercase = "native"

    private val adKey = TradplusAdUnitIds.NATIVE

    private val adFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<TPNative?>>()
    private val tpNativeMap = mutableMapOf<NativeAdPlace, TPNative?>()

    fun getAdFlow(place: NativeAdPlace): MutableStateFlow<TPNative?> {
        return adFlowMap.getOrPut(place) {
            MutableStateFlow(null)
        }
    }

    fun buildAd(place: NativeAdPlace) {
        debugLog(tag = TAG) { "buildAd for place: ${place.name}" }

        // Check if TradPlus SDK is initialized
        if (!TradPlusSdk.getIsInit()) {
            debugLog(tag = TAG) { "TradPlus SDK not initialized, cannot load ad" }
            return
        }

        // Create TPNative instance
        val tpNative = TPNative(context, adKey)
        tpNativeMap[place] = tpNative

        // Set ad listener
        tpNative.setAdListener(object : NativeAdListener {
            override fun onAdLoaded(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdLoaded for place: ${place.name}" }
                
                GlobalScope.launch(Dispatchers.Main.immediate) {
                    val adFlow = getAdFlow(place)
                    val cachedNative = adFlow.first()
                    
                    // Clean up previous ad
                    cachedNative?.onDestroy()
                    
                    // Update flow with new ad
                    adFlow.update { tpNative }
                }

                logEventRecord("ad_${adUnitNameLowercase}_load_success")
            }

            override fun onAdLoadFailed(error: TPAdError?) {
                debugLog(tag = TAG) { "onAdLoadFailed for place: ${place.name}, error: ${error?.errorMsg}" }
                
                GlobalScope.launch(Dispatchers.Main.immediate) {
                    // Clean up on failure
                    tpNativeMap[place]?.onDestroy()
                    tpNativeMap.remove(place)
                }
                
                logEventRecord("ad_${adUnitNameLowercase}_load_failed")
            }

            override fun onAdClicked(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdClicked for place: ${place.name}" }
                splashController.doSkipSplash(true)
                logEventRecord("ad_${adUnitNameLowercase}_click")
            }

            override fun onAdImpression(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdImpression for place: ${place.name}" }
                logEventRecord("ad_${adUnitNameLowercase}_impress")
                
                // Record revenue analytics
                adInfo?.let { info ->
                    val adSourceName = info.adSourceName
                    val adFormat = "native"
                    val adUnitId = adKey

                    AnalyticsLogEvent.recordAdImpression(
                        null, // adValue not available in TradPlus callback
                        adSourceName,
                        adFormat,
                        adUnitId
                    )
                }
            }

            override fun onAdClosed(adInfo: TPAdInfo?) {
                debugLog(tag = TAG) { "onAdClosed for place: ${place.name}" }
            }
        })

        // Load the ad
        tpNative.loadAd()
        logEventRecord("ad_${adUnitNameLowercase}_load")
    }

    fun destroy(place: NativeAdPlace) {
        debugLog(tag = TAG) { "destroy for place: ${place.name}" }
        
        GlobalScope.launch(Dispatchers.Main.immediate) {
            // Clean up TPNative instance
            tpNativeMap[place]?.onDestroy()
            tpNativeMap.remove(place)
            
            // Clear flow
            adFlowMap[place]?.update { null }
        }
    }

    fun destroyAll() {
        debugLog(tag = TAG) { "destroyAll" }
        
        GlobalScope.launch(Dispatchers.Main.immediate) {
            adFlowMap.keys.forEach(::destroy)
        }
    }
}
