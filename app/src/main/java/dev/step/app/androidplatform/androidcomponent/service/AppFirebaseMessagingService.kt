package dev.step.app.androidplatform.androidcomponent.service

import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import dev.step.app.androidplatform.androidcomponent.receiver.TimeTickReceiver
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant

class AppFirebaseMessagingService: FirebaseMessagingService() {

    sealed class MsgType(val rawValue: String) {
        data object Console : MsgType("epedometer_fcm_console")
        data object Server : MsgType("epedometer_fcm_server")

        companion object {
            fun of(rawValue: String?): MsgType? {
                return when (rawValue) {
                    Console.rawValue -> Console
                    Server.rawValue -> Server
                    else -> null
                }
            }
        }
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
    }

    override fun onMessageReceived(message: RemoteMessage) {
//        super.onMessageReceived(message)
        logEventRecord("receiver_fcm")
        handleMessage(message)
        executeTimeTickTaskIfNeeded()
    }

    private fun handleMessage(message: RemoteMessage) {
        val msgTypeRawValue = message.data["type"]

        when (MsgType.of(msgTypeRawValue)) {
            MsgType.Console -> {}
            MsgType.Server -> handleServerMessage(message)
            null -> {}
        }
    }

    private fun handleServerMessage(message: RemoteMessage) {
        super.onMessageReceived(message)
        logEventRecord("fcm_handle_server_message")
    }

    private var latestExecuteTimeTickTaskSeconds = 0L
    private fun executeTimeTickTaskIfNeeded() {
        val nowSeconds = nowInstant().epochSeconds

        if (nowSeconds - 60 >= latestExecuteTimeTickTaskSeconds) {
            TimeTickReceiver.handleTimeTick()
            latestExecuteTimeTickTaskSeconds = nowSeconds
        }
    }
}