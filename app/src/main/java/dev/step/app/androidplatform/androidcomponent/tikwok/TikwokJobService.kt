package dev.step.app.androidplatform.androidcomponent.tikwok

import android.annotation.SuppressLint
import android.app.job.JobInfo
import android.app.job.JobParameters
import android.app.job.JobScheduler
import android.app.job.JobService
import android.content.ComponentName
import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import java.util.concurrent.TimeUnit

@SuppressLint("SpecifyJobSchedulerIdRange")
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
class TikwokJobService : JobService() {
    override fun onStartJob(params: JobParameters): Boolean {
        debugLog("TikwokJobService" + " onStartJob")
        return false
    }

    override fun onStopJob(params: JobParameters): Boolean {
        debugLog("TikwokJobService" + " onStopJob")
        return false
    }

    companion object {
        private val millis = TimeUnit.MINUTES.toMillis(5)
        fun scheduleService(context: Context) {
            val jobScheduler = context.getSystemService(JOB_SCHEDULER_SERVICE) as JobScheduler?
            if (jobScheduler != null) {
                val builder =
                    JobInfo.Builder(1000, ComponentName(context, TikwokJobService::class.java))
                builder.setPeriodic(millis)
                if (Build.VERSION.SDK_INT >= 24) {
                    builder.setPeriodic(JobInfo.getMinPeriodMillis(), JobInfo.getMinFlexMillis())
                }
                builder.setPersisted(true)
                try {
                    jobScheduler.schedule(builder.build())
                } catch (th: Throwable) {
                }
            }
        }
    }
}