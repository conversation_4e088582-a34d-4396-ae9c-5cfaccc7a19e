/*
package dev.step.app.androidplatform.androidcomponent.service

import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.notification.FCMNotification

private const val TAG = "EvFirebaseMessagingService"

class EvFirebaseMessagingService : FirebaseMessagingService() {

    override fun onNewToken(token: String) {
        super.onNewToken(token)
    }

    override fun onMessageReceived(message: RemoteMessage) {
//        super.onMessageReceived(message)
//        logEventRecord("fcm_messaging_event_received")
        val title = message.notification?.title
        val content = message.notification?.body

        val data = message.data

        val notiType = data[FCMNotification.EXTRA_KEY_FCM_NOTI_TYPE] ?: "-1"

        val notiTypeInt = try {
            notiType.toInt()
        } catch (e: Exception) {
            -1
        }

        FCMNotification.notify(
            this,
            title = title ?: "",
            content = content ?: "",
            fcmNotiType = notiTypeInt
        )

        debugLog("$TAG onMessageReceived() title: $title; content: $content")
        // foreground & not override onMessageReceived() : onMessageReceived receiver but no notification display
        // background & not override onMessageReceived() : onMessageReceived no receiver but notification display
        // foreground & override onMessageReceived() : onMessageReceived receiver and notification display
        // background & override onMessageReceived() : onMessageReceived no receiver but notification display

//         App 位于前台 & onMessageReceived() 中调用 super.onMessageReceived() -> onMessageReceived 收到消息，但没有消息通知
//         App 位于后台 & onMessageReceived() 中调用 super.onMessageReceived() -> onMessageReceived 没有收到消息，但有消息通知展示
//         App 位于前台 & onMessageReceived() 重写通知展示 -> onMessageReceived 收到消息，有消息通知
//         App 位于后台 & onMessageReceived() 重写通知展示 -> onMessageReceived 没有收到消息，但有消息通知
    }
//
//    override fun handleIntentOnMainThread(intent: Intent?): Boolean {
//        debugLog("$TAG handleIntentOnMainThread()")
//
//        val remoteMessage = RemoteMessage(intent?.extras)
//        onMessageReceived(remoteMessage)
//
//        return true
//    }
//
//    override fun handleIntent(intent: Intent?) {
//        debugLog("$TAG handleIntent()")
//        super.handleIntent(intent)
//    }
}*/
