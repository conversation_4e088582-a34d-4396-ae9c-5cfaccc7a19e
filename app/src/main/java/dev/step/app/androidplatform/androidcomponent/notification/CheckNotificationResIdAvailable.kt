package dev.step.app.androidplatform.androidcomponent.notification

import android.content.Context
import android.os.Build
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.global.isResourceIdAvailable

internal fun Context.checkNotificationResIdAvailable(): Bo<PERSON>an {
    return if (Build.VERSION.SDK_INT in Build.VERSION_CODES.M..Build.VERSION_CODES.N) {
        isResourceIdAvailable(R.drawable.ic_round_walk) || isResourceIdAvailable(R.layout.layout_pedometer_notification_fold)
    } else {
        true
    }
}
