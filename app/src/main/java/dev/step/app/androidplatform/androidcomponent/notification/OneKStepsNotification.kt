/*
package dev.step.app.androidplatform.androidcomponent.notification

import android.annotation.SuppressLint
import dev.step.app.R
import dev.step.app.data.pojo.remoteconfig.RepeatNotiMessageGroup
import dev.step.app.destinations.ClickNotiNavToRewardTipsDialogDestination
import dev.step.app.destinations.RewardedDialogDestination
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration


@SuppressLint("StaticFieldLeak")
object OneKStepsNotification : RemoteConfigMessageRepeatNotification() {
    override val TAG: String = "1ks"
    override val NOTI_ID_STARTING_NUMBER = 1000

    override val notiConfig: RepeatNotiMessageGroup?
        get() = remoteConfigHelper.getNoti1kStepsReminder()

    override val notiMessageFirstPushAfterDuration: Duration
        get() = notiConfig?.messageFirstPushAfterOfMinutes?.toDuration(DurationUnit.MINUTES)
            ?: notiConfig?.messageFirstPushAfterOfHours?.toDuration(DurationUnit.HOURS)
            ?: 12.toDuration(DurationUnit.HOURS)

    override val notiRepeatIntervalOfMinutes: Int
        get() = notiConfig?.repeatIntervalOfMinutes ?: 90

    override val notiActionRoutes: List<String>
        get() {
            val targetNavRoute = RewardedDialogDestination(
                from = "custom_notification",
                coins = notiConfig?.coins ?: 1500,
                times = notiConfig?.coinRewardedTimes ?: 4
            ).route

            val wrapperNavRoute = ClickNotiNavToRewardTipsDialogDestination(
                from = "reward2",
                navRoute = targetNavRoute
            ).route

            return listOf(wrapperNavRoute)
        }
    override val notiActionEventRecords = listOf("reward2")

    override val notiActionImageResList: List<Int> = listOf(
        R.drawable.img_noti_coins
    )
}*/
