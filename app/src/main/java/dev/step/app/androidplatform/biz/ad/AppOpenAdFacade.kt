package dev.step.app.androidplatform.biz.ad

import android.app.Activity
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.appopen.AdmobAppOpenAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.appopen.TradPlusSplashAdManager
import org.koin.core.annotation.Single

@Single
class AppOpenAdFacade(
    private val admobManager: AdmobAppOpenAdManager,
    private val tradplusSplashManager: TradPlusSplashAdManager,
    private val remoteConfig: FirebaseRemoteConfigHelper
) {
    companion object {
        private val DEFAULT_PROVIDER = AdProvider.TRADPLUS
    }

    // Cache to avoid frequent remote config reads
    private var cachedProvider: AdProvider? = null

    fun getCurrentProvider(): AdProvider {
        return cachedProvider ?: remoteConfig.getAdProvider().also { cachedProvider = it }
    }

    fun refreshProvider() {
        cachedProvider = null
    }

    fun tryToLoadAd(activity: Activity) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.tryToLoadAd(activity)
            AdProvider.TRADPLUS -> tradplusSplashManager.tryToLoadAd(activity)
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        immediate: Boolean = false
    ) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.tryToShowAd(activity, immediate)
            AdProvider.TRADPLUS -> tradplusSplashManager.tryToShowAd(activity, immediate)
        }
    }

    val adLoadingStateEventFlow: EventFlow<AppOpenAdLoadingStateEvent>
        get() = when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.adLoadingStateEventFlow
            AdProvider.TRADPLUS -> tradplusSplashManager.adLoadingStateEventFlow
        }

    val adShowStateEventFlow: EventFlow<AppOpenAdShowStateEvent>
        get() = when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.adShowStateEventFlow
            AdProvider.TRADPLUS -> tradplusSplashManager.adShowStateEventFlow
        }

    fun onDestroy() {
        // Only TradPlus manager has onDestroy method currently
        when (getCurrentProvider()) {
            AdProvider.TRADPLUS -> tradplusSplashManager.onDestroy()
            AdProvider.ADMOB -> { /* AdmobAppOpenAdManager doesn't have onDestroy */ }
        }
    }
}