@file:Suppress("ObjectPropertyName")

package dev.step.app.androidplatform.biz

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update

class SplashHelper {

    private val _skipSplash = MutableStateFlow(false)
    val skipSplash: StateFlow<Boolean> get() = _skipSplash

    fun doSkipSplash(doSkip: Boolean) {
        _skipSplash.update { doSkip }
    }
}
