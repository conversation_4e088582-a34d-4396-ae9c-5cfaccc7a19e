package dev.step.app.androidplatform.biz.ad

import android.app.Activity
import android.content.Context
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.ad.admob.interstitial.AdmobInterstitialAdManager
import dev.step.app.androidplatform.biz.ad.tradplus.interstitial.TradplusInterstitialAdManager
import kotlinx.coroutines.flow.MutableStateFlow
import org.koin.core.annotation.Single

@Single
class InterstitialAdFacade(
    private val admobManager: AdmobInterstitialAdManager,
    private val tradplusManager: TradplusInterstitialAdManager,
    private val remoteConfig: FirebaseRemoteConfigHelper
) {
    companion object {
        private val DEFAULT_PROVIDER = AdProvider.TRADPLUS
    }

    // Cache to avoid frequent remote config reads
    private var cachedProvider: AdProvider? = null

    fun getCurrentProvider(): AdProvider {
        return cachedProvider ?: remoteConfig.getAdProvider().also { cachedProvider = it }
    }

    fun refreshProvider() {
        cachedProvider = null
    }

    fun tryToLoadAd(activity: Activity) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.tryToLoadAd(activity)
            AdProvider.TRADPLUS -> tradplusManager.tryToLoadAd(activity)
        }
    }

    fun tryToLoadAd(context: Context) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.tryToLoadAd(context as Activity)
            AdProvider.TRADPLUS -> tradplusManager.tryToLoadAd(context)
        }
    }

    suspend fun tryToShowAd(
        activity: Activity,
        adPlaceName: String? = null,
        onReadyShowAd: (() -> Unit)? = null
    ) {
        when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.tryToShowAd(activity, adPlaceName, onReadyShowAd)
            AdProvider.TRADPLUS -> tradplusManager.tryToShowAd(activity, adPlaceName, onReadyShowAd)
        }
    }

    val adLoadingStateEventFlow: EventFlow<*>
        get() = when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.adLoadingStateEventFlow
            AdProvider.TRADPLUS -> tradplusManager.adLoadingStateEventFlow
        }

    val adShowStateEventFlow: EventFlow<*>
        get() = when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.adShowStateEventFlow
            AdProvider.TRADPLUS -> tradplusManager.adShowStateEventFlow
        }

    val adShowStateFlow: MutableStateFlow<Boolean>
        get() = when (getCurrentProvider()) {
            AdProvider.ADMOB -> admobManager.adShowStateFlow
            AdProvider.TRADPLUS -> tradplusManager.adShowStateFlow
        }
}