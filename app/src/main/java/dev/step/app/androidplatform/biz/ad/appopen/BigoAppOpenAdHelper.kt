//package dev.step.app.androidplatform.biz.ad.appopen
//
//import android.content.Context
//import android.view.ViewGroup
//import com.google.firebase.analytics.FirebaseAnalytics
//import dev.step.app.BuildConfig
//import dev.step.app.R
//import dev.step.app.androidplatform.androidcomponent.global.debugLog
//import dev.step.app.androidplatform.biz.SplashHelper
//import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
//import dev.step.app.androidplatform.biz.analytics.logEventAdRevenueRecord
//import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.ext.time.nowInstant
//import dev.step.app.androidplatform.sendBlock
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.GlobalScope
//import kotlinx.coroutines.delay
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.withContext
//import sg.bigo.ads.BigoAdSdk
//import sg.bigo.ads.api.AdConfig
//import sg.bigo.ads.api.AdError
//import sg.bigo.ads.api.AdLoadListener
//import sg.bigo.ads.api.SplashAd
//import sg.bigo.ads.api.SplashAdInteractionListener
//import sg.bigo.ads.api.SplashAdLoader
//import sg.bigo.ads.api.SplashAdRequest
//
//
//private const val TAG = "BigoAppOpenAdHelper"
//
//private const val AdReloadIntervalSeconds = 55 * 60
//
//class BigoAppOpenAdHelper(
//    private val context: Context,
//    private val splashHelper: SplashHelper
//) {
//    private var appOpenAd: SplashAd? = null
//
//    private var latestCacheAdInstantSeconds: Long = 0L
//    private var isLoading = false
//
//    private val adRequest = SplashAdRequest.Builder()
//        .withSlotId(BuildConfig.BIGO_APP_OPEN_ID)
//        .withAppLogo(R.drawable.ic_app_launcher)
//        .withAppName(context.resources.getString(R.string.app_name))
//        .build()
//
//    private val adLoader = SplashAdLoader
//        .Builder()
//        .withAdLoadListener(object : AdLoadListener<SplashAd> {
//            override fun onError(error: AdError) {
//                isLoading = false
//
//                appOpenAd?.destroy()
//                appOpenAd = null
//            }
//
//            override fun onAdLoaded(ad: SplashAd) {
//                isLoading = false
//
//                latestCacheAdInstantSeconds = nowInstant().epochSeconds
//
//                appOpenAd?.destroy()
//                appOpenAd = ad
//                _closeSplashEventFlow.sendBlock {
//                    CloseSplashEvent().apply {
//                        debugLog("loadAd send closeSplashEvent when onAdLoaded(): $this")
//                    }
//                }
//            }
//
//        }).build()
//
//    fun init(context: Context) {
//        val config: AdConfig = AdConfig.Builder()
//            .setAppId(BuildConfig.BIGO_APP_ID)
//            .setDebug(BuildConfig.DEBUG)
//            .build()
//
//        BigoAdSdk.initialize(context, config) {
//
//        }
//    }
//
//    fun loadAd() {
//        isLoading = true
//        adLoader.loadAd(adRequest)
//    }
//
//    fun tryToLoadAd() {
//        debugLog("$TAG tryToLoadAd()")
//
//        GlobalScope.launch(Dispatchers.Main) {
////            _closeSplashEventFlow.update { null } // clean closeSplashEvent, because other event( in onAdLoaded() ) may have been sent before
//
//            val now = nowInstant()
//
//            if (
//                appOpenAd != null
//                && now.epochSeconds - AdReloadIntervalSeconds < latestCacheAdInstantSeconds
//            ) { // has available ad cache
//                debugLog("$TAG tryToLoadAd() has available ad cache")
//
////                delay(600)
//                _closeSplashEventFlow.sendBlock {
//                    CloseSplashEvent().apply {
//                        debugLog("loadAd send closeSplashEvent when has ad cache: $this")
//                    }
//                }
//            } else {
//                debugLog("$TAG tryToLoadAd() isLoadingAd: $isLoading")
//
//                if (!isLoading) {
//                    loadAd()
//                }
//
//                launch {
//                    delay(5000)
//                    if (appOpenAd == null) {
//                        _closeSplashEventFlow.sendBlock {
//                            CloseSplashEvent().apply {
//                                debugLog("loadAd send closeSplashEvent when timeout: $this")
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }
//
//    private fun show(viewContainer: ViewGroup, showFinishedBlock: (() -> Unit)? = null) {
//        appOpenAd?.setAdInteractionListener(object : SplashAdInteractionListener {
//            override fun onAdError(p0: AdError) {
//                debugLog("$TAG onAdError")
//
//                logEventRecord("ad_app_open_error_bigo")
//                appOpenAd = null
//            }
//
//            override fun onAdImpression() {
//                debugLog("$TAG onAdImpression")
//                logEventRecord("ad_app_open_impress_bigo")
//
//                val revenue = appOpenAd?.bid?.price?.let { it / 1000 } ?: 0.0
//                logEventAdRevenueRecord("Ad_Impression_Revenue") {
//                    putDouble(FirebaseAnalytics.Param.VALUE, revenue)
//                    putString(FirebaseAnalytics.Param.CURRENCY, "USD")
//                    putString("adNetwork", "bigo")
//                    putString("adFormat", "APP_OPEN")
//                }
//
//                AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(
//                    adFormat = "APP_OPEN",
//                    adValue = revenue,
//                    adNetwork = "bigo",
//                    adUnitId = BuildConfig.BIGO_APP_OPEN_ID
//                )
//
//                logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
//                    putString(FirebaseAnalytics.Param.AD_PLATFORM, "bigo")
//                    putString(FirebaseAnalytics.Param.AD_SOURCE, "bigo")
//                    putString(FirebaseAnalytics.Param.AD_FORMAT, "APP_OPEN")
//                    putString(FirebaseAnalytics.Param.AD_UNIT_NAME, BuildConfig.BIGO_APP_OPEN_ID)
//                    putDouble(FirebaseAnalytics.Param.VALUE, revenue)
//                    putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
//                }
//
//                appOpenAd?.let {
//                    AnalyticsLogEvent.roasReportBigoAppOpenAd(it)
//                }
//            }
//
//            override fun onAdClicked() {
//                debugLog("$TAG onAdClicked")
//
//                logEventRecord("ad_app_open_click_bigo")
//
//                splashHelper.doSkipSplash(true)
//            }
//
//            override fun onAdSkipped() {
//                debugLog("$TAG onAdSkipped")
//                showFinishedBlock?.invoke()
//
//                logEventRecord("ad_app_open_skip_bigo")
//
//                appOpenAd = null
//                if (!isLoading) {
//                    loadAd()
//                }
//            }
//
//            override fun onAdFinished() {
//                debugLog("$TAG onAdFinished")
//
//                showFinishedBlock?.invoke()
//
//                logEventRecord("ad_app_open_finish_bigo")
//
//                appOpenAd = null
//                if (!isLoading) {
//                    loadAd()
//                }
//            }
//
//            // useless callback
//            override fun onAdOpened() {}
//            override fun onAdClosed() {}
//        })
//
////        appOpenAd?.show()
//
//        debugLog("$TAG show ad")
//        appOpenAd?.showInAdContainer(viewContainer)
//    }
//
//    fun tryToShowAd(
//        container: ViewGroup,
//        tryToShowFinishedBlock: (() -> Unit)? = null
//    ) {
//        debugLog("$TAG tryToShowAd()")
//        GlobalScope.launch {
//            logEventRecord("ad_app_open_show_bigo")
//
//            val now = nowInstant()
//
//            if (
//                appOpenAd != null
//                && now.epochSeconds - AdReloadIntervalSeconds < latestCacheAdInstantSeconds
//            ) {
//                debugLog("$TAG cache available invoke showAppOpenAd()")
//                withContext(Dispatchers.Main) {
//                    show(container, tryToShowFinishedBlock)
//                }
//            } else {
//                withContext(Dispatchers.Main) {
//                    tryToShowFinishedBlock?.invoke()
//                }
//
//                debugLog("$TAG cache unavailable isLoadingAd: $isLoading")
//                if (!isLoading) {
//                    loadAd()
//                }
//            }
//
//        }
//    }
//}