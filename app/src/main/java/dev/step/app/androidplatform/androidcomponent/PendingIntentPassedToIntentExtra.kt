package dev.step.app.androidplatform.androidcomponent

import android.content.Context
import android.content.Intent
import dev.step.app.MainActivity
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

object PendingIntentPassedToIntentExtra {

    private const val TAG = "NotiPendingIntentPassedToIntentExtra"

    private const val CLICK_FROM = "${TAG}_click_from"

    private val handleStartUpFromHomeEnable = MutableStateFlow(true)

    fun createIntent(
        context: Context,
        clickNotiFrom: String
    ) = Intent(context, MainActivity::class.java).apply {
        putExtra(CLICK_FROM, clickNotiFrom)
    }

    fun handleIntent(
        intent: Intent? = globalMainActivity?.intent,
        isColdStart: Boolean,
    ) {
        intent ?: return

        val clickNotiFrom = intent.getStringExtra(CLICK_FROM)

        val startUpTypeText: String = if (isColdStart) {
            ""
        } else {
            "_hot"
        }

        if (!clickNotiFrom.isNullOrEmpty()) {
            debugLog("start_up_from_$clickNotiFrom$startUpTypeText")
            logEventRecord("start_up_from_$clickNotiFrom$startUpTypeText")

            intent.putExtra(CLICK_FROM, "")

            if (!isColdStart) {
                handleStartUpFromHomeEnable.update { false }
            }

        } else {
            GlobalScope.launch(Dispatchers.Main) {
                if (handleStartUpFromHomeEnable.first()) {
                    debugLog("start_up_from_home$startUpTypeText")
                    logEventRecord("start_up_from_home$startUpTypeText")
                } else {
                    handleStartUpFromHomeEnable.emit(true)
                }
            }
        }
    }
}
