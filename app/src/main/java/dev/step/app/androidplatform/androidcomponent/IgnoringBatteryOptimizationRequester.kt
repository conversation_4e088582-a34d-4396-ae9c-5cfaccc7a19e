package dev.step.app.androidplatform.androidcomponent

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.annotation.RequiresApi
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.withId
import kotlinx.datetime.Instant
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val TAG = "IgnoringBatteryOptimizationRequester"

class IgnoringBatteryOptimizationRequester {

    private val mmkv = AppMMKV.withId(TAG)

    fun hasIgnoring(activity: Activity): Boolean? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = activity.getSystemService(Context.POWER_SERVICE) as PowerManager?

            powerManager?.isIgnoringBatteryOptimizations(activity.packageName)
        } else {
            null
        }
    }

    fun canOpenBatteryOptimizationSettings(activity: Activity): Boolean {
        val now = nowInstant()

        return if (
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
            && hasIgnoring(activity) == false
            && now >= latestOpenBatteryOptimizationSettingsInstant() + 12.toDuration(DurationUnit.HOURS)
            && openBatteryOptimizationTimes < REQUEST_LIMIT
        ) {
            true
        } else {
            false
        }
    }

    fun openSystemBatteryOptimizationSettings(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            activity.openBatteryOptimizationSettings()
        }
    }

    fun latestOpenBatteryOptimizationSettingsInstant(): Instant {
        return mmkv.decodeLong(latestOpenBatteryOptimizationSettingsInstantKey, 0L)
            .let(Instant::fromEpochSeconds)
    }

    fun storeOpenBatteryOptimizationSettingsInstant(instant: Instant = nowInstant()) {
        mmkv.encode(latestOpenBatteryOptimizationSettingsInstantKey, instant.epochSeconds)
    }

    var openBatteryOptimizationTimes
        get() = mmkv.decodeInt(openBatteryOptimizationTimesKey, 0)
        set(value) {
            mmkv.encode(openBatteryOptimizationTimesKey, value)
        }

    @RequiresApi(Build.VERSION_CODES.M)
    fun Activity.openBatteryOptimizationSettings(): Boolean {
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager?
        val hasIgnoringBatteryOptimizations =
            powerManager?.isIgnoringBatteryOptimizations(packageName)

        val needToOpenBatteryOptimizationSettings = hasIgnoringBatteryOptimizations == false

        runOnUiThread {
            if (needToOpenBatteryOptimizationSettings) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                intent.data = Uri.parse("package:$packageName")
                startActivityForResult(intent, REQUEST_CODE)
                logEventRecord("battery_permission_request")
                debugLog("battery_permission_request")
            }
        }

        return needToOpenBatteryOptimizationSettings
    }

    companion object {
        private const val REQUEST_LIMIT = 2
        const val REQUEST_CODE = 0x9000

        private const val latestOpenBatteryOptimizationSettingsInstantKey =
            "latestOpenBatteryOptimizationSettingsInstant"

        private const val openBatteryOptimizationTimesKey = "openBatteryOptimizationTimes"

        val ignoringEventFlow = EventFlow<Boolean?>()
    }
}