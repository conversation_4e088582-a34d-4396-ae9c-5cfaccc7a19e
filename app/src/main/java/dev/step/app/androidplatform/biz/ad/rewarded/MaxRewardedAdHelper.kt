//@file:Suppress("ObjectPropertyName")
//
package dev.step.app.androidplatform.biz.ad.rewarded
//
//import android.app.Activity
//import android.content.Context
//import com.applovin.mediation.*
//import com.applovin.mediation.ads.MaxRewardedAd
//import com.google.firebase.analytics.FirebaseAnalytics
//import dev.step.app.BuildConfig
//import dev.step.app.R
import dev.step.app.androidplatform.EventFlow
//import dev.step.app.androidplatform.androidcomponent.global.debugLog
//import dev.step.app.androidplatform.androidcomponent.global.showToast
//import dev.step.app.androidplatform.biz.SplashHelper
//import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
//import dev.step.app.androidplatform.biz.analytics.logEventAdRevenueRecord
//import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.ext.time.nowInstant
//import dev.step.app.androidplatform.send
//import dev.step.app.data.kvstore.UserOperateDataKv
//import kotlinx.coroutines.*
//import kotlinx.coroutines.flow.*
//
//private const val _adReloadIntervalSeconds = 45 * 60
//
//class MaxRewardedAdHelper(
//    private val context: Context,
//    splashAdHelper: SplashHelper,
//    private val userOperateDataKv: UserOperateDataKv,
//) {
//
//    private var isShowAdWithTipsDialog = false
//
//    private var _rewardedAd: MaxRewardedAd? = null
//
//    private val _lastCacheAdInstantSeconds = MutableStateFlow(0L)
//
//    private val isLoadingAdFlow = MutableStateFlow(false)
//
//    private val currentActiveAdFrom = MutableStateFlow("")
//
//    fun hasInit(): Boolean {
//        return _rewardedAd != null
//    }
//
//    private val maxRewardedAdListener = object : MaxRewardedAdListener {
//        override fun onAdLoaded(ad: MaxAd) {
//            debugLog("loadAd endLoad")
//
//            GlobalScope.launch(Dispatchers.Main) {
//                isLoadingAdFlow.update { false }
////                _closeSplashEventFlow.update {
////                    CloseSplashEvent().apply {
////                        debugLog("loadAd send closeSplashEvent when onAdLoaded(): $this")
////                    }
////                }
//                if (isShowAdWithTipsDialog) {
//
//                    debugLog("rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog) onAdLoaded")
//
//                    tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
//                    rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
////                    showRewardedAd()
//                }
//
//                _lastCacheAdInstantSeconds.update { nowInstant().epochSeconds }
//            }
//        }
//
//        override fun onAdDisplayed(ad: MaxAd) {
//            debugLog("loadAd onAdDisplayed")
//
//            showToast(context.getString(R.string.rewarded_ad_tips_content_3))
//
//            GlobalScope.launch(Dispatchers.Main) {
//                splashAdHelper.doSkipSplash(true)
//
//                val adFrom = currentActiveAdFrom.first()
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_incentive_impress")
//                    logEventRecord("ad_${adFrom}_incentive_impress")
//                    logEventRecord("ad_incentive_impress")
//                }
//            }
//        }
//
//        override fun onAdHidden(ad: MaxAd) {
//            debugLog("loadAd onAdHidden")
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = currentActiveAdFrom.first()
//
//                debugLog("ad_${adFrom}_incentive_close")
//                logEventRecord("ad_${adFrom}_incentive_close")
//            }
//            loadRewardedAd()
//
//            if (isShowAdWithTipsDialog) {
//                rewardedLoadingDialogFinishEventFlow.send(Unit)
//                isShowAdWithTipsDialog = false
//            }
//        }
//
//        override fun onAdClicked(ad: MaxAd) {
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = currentActiveAdFrom.first()
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_incentive_click")
//                    logEventRecord("ad_${adFrom}_incentive_click")
//                    logEventRecord("ad_incentive_click")
//                }
//            }
//        }
//
//        override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
//            debugLog("loadAd onAdLoadFailed")
//
//            GlobalScope.launch(Dispatchers.Main) {
//                isLoadingAdFlow.update { false }
//
//                val adFrom = currentActiveAdFrom.first()
//
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_incentive_load_failed")
//                    logEventRecord("ad_${adFrom}_incentive_load_failed")
//                }
//
//                currentActiveAdFrom.update { "" }
//            }
//        }
//
//        override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = currentActiveAdFrom.first()
//
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_incentive_display_failed")
//                    logEventRecord("ad_${adFrom}_incentive_display_failed")
//                }
//            }
//        }
//
//        override fun onUserRewarded(ad: MaxAd, reward: MaxReward) {
//            debugLog("loadAd onUserRewarded")
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = currentActiveAdFrom.first()
//
//                debugLog("ad_${adFrom}_incentive_rewarded")
//                logEventRecord("ad_${adFrom}_incentive_rewarded")
//            }
//        }
//
//        override fun onRewardedVideoStarted(ad: MaxAd) {
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = currentActiveAdFrom.first()
//
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_incentive_rewarded_video_started")
//                    logEventRecord("ad_${adFrom}_incentive_rewarded_video_started")
//                }
//            }
//        }
//
//        override fun onRewardedVideoCompleted(ad: MaxAd) {
//            GlobalScope.launch(Dispatchers.Main) {
//                val adFrom = currentActiveAdFrom.first()
//
//                if (adFrom.isNotEmpty()) {
//                    debugLog("ad_${adFrom}_incentive_rewarded_video_completed")
//                    logEventRecord("ad_${adFrom}_incentive_rewarded_video_completed")
//                }
//            }
//        }
//
//    }
//
//    private val maxRewardedAdRevenueListener = MaxAdRevenueListener { ad ->
//        if (!BuildConfig.DEBUG) {
//            ad?.let {
//                logEventAdRevenueRecord("Ad_Impression_Revenue") {
//                    putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                    putString(FirebaseAnalytics.Param.CURRENCY, "USD")
//                    putString("adNetwork", ad.networkName)
//                    putString("adFormat", ad.format.label)
//                }
//
//                AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(
//                    adFormat = ad.format.label,
//                    adValue = ad.revenue,
//                    adNetwork = ad.networkName,
//                    adUnitId = ad.adUnitId
//                )
//
//                logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
//                    putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
//                    putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
//                    putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
//                    putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
//                    putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                    putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
//                }
//
//                AnalyticsLogEvent.roasReport("rewarded", ad)
//            }
//        }
//    }
//
//    fun initIfNeed(activity: Activity) {
//        if (_rewardedAd == null) {
//            debugLog("loadAd init")
//            _rewardedAd = MaxRewardedAd.getInstance(BuildConfig.MAX_REWARDED_ID, activity).apply {
//                setListener(maxRewardedAdListener)
//                setRevenueListener(maxRewardedAdRevenueListener)
//            }
//        }
//    }
//
//    private fun loadRewardedAd() {
//        _rewardedAd?.let {
//            it.loadAd()
//            debugLog("loadAd startLoad")
//            isLoadingAdFlow.update { true }
//        }
//    }
//
//    fun showRewardedAd() {
//        _rewardedAd?.let {
//            if (it.isReady) {
//                debugLog("loadAd showRewardedAd")
//                it.showAd()
//            }
//        }
//    }
//
//    fun tryToLoadRewardedAd() {
//        GlobalScope.launch(Dispatchers.Main) {
//            debugLog("loadAd tryToLoadRewardedAd")
////            _closeSplashEventFlow.update { null } // clean closeSplashEvent, because other event( in onAdLoaded() ) may have been sent before
//            debugLog("loadAd send closeSplashEvent null, cuz need to clean flow")
//
//            val now = nowInstant()
//
//            debugLog("loadAd tryToLoadRewardedAd _rewardedAd?.isReady: ${_rewardedAd?.isReady}")
//            if (
//                _rewardedAd?.isReady == true
//                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
//            ) { // has available ad cache
//                debugLog("loadAd has available ad cache")
//
////                delay(800)
////                _closeSplashEventFlow.update {
////                    CloseSplashEvent().apply {
////                        debugLog("loadAd send closeSplashEvent when has ad cache: $this")
////                    }
////                }
//            } else {
//                debugLog("loadAd no available ad cache")
//
//                val isLoadingAd = isLoadingAdFlow.first()
//
//                debugLog("loadAd tryToLoadRewardedAd isLoadingAd: $isLoadingAd")
//
//                if (!isLoadingAd) {
//                    loadRewardedAd()
//                }
//
////                launch {
////                    delay(3000)
////                    if (_rewardedAd?.isReady != true) {
////                        _closeSplashEventFlow.update {
////                            CloseSplashEvent().apply {
////                                debugLog("loadAd send closeSplashEvent when timeout: $this")
////                            }
////                        }
////                    }
////                }
//            }
//        }
//    }
//
//    private var tryToShowRewardedAdWithTipsDialogTimeoutJob: Job? = null
//    fun tryToShowRewardedLoadingDialog(
//        from: String? = null,
//        instantlyLoad: Boolean = !userOperateDataKv.tenjinAttr.isOrganic()
//    ) {
//        debugLog("loadAd tryToShowRewardedLoadingDialog from: $from")
//
//        GlobalScope.launch(Dispatchers.Main) {
//            isShowAdWithTipsDialog = true
//
//            debugLog("rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.StartShow)")
//            rewardedLoadingDialogEventFlow.send(
//                RewardedLoadingDialogEvent.StartShow(
//                    instantlyLoad
//                )
//            )
//
//            from?.let {
//                debugLog("ad_${from}_incentive_show")
//                logEventRecord("ad_${from}_incentive_show")
//                logEventRecord("ad_incentive_show")
//                currentActiveAdFrom.emit(from)
//            }
//
//            val now = nowInstant()
//
//            debugLog("loadAd _rewardedAd?.isReady -> ${_rewardedAd?.isReady}")
//            if (
//                (_rewardedAd?.isReady == true)
//                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
//            ) {
//                delay(1000)
//                debugLog("rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)")
//                rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.ShowAdAndDismissDialog)
////                delay(100)
////                showRewardedAd()
//            } else {
//
//                val isLoadingAd = isLoadingAdFlow.first()
//                debugLog("loadAd tryToShowRewardedAd isLoadingAd: $isLoadingAd")
//
//                if (!isLoadingAd) {
//                    loadRewardedAd()
//                }
//
////                debugLog("rewardedAdWithTipsDialogEventFlow.send(RewardedAdWithTipsDialogEvent.Showing)")
////                rewardedAdWithTipsDialogEventFlow.send(RewardedAdWithTipsDialogEvent.Showing)
//
//                tryToShowRewardedAdWithTipsDialogTimeoutJob?.cancel()
//                tryToShowRewardedAdWithTipsDialogTimeoutJob = null
//                tryToShowRewardedAdWithTipsDialogTimeoutJob = launch {
//                    delay(2_0000)
//                    debugLog("rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)")
//                    rewardedLoadingDialogEventFlow.send(RewardedLoadingDialogEvent.LoadingTimeout)
//                }
//            }
//        }
//    }
//}
//
sealed interface RewardedLoadingDialogEvent {
    data class StartShow(val instantlyLoad: Boolean) : RewardedLoadingDialogEvent
    data object Showing : RewardedLoadingDialogEvent
    data object LoadingTimeout : RewardedLoadingDialogEvent
    data object ShowAdAndDismissDialog : RewardedLoadingDialogEvent
}

val rewardedLoadingDialogEventFlow = EventFlow<RewardedLoadingDialogEvent>()
val rewardedLoadingDialogFinishEventFlow = EventFlow<Unit>()