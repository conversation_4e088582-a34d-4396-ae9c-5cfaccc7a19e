package dev.step.app.androidplatform.ext.ktserialization

import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json


@Suppress("ObjectPropertyName")
val _json = Json {
	ignoreUnknownKeys = true
}

inline fun <reified T> String.toObjOrNull(): T? {
	return try {
		_json.decodeFromString<T?>(this)
	} catch (e: Exception) {
		e.printStackTrace()
		null
	}
}

inline fun <reified T> T?.toJsonString(): String = _json.encodeToString(this)
