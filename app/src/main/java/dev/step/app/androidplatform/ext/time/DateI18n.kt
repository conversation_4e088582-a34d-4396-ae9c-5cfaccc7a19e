package dev.step.app.androidplatform.ext.time

import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.Month
import java.time.format.TextStyle
import java.util.Locale

val supportLocales: List<Locale> = listOf(
    Locale("in", "ID"),
    Locale("ja", "JP"),
    Locale("pt", "BR"),
    Locale("ru", "RU"),
    Locale("tr", "TR"),
)

val englishLocale = Locale("en")

fun Month.displayName(
    style: TextStyle = TextStyle.SHORT,
    locale: Locale = Locale.getDefault()
): String {

    val useLocale = if (locale in supportLocales) {
        locale
    } else {
        englishLocale
    }

    return this.getDisplayName(style, useLocale)
}

fun DayOfWeek.displayName(
    style: TextStyle = TextStyle.SHORT,
    locale: Locale = Locale.getDefault()
): String {

    val useLocale = if (locale in supportLocales) {
        locale
    } else {
        englishLocale
    }

    return this.getDisplayName(style, useLocale)
}

fun LocalDateTime.monthAndDayDisplayName(
    style: TextStyle = TextStyle.SHORT,
    locale: Locale = Locale.getDefault()
): String {
    val ldt = this

    return if (locale.language.lowercase() == "ja") {
        ldt.month.displayName(style, locale) + "${ldt.dayOfMonth}日"
    } else {
        "${ldt.dayOfMonth} ${ldt.month.displayName(style, locale)}"
    }
}
