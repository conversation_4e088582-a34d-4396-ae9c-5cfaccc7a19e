package dev.step.app.androidplatform

import dev.step.app.data.adt.MeasurementUnit
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration

fun stepLengthByBodyHeight(cm: Float) = cm * 0.415f

fun stepLengthByBodyHeight(ftIn: Pair<Int, Int>): Pair<Int, Int> {
    val bodyHeightCm = ftInToCm(ftIn)
    val stepLengthCm = stepLengthByBodyHeight(bodyHeightCm)
    return cmToFtIn(stepLengthCm)
}


private const val walkMet = 3.0f
fun calculateKcal(duration: Duration, weightKg: Float): Float {
    val minutes = duration.inWholeMinutes

    return minutes * walkMet * 3.5f * weightKg / 200
}

// ---------------------------------------------------------------------------------------------

fun stepsToDistance(steps: Int, stepLengthCm: Float, mus: MeasurementUnit): Float {
    val distanceKm = (stepLengthCm * steps) / 100f / 1000f

    return when (mus) {
        MeasurementUnit.Metric -> distanceKm
        MeasurementUnit.Imperial -> kmToMile(distanceKm)
    }
}

fun stepsToKcal(steps: Int, weightKg: Float): Float {
    return calculateKcal(stepsToDurationSeconds(steps).toInt().toDuration(DurationUnit.SECONDS), weightKg)
}

const val humanAvgSecondsPerSteps = 3600 / 6000f

fun stepsToDurationSeconds(steps: Int): Float {
    return steps * humanAvgSecondsPerSteps
}
