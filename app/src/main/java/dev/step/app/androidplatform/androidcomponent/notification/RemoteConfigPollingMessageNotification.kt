@file:Suppress("LocalVariableName", "PrivatePropertyName", "PropertyName")

package dev.step.app.androidplatform.androidcomponent.notification

import android.content.Context
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalContext
import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.withId
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.pojo.remoteconfig.PollingMessage
import dev.step.app.data.pojo.remoteconfig.PollingMessageConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

abstract class RemoteConfigPollingMessageNotification : KoinComponent {

    abstract val tag: String
    abstract val notiId: Int

    private val NEXT_NOTI_INSTANT get() = "${tag}_next_noti_instant"
    private val NEXT_NOTI_INDEX get() = "${tag}_next_noti_index"

    protected val mmkv get() = AppMMKV.withId(tag)

    private val noti = HandsUpNotification

    protected val context: Context = globalContext
    protected val remoteConfigHelper: FirebaseRemoteConfigHelper by inject()
    protected val userOperateDataKv: UserOperateDataKv by inject()

    protected abstract val pushStrategyConfig: PollingMessageConfig
    protected abstract val messages: List<PollingMessage>

    private var nextNotiInstant: Instant
        get() = Instant.fromEpochSeconds(
            mmkv.decodeLong(NEXT_NOTI_INSTANT, 0L)
        )
        set(instant) {
            mmkv.encode(NEXT_NOTI_INSTANT, instant.epochSeconds)
        }

    private var nextNotiIndex: Int
        get() = mmkv.decodeInt(NEXT_NOTI_INDEX, 0)
        set(index) {
            mmkv.encode(NEXT_NOTI_INDEX, index)
        }

    private fun useFullScreenIntent(): Boolean {
        return !userOperateDataKv.tenjinAttr.isOrganic()
    }

    fun configureFirstNextNotiInstantIfNeeded(
        instant: Instant = nowInstant(),
    ) {
        if (nextNotiInstant.epochSeconds == 0L) {
            nextNotiInstant =
                instant + pushStrategyConfig.first_push_after_of_minutes.toDuration(DurationUnit.MINUTES)
        }
    }

    open fun tryToShowNotification(
        instant: Instant,
        immediately: Boolean = false,
    ) {
        debugLog("$tag tryToShowNotification")

        if ((nextNotiInstant.epochSeconds != 0L && instant >= nextNotiInstant) || immediately) {
            if (messages.isNotEmpty()) {
                val msg = messages.getOrNull(nextNotiIndex) ?: run {
                    nextNotiIndex = 0
                    messages.first()
                }

                val _nextNotiIndex = nextNotiIndex

                GlobalScope.launch(Dispatchers.Main) {
//                    val useFullScreenIntent = useFullScreenIntent()
                    val useFullScreenIntent = false

//                    val notiId = NOTI_ID_STARTING_NUMBER + _nextNotiIndex
                    val notiId = notiId
                    val title = msg.title
                    val content = msg.content
                    val imageRes = msg.drawableResId

                    noti.notify(
                        context = context,
                        title = title,
                        content = content,
                        channelId = tag,
                        notificationId = notiId,
                        navId = msg.nav_id,
                        imageRes = imageRes,
                        clickEventRecord = ArrayList<String>().apply {
                            add("click_cus_noti_sid${msg.sid}")
                            add("click_cus_noti")
                        },
                        useFullScreenIntent = useFullScreenIntent,
                    )
                }


                logEventRecord("show_cus_noti_sid${msg.sid}")
                logEventRecord("show_cus_noti")

                nextNotiIndex =
                    if (messages.indices.contains(_nextNotiIndex + 1)) {
                        _nextNotiIndex + 1
                    } else {
                        0
                    }


                if (!immediately) {
                    nextNotiInstant =
                        instant + pushStrategyConfig.push_interval_of_minutes
                            .toDuration(DurationUnit.MINUTES)
                }
            }
        }
    }

}
