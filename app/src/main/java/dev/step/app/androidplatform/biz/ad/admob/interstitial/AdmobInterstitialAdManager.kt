package dev.step.app.androidplatform.biz.ad.admob.interstitial

import android.app.Activity
import android.content.Context
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import dev.step.app.androidplatform.EventFlow
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.FullscreenAdManager
import dev.step.app.androidplatform.biz.ad.admob.AdmobAdUnitIds
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L

@Single
class AdmobInterstitialAdManager(
  private val splashController: SplashHelper,
  private val fullscreenAdManager: FullscreenAdManager
) {
  @Suppress("PrivatePropertyName")
  private val TAG = "InterstitialAdManager"

  private val adKey: String = AdmobAdUnitIds.INTERSTITIAL

  private var interstitialAd: InterstitialAd? = null
  private var isLoadingAd = false
  private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

  private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

  sealed interface AdLoadingStateEvent {
    data object TimeOut : AdLoadingStateEvent
    data object Loaded : AdLoadingStateEvent
    data object FailedToLoad : AdLoadingStateEvent
  }

  sealed interface AdShowStateEvent {
    data object Finish : AdShowStateEvent
    data object Showing : AdShowStateEvent
    data object FailedToShow : AdShowStateEvent
    data object SkipToShow : AdShowStateEvent
  }

  val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<AdShowStateEvent>()

  val adShowStateFlow = MutableStateFlow(false)

  private var sendLoadingTimeOutJob: Job? = null

  fun tryToLoadAd(activity: Activity) {
    debugLog(tag = TAG) { "tryToLoadAd" }

    if (isLoadingAd) return

    if (isAdAvailable()) {
      debugLog(tag = TAG) { "hasAdAvailable" }
    } else {
      debugLog(tag = TAG) { "noAdAvailable" }

      loadAd(activity)
    }
  }

  private fun loadAd(context: Context) {
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
    sendLoadingTimeOutJob = GlobalScope.launch(Dispatchers.Default) {
      delay(8_000)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
      adLoadingStateEventFlow.send(AdLoadingStateEvent.TimeOut)
    }

    if (isLoadingAd) {
      debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
      return
    }

    debugLog(tag = TAG) { "loadAd" }

    isLoadingAd = true

    InterstitialAd.load(
      context,
      adKey,
      AdRequest.Builder().build(),
      object : InterstitialAdLoadCallback() {
        override fun onAdLoaded(ad: InterstitialAd) {
          debugLog(tag = TAG) { "onAdLoaded" }

          ad.onPaidEventListener = OnPaidEventListener { adValue ->
            val adSourceName = ad.responseInfo.loadedAdapterResponseInfo?.adSourceName
            val adFormat = "interstitial"
            val adUnitId = ad.adUnitId

            AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
            AnalyticsLogEvent.recordAdImpressionRevenue(
              adValue,
              adSourceName,
              adFormat,
              latestActiveAdPlaceNameFlow.value ?: ""
            )
            AnalyticsLogEvent.recordAdImpression(
              adValue,
              adSourceName,
              adFormat,
              adUnitId
            )

            AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, ad)
          }

          interstitialAd = ad
          isLoadingAd = false
          latestLoadAdSuccessInstant = nowInstant()

          sendLoadingTimeOutJob?.cancel()
          sendLoadingTimeOutJob = null
          adLoadingStateEventFlow.send(AdLoadingStateEvent.Loaded)
          debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

//                    logEventRecord("ad_${adUnitNameLowercase}_load_success")
          logEventRecord("ad_interstitial_load_success")
        }

        override fun onAdFailedToLoad(loadAdError: LoadAdError) {
          debugLog(tag = TAG) { "onAdFailedToLoad" }

          isLoadingAd = false

          sendLoadingTimeOutJob?.cancel()
          sendLoadingTimeOutJob = null
          adLoadingStateEventFlow.send(AdLoadingStateEvent.FailedToLoad)
        }
      }
    )

    logEventRecord("ad_interstitial_load")
  }

  private fun isAdAvailable(): Boolean {
    return interstitialAd != null && checkAdIsValidAtCachePeriod()
  }

  private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
    val secondsDifference: Long =
      nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
    return secondsDifference < adCachePeriodSeconds
  }

  private fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd" }

    logEventRecord("ad_interstitial_show")

    interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
      override fun onAdDismissedFullScreenContent() {
        interstitialAd = null
        loadAd(activity)
        adShowStateEventFlow.send(AdShowStateEvent.Finish)
        GlobalScope.launch {
          delay(2000)
          adShowStateFlow.emit(false)
        }
      }

      override fun onAdFailedToShowFullScreenContent(adError: AdError) {
        debugLog(tag = TAG) { "onAdFailedToShowFullScreenContent" }
        interstitialAd = null
        loadAd(activity)
        adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
        adShowStateFlow.update { false }
      }

      override fun onAdShowedFullScreenContent() {
        fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()
        adShowStateEventFlow.send(AdShowStateEvent.Showing)
        adShowStateFlow.update { true }
        debugLog(tag = TAG) { "onAdShowedFullScreenContent" }
      }

      override fun onAdClicked() {
        splashController.doSkipSplash(true)
        logEventRecord("ad_interstitial_click")
      }

      override fun onAdImpression() {
        logEventRecord("ad_interstitial_impress")
      }
    }

    interstitialAd?.show(activity)
  }

  suspend fun tryToShowAd(
    activity: Activity,
    adPlaceName: String? = null,
    onReadyShowAd: (() -> Unit)? = null
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd" }

    adPlaceName?.let {
      latestActiveAdPlaceNameFlow.update { "inter_$adPlaceName" }
    }

    if (fullscreenAdManager.isAdShowTimeInShowInterval()) {
      debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
      adShowStateEventFlow.send(AdShowStateEvent.SkipToShow)
    } else { // over the show interval, need to show ad
      onReadyShowAd?.invoke()
      debugLog(tag = TAG) { "over the show interval, need to show ad" }
      if (isAdAvailable()) { // cache available
        debugLog(tag = TAG) { "cache available" }
        delay(1_000)
        showAd(activity)
      } else { // cache not available
        debugLog(tag = TAG) { "cache not available" }
        loadAd(activity)
      }
    }
  }
}