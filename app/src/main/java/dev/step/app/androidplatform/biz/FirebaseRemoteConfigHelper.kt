package dev.step.app.androidplatform.biz

import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigValue
import com.google.firebase.remoteconfig.get
import com.google.firebase.remoteconfig.ktx.remoteConfig
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.ktserialization.toObjOrNull
import dev.step.app.androidplatform.ext.withId
import dev.step.app.data.pojo.remoteconfig.*
import dev.step.app.androidplatform.biz.ad.AdProvider
import kotlinx.datetime.Instant

private const val TAG = "FirebaseRemoteConfigHelper"

private const val lastResetInstantSecondsKey = TAG + "_lastResetInstantSecondsKey"

class FirebaseRemoteConfigHelper {

    private val remoteConfig: FirebaseRemoteConfig = Firebase.remoteConfig

    private val mmkv = AppMMKV.withId(TAG)

    fun tryToUpdateConfig(nowInstant: Instant) {
        val lastResetInstantSeconds = mmkv.decodeLong(lastResetInstantSecondsKey, 0)

        if (nowInstant.epochSeconds - lastResetInstantSeconds >= 3600L) { // over 1 hour
//            reset()
            fetchAndActive { isSuccessful ->
                if (isSuccessful == true) {
                    mmkv.encode(lastResetInstantSecondsKey, nowInstant.epochSeconds)
                }
            }
        }
    }

    fun reset() {
        remoteConfig.reset()
    }

    fun fetchAndActive(onCompleteBlock: (isSuccessful: Boolean?) -> Unit) {
        remoteConfig.fetchAndActivate().addOnCompleteListener {
            onCompleteBlock(it.isSuccessful)
        }
    }

    fun getStepExchange(): StepExchange? {
        return remoteConfig["steps_exchange"].toObjOrNull<StepExchange>()
    }

    fun getGame1(): Game1? {
        return remoteConfig["game1"].toObjOrNull<Game1>() ?: Game1.Default
    }

    fun getGame2(): Game2? {
        return remoteConfig["game2"].toObjOrNull<Game2>() ?: Game2.Default
    }

    fun getGame3(): Game3? {
        return remoteConfig["game3"].toObjOrNull<Game3>() ?: Game3.Default
    }

    fun getDailyTask(): DailyTask? {
        return remoteConfig["daily_tasks"].toObjOrNull<DailyTask>()
    }

    fun getOUserWithDrawUS(): OUserWithDraw? {
        return remoteConfig["withdraw_o_user"].toObjOrNull<OUserWithDraw>()
    }

    fun getPUserWithDrawUS(): PUserWithDraw? {
        return remoteConfig["withdraw_p_user"].toObjOrNull<PUserWithDraw>()
    }

    fun getMRecAdRefreshIntervalSecondsInDialog(): Long {
        val key = "native_ad_refresh_interval_seconds_in_dialog"

        val defInterval = 30L

        return remoteConfig[key].asLong().let {
            if (it <= 0L) defInterval else it
        }
    }

    fun getRewardBubble(): RewardedBubbles {
        val key = "rewarded_bubbles"
        return remoteConfig[key].toObjOrNull<RewardedBubbles>() ?: RewardedBubbles.Default
    }

    fun getRewardBubblesRefreshIntervalMinutes(): Long {
        val key = "rewarded_bubbles_refresh_interval_minutes"
        return remoteConfig.getLong(key)
    }

    fun getInterstitialAdInterval(): InterstitialAdIntervalSeconds {
        val key = "interstitial_ad_action_interval_seconds"

        return remoteConfig[key].toObjOrNull<InterstitialAdIntervalSeconds>()
            ?: InterstitialAdIntervalSeconds.Default
    }

    fun redeemPicturePriceByCoins(): Long {
        val key = "redeem_picture_price_by_coins"

        return try {
            remoteConfig[key].asLong().takeIf { it > 0L } ?: 350000
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            350000
        }
    }

    fun monoMessages(
        isOrganic: Boolean,
    ): List<PollingMessage>? {
        val key = if (isOrganic) "mono_messages_for_o" else "mono_messages_for_p"

        return try {
            remoteConfig[key].toObjOrNull<List<PollingMessage>>()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun monoMessagesStrategyConfig(
        isOrganic: Boolean,
    ): PollingMessageConfig? {
        val key = if (isOrganic) "mono_messages_strategy_for_o" else "mono_messages_strategy_for_p"

        return try {
            remoteConfig[key].toObjOrNull<PollingMessageConfig>()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun appLuckSwitch(
        isOrganic: Boolean,
    ): AppLuckSwitch? {
        val key = if (isOrganic) "app_luck_switch_for_o" else "app_luck_switch_for_p"

        return try {
            remoteConfig[key].toObjOrNull<AppLuckSwitch>()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun appLuckUrls(): AppLuckUrls? {
        val key = "app_luck_urls"

        return try {
            remoteConfig[key].toObjOrNull<AppLuckUrls>()
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun getAdProvider(): AdProvider {
        val key = "ad_provider"

        return try {
            val providerString = remoteConfig[key].asString()
            when (providerString.lowercase()) {
                "admob" -> AdProvider.ADMOB
                "tradplus" -> AdProvider.TRADPLUS
                else -> AdProvider.TRADPLUS // default
            }
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            AdProvider.TRADPLUS // default fallback
        }
    }

    private inline fun <reified T> FirebaseRemoteConfigValue.toObjOrNull(): T? {
        return asString().toObjOrNull<T>()
    }
}