package dev.step.app.androidplatform.biz.ad.appopen

import dev.step.app.androidplatform.EventFlow
import kotlinx.coroutines.flow.SharedFlow
import java.util.UUID

data class CloseSplashEvent(val eventId: String = UUID.randomUUID().toString())

@Suppress("ObjectPropertyName")
internal val _closeSplashEventFlow: EventFlow<CloseSplashEvent> = EventFlow()

val closeSplashEventFlow: SharedFlow<CloseSplashEvent> get() = _closeSplashEventFlow