//package dev.step.app.androidplatform.biz.ad.mrec
//
//import android.content.Context
//import android.view.View
//import android.view.View.OnAttachStateChangeListener
//import androidx.core.view.ViewCompat
//import com.applovin.mediation.*
//import com.applovin.mediation.ads.MaxAdView
//import com.google.firebase.analytics.FirebaseAnalytics
//import dev.step.app.BuildConfig
//import dev.step.app.androidplatform.androidcomponent.global.debugLog
//import dev.step.app.androidplatform.biz.SplashHelper
//import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
//import dev.step.app.androidplatform.biz.analytics.logEventAdRevenueRecord
//import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.ext.time.nowInstant
//import kotlinx.coroutines.*
//import kotlinx.coroutines.flow.*
//import kotlinx.datetime.Instant
//import kotlin.collections.getOrPut
//import kotlin.collections.mutableMapOf
//import kotlin.collections.set
//
//
//private const val TAG = "MaxMRecAdHelper"
//
//enum class MRecAdPlaceholder {
//    Dialog,
//
//    HomeScreen,
//    WithdrawScreen,
//    RedeemedScreen,
//}
//
//class MaxMRecAdListener(
//    private val placeholder: MRecAdPlaceholder,
//    private val hasCacheFlow: MutableStateFlow<Boolean>,
//    private val hasPreloadButNoDisplayFlow: MutableStateFlow<Boolean>,
//    private val lastImpressInstantFlow: MutableStateFlow<Instant?>,
//    private val adPlaceNameFlow: StateFlow<String?>,
//    private val splashHelper: SplashHelper,
//) : MaxAdViewAdListener,
//    MaxAdRevenueListener {
//
//    private var adViewAttachedToWindow: Boolean = false
//
//
//    override fun onAdLoaded(ad: MaxAd) {
//        debugLog("$TAG $placeholder onAdLoaded")
//
//        hasCacheFlow.update { true }
//
//        mRecAdMap[placeholder]?.let { maxMRecAd ->
//            if (!ViewCompat.isAttachedToWindow(maxMRecAd.adView)) {
//                adViewAttachedToWindow = false
//
//                debugLog("$TAG $placeholder not attached to window")
//                maxMRecAd.stopAutoRefresh()
//            } else {
//                adViewAttachedToWindow = true
//
//                debugLog("$TAG $placeholder is attached to window")
//            }
//        }
//    }
//
//    override fun onAdDisplayed(ad: MaxAd) {
//        debugLog("$TAG $placeholder onAdDisplayed")
//
//        GlobalScope.launch(Dispatchers.Main) {
//            adPlaceNameFlow.first()?.let { adPlaceName ->
//                debugLog("$TAG $placeholder ad_${adPlaceName}_native_impress")
//                logEventRecord("ad_${adPlaceName}_native_impress")
//                logEventRecord("ad_native_impress")
//
//                lastImpressInstantFlow.update { nowInstant() }
//            }
//
//            debugLog("adViewAttachedToWindow -> $adViewAttachedToWindow")
//            if (!adViewAttachedToWindow) {
//                hasPreloadButNoDisplayFlow.update { true }
//            }
//        }
//    }
//
//    override fun onAdHidden(ad: MaxAd) {
//        debugLog("$TAG $placeholder onAdHidden")
//    }
//
//    override fun onAdClicked(ad: MaxAd) {
//        debugLog("$TAG $placeholder onAdClicked")
//
//        splashHelper.doSkipSplash(true)
//
//        GlobalScope.launch(Dispatchers.Main) {
//            val adPlaceName = adPlaceNameFlow.first() ?: return@launch
//            debugLog("$TAG $placeholder $adPlaceName onAdClicked")
//
//            logEventRecord("ad_${adPlaceName}_native_click")
//            logEventRecord("ad_native_click")
//        }
//    }
//
//    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
//        debugLog("$TAG $placeholder onAdLoadFailed")
//    }
//
//    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
//        debugLog("$TAG $placeholder onAdDisplayFailed")
//    }
//
//    override fun onAdExpanded(ad: MaxAd) {
//        debugLog("$TAG $placeholder onAdExpanded")
//    }
//
//    override fun onAdCollapsed(ad: MaxAd) {
//        debugLog("$TAG $placeholder onAdCollapsed")
//    }
//
//    override fun onAdRevenuePaid(ad: MaxAd) {
//        debugLog("$TAG $placeholder onAdRevenuePaid")
//
//        if (!BuildConfig.DEBUG) {
//            ad?.let {
//                logEventAdRevenueRecord("Ad_Impression_Revenue") {
//                    putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                    putString(FirebaseAnalytics.Param.CURRENCY, "USD")
//                    putString("adNetwork", ad.networkName)
//                    putString("adFormat", ad.format.label)
//                }
//
//                AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(
//                    adFormat = ad.format.label,
//                    adValue = ad.revenue,
//                    adNetwork = ad.networkName,
//                    adUnitId = ad.adUnitId
//                )
//
//                logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
//                    putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
//                    putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
//                    putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
//                    putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
//                    putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
//                    putString(FirebaseAnalytics.Param.CURRENCY, "USD") // All
//                }
//
//                AnalyticsLogEvent.roasReport("native", ad)
//            }
//        }
//    }
//}
//
//private val mRecAdMap = mutableMapOf<MRecAdPlaceholder, MaxMRecAd>()
//
//open class MaxMRecAd(
//    context: Context,
//    private val placeholder: MRecAdPlaceholder,
//    splashHelper: SplashHelper,
//) {
//
//    private val adPlaceNameFlow = MutableStateFlow<String?>(null)
//
//    val hasCacheFlow = MutableStateFlow(false)
//
//    private val hasPreloadButNoDisplayFlow = MutableStateFlow(false)
//
//    private val lastImpressInstantFlow = MutableStateFlow<Instant?>(null)
//
//    private val adListener =
//        MaxMRecAdListener(
//            placeholder,
//            hasCacheFlow,
//            hasPreloadButNoDisplayFlow,
//            lastImpressInstantFlow,
//            adPlaceNameFlow,
//            splashHelper
//        )
//
//    var adView: MaxAdView = MaxAdView(BuildConfig.MAX_MREC_ID, MaxAdFormat.MREC, context).apply {
//        debugLog("$TAG create mrecAd for $placeholder")
//        setListener(adListener)
//        setRevenueListener(adListener)
//
//        addOnAttachStateChangeListener(object : OnAttachStateChangeListener {
//            override fun onViewAttachedToWindow(v: View) {
//                GlobalScope.launch(Dispatchers.Main) {
//                    val placeName = adPlaceNameFlow.first()
//
//                    debugLog("$TAG $placeholder $placeName onViewAttachedToWindow")
//                    <EMAIL>()
//
//                    val hasPreloadButNoDisplay = hasPreloadButNoDisplayFlow.first()
//
//                    if (hasPreloadButNoDisplay && placeName != null) {
//                        logEventRecord("ad_${placeName}_native_impress")
//                        logEventRecord("ad_native_impress")
//                        debugLog("$TAG $placeholder ad_${placeName}_native_impress")
//                        lastImpressInstantFlow.update { nowInstant() }
//                        hasPreloadButNoDisplayFlow.update { false }
//                    }
//                }
//            }
//
//            override fun onViewDetachedFromWindow(view: View) {
//                debugLog("$TAG $placeholder onViewDetachedFromWindow")
//                <EMAIL>()
//            }
//        })
//        loadAd()
//    }
//
//    suspend fun getLastImpressInstant() = lastImpressInstantFlow.first()
//
//    fun configure(placeName: String) {
//        adPlaceNameFlow.update { placeName }
//    }
//
//    fun stopAutoRefresh() {
//        debugLog("$TAG $placeholder stopAutoRefresh")
//
//        adView.apply {
//            setExtraParameter("allow_pause_auto_refresh_immediately", "true")
//            stopAutoRefresh()
//        }
//
//        adPlaceNameFlow.update { null }
//    }
//
//    fun startAutoRefresh() {
//        debugLog("$TAG $placeholder startAutoRefresh")
//        adView.startAutoRefresh()
//    }
//
//    fun loadAd() {
//        debugLog("$TAG $placeholder loadAd")
//        adView.loadAd()
//    }
//
//}
//
//class MaxMRecAdInDialog(
//    context: Context,
//    splashHelper: SplashHelper,
//) : MaxMRecAd(context, MRecAdPlaceholder.Dialog, splashHelper)
//
//class MaxMRecAdHelper(
//    private val splashHelper: SplashHelper,
//) {
//
//    fun initIfNeed(context: Context) {
//        if (mRecAdMap.isNotEmpty()) return
//
////        MRecAdPlaceholder.values().forEach { placeholder ->
////            mRecAdMap[placeholder] = MaxMRecAd(context, placeholder)
////        }
//
//        mRecAdMap[MRecAdPlaceholder.Dialog] =
//            MaxMRecAdInDialog(context, splashHelper)
//        mRecAdMap[MRecAdPlaceholder.HomeScreen] =
//            MaxMRecAd(context, MRecAdPlaceholder.HomeScreen, splashHelper)
//    }
//
//    fun getMRecAd(
//        context: Context,
//        placeholder: MRecAdPlaceholder,
//        placeName: String,
//        splashHelper: SplashHelper,
//    ): MaxMRecAd {
//        return mRecAdMap.getOrPut(placeholder) { MaxMRecAd(context, placeholder, splashHelper) }
//            .apply { configure(placeName) }
//    }
//
//}
