package dev.step.app.androidplatform.biz

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.compose.runtime.Composable
import androidx.core.content.ContextCompat
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.MultiplePermissionsState
import com.google.accompanist.permissions.PermissionState
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.google.accompanist.permissions.rememberPermissionState
import com.tencent.mmkv.MMKV
import dev.step.app.androidplatform.androidcomponent.global.openAppDetailsSettings
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.withId
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.compareTo
import kotlin.time.DurationUnit
import kotlin.time.toDuration


object ActivityRecognitionPermissionRequester : KoinComponent {

    private const val TAG = "ActivityRecognitionPermissionRequester"

    private const val LATEST_OPEN_INSTANT_KEY = "_latestOpenInstantKey"
    private const val REQUEST_LIMIT = 5
    private val REQUEST_INTERVAL_DURATION = 6.toDuration(DurationUnit.HOURS)

    private val mmkv = AppMMKV.withId(TAG)
    private val splashHelper: SplashHelper by inject()

    private var requestCount
        get() = mmkv.decodeInt(TAG + "_requestCount", 0)
        set(value) {
            mmkv.encode(TAG + "_requestCount", value)
        }

    var requestDialogDisplayCount
        get() = mmkv.decodeInt(TAG + "_requestDialogDisplayCount", 0)
        set(value) {
            mmkv.encode(TAG + "_requestDialogDisplayCount", value)
        }

    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    fun permissionState() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            listOf(
                Manifest.permission.FOREGROUND_SERVICE_HEALTH,
                Manifest.permission.ACTIVITY_RECOGNITION,
            )
        } else {
            listOf(
                Manifest.permission.ACTIVITY_RECOGNITION,
            )
        }

        rememberMultiplePermissionsState(permissions)
    } else {
        null
    }

    fun hasPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val activityRecognitionGranted = ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.ACTIVITY_RECOGNITION
            ) == PackageManager.PERMISSION_GRANTED

            val foregroundServiceHealthGranted =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    ContextCompat.checkSelfPermission(
                        context,
                        Manifest.permission.FOREGROUND_SERVICE_HEALTH
                    ) == PackageManager.PERMISSION_GRANTED
                } else {
                    true
                }

            activityRecognitionGranted && foregroundServiceHealthGranted
        } else {
            true
        }
    }


    @OptIn(ExperimentalPermissionsApi::class)
    fun launchPermissionRequest(
        context: Context,
        motionSensorPermissionState: MultiplePermissionsState
    ) {
        if (requestCount < 1) {
            motionSensorPermissionState.launchMultiplePermissionRequest()
        } else {
            splashHelper.doSkipSplash(true)
            context.openAppDetailsSettings()
        }
    }

    @Suppress("RedundantIf")
    fun canOpenRequester(activity: Activity): Boolean {
        val now = nowInstant()

        return if (
            !hasPermission(activity)
            && now >= latestOpenInstant() + REQUEST_INTERVAL_DURATION
            && requestDialogDisplayCount < REQUEST_LIMIT
        ) {
            true
        } else {
            false
        }
    }

    fun latestOpenInstant(): Instant {
        return mmkv.decodeLong(LATEST_OPEN_INSTANT_KEY, 0L)
            .let(Instant::fromEpochSeconds)
    }

    fun storeOpenInstant(instant: Instant = nowInstant()) {
        mmkv.encode(LATEST_OPEN_INSTANT_KEY, instant.epochSeconds)
    }
}