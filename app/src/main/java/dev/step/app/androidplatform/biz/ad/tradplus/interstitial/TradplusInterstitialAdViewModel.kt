package dev.step.app.androidplatform.biz.ad.tradplus.interstitial

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.ViewModel
import dev.step.app.NavigateAction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import org.koin.android.annotation.KoinViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container


@KoinViewModel
class TradplusInterstitialAdViewModel(
  private val interstitialAdManager: TradplusInterstitialAdManager
) : ViewModel(),
  ContainerHost<TradplusInterstitialAdViewState, TradplusInterstitialAdSideEffect> {

  override val container: Container<TradplusInterstitialAdViewState, TradplusInterstitialAdSideEffect> =
    container(TradplusInterstitialAdViewState())

  sealed interface ExecuteInterAd {
    data object Navigate : ExecuteInterAd
    data object OnBack : ExecuteInterAd
  }

  private val executeInterAdFlow = MutableStateFlow<ExecuteInterAd?>(null)

  @SuppressLint("StaticFieldLeak")
  private var showInterAdContainer: Activity? = null
  private var showInterAdAfterNavAction: NavigateAction? = null
  fun onTryToShowInterAdAndNavAction(
    activity: Activity,
    navAction: NavigateAction,
    adPlaceName: String? = null,
  ) = intent {
//    reduce { state.copy(adLoading = true) }

    executeInterAdFlow.update { ExecuteInterAd.Navigate }

    showInterAdContainer = activity
    showInterAdAfterNavAction = navAction

    interstitialAdManager.tryToShowAd(activity, adPlaceName, onReadyShowAd = {
      intent { reduce { state.copy(adLoading = true) } }
    })
  }

  fun onBackAndBeforeTryToShowInterAd(
    activity: Activity,
    adPlaceName: String? = null,
  ) = intent {
//    reduce { state.copy(adLoading = true) }

    executeInterAdFlow.update { ExecuteInterAd.OnBack }

    showInterAdContainer = activity
    showInterAdAfterNavAction = null

    interstitialAdManager.tryToShowAd(activity, adPlaceName, onReadyShowAd = {
      intent { reduce { state.copy(adLoading = true) } }
    })
  }

  fun registerInterAdEventFlow(
    lifecycleScope: CoroutineScope
  ) {
    interstitialAdManager.adLoadingStateEventFlow.onEach {
      when (it) {
        TradplusInterstitialAdManager.AdLoadingStateEvent.TimeOut,
        TradplusInterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
          handleInterAdFinishToDo()
        }

        TradplusInterstitialAdManager.AdLoadingStateEvent.Loaded -> {
          showInterAdContainer?.let { containerActivity ->
            interstitialAdManager.tryToShowAd(containerActivity, null)
          }
        }
      }
    }.launchIn(lifecycleScope)

    interstitialAdManager.adShowStateEventFlow.onEach {
      when (it) {
        TradplusInterstitialAdManager.AdShowStateEvent.FailedToShow,
        TradplusInterstitialAdManager.AdShowStateEvent.Finish,
        TradplusInterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
          handleInterAdFinishToDo()
        }

        TradplusInterstitialAdManager.AdShowStateEvent.Showing -> {
          intent {
            delay(500)
            reduce { state.copy(adLoading = false) }
          }
        }
      }
    }.launchIn(lifecycleScope)
  }

  private fun handleInterAdFinishToDo() = intent {
    reduce { state.copy(adLoading = false) }
    showInterAdContainer = null

    when (executeInterAdFlow.first()) {
      ExecuteInterAd.OnBack -> postSideEffect(TradplusInterstitialAdSideEffect.NavUp)
      ExecuteInterAd.Navigate -> intent {
        showInterAdAfterNavAction?.let { navAction ->
          postSideEffect(TradplusInterstitialAdSideEffect.NavTo(navAction))
        }
        showInterAdAfterNavAction = null
      }

      null -> {}
    }

    executeInterAdFlow.update { null }
  }

}