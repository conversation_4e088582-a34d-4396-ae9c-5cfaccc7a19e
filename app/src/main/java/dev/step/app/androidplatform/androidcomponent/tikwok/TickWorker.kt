package dev.step.app.androidplatform.androidcomponent.tikwok

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters
import dev.step.app.androidplatform.androidcomponent.notification.ActiveStepTrackNotificationHelper
import org.koin.core.component.KoinComponent


class TickWorker(
    context: Context,
    workerParams: WorkerParameters
) : Worker(context, workerParams), KoinComponent {

    private val notiHelper: ActiveStepTrackNotificationHelper = ActiveStepTrackNotificationHelper

    override fun doWork(): Result {
        refreshNotify()
        return Result.success()
    }

    @Synchronized
    fun refreshNotify() {
        //定时任务
        try {
            notiHelper.onStepEventUpdate(false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}