package dev.step.app.androidplatform.androidcomponent.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import dev.step.app.androidplatform.androidcomponent.notification.MonoConfigPollingMessageNotification
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.kvstore.WalletBizKv
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject


object UnlockReceiver : BroadcastReceiver(), KoinComponent {

    private val walletBizKv: WalletBizKv by inject()

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == Intent.ACTION_USER_PRESENT) {
            val instant = nowInstant()

            MonoConfigPollingMessageNotification.tryToShowNotification(instant)

//            val signInTimes = walletBizKv.getSignInTimes()
//            if (signInTimes >= 3) {
//                RewardWhenSignIn3dayNotification.tryToShowNotification(instant, true)
//            }
        }
    }
}

fun registerUnlockReceiver(context: Context) {
    context.registerReceiver(UnlockReceiver, IntentFilter(Intent.ACTION_USER_PRESENT))
}
