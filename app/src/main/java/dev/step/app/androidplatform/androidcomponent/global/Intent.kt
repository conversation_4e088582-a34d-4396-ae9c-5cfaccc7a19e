package dev.step.app.androidplatform.androidcomponent.global

import android.content.Context
import android.content.Intent
import android.content.Intent.*
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.annotation.RequiresApi
//import com.google.firebase.crashlytics.ktx.crashlytics
//import com.google.firebase.ktx.Firebase


fun Context.openBrowser(url: String) {
    runCatching {
        startActivity(
            Intent(ACTION_VIEW, Uri.parse(url))
        )
    }
}

fun Context.openFeedbackMailto(
    email: String,
    subject: String? = null,
    metadata: String? = null
) {
    runCatching {
        startActivity(createFeedbackMailtoIntent(email, subject, metadata))
    }
}

@RequiresApi(Build.VERSION_CODES.O)
fun Context.openNotificationSettings() {
    try {
        startActivity(
            Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                putExtra(
                    Settings.EXTRA_APP_PACKAGE,
                    <EMAIL>
                )
            }
        )
    } catch (e: Exception) {
//        Firebase.crashlytics.recordException(e)
        e.printStackTrace()
    }
}

fun Context.openAppDetailsSettings() {
    try {
        startActivity(
            Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, Uri.fromParts("package", packageName, null)).apply {
                addFlags(FLAG_ACTIVITY_NEW_TASK)
            }
        )
    } catch (e: Exception) {
//        Firebase.crashlytics.recordException(e)
        e.printStackTrace()
    }
}

private fun createFeedbackMailtoIntent(
    email: String,
    subject: String?,
    metadata: String?
): Intent {
    return Intent(ACTION_SEND)
        .putExtra(
            EXTRA_EMAIL,
            arrayOf(email)
        )
        .putExtra(
            EXTRA_SUBJECT,
            subject
        )
        .putExtra(
            EXTRA_TEXT,
            metadata
        )
        .apply {
            selector = Intent(ACTION_SENDTO).setData(Uri.parse("mailto:"))
        }
}
