package dev.step.app.androidplatform.biz

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import dev.step.app.DoGlobalNavigate
import dev.step.app.NotificationPermissionRequesterDialogNode
import dev.step.app.SplashNode
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.openNotificationSettings
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.withId
import dev.step.app.sendGlobalNavigateEvent
import dev.step.app.ui.screen.guidepermissions.GuideActivityRecognitionPermissionNode
import dev.step.app.ui.screen.guidepermissions.GuideNotificationPermissionScreenNode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random
import kotlin.random.nextInt
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val TAG = "NotificationPermissionRequester"

class NotificationPermissionRequester(
    private val splashHelper: SplashHelper
) {

    private val mmkv = AppMMKV.withId(TAG)

    private var requestTimes
        get() = mmkv.decodeInt("requestTimes", 0)
        set(value) {
            mmkv.encode("requestTimes", value)
            Unit
        }

    private var latestRequestSeconds
        get() = mmkv.decodeLong("latestRequestSeconds", 0L)
        set(value) {
            mmkv.encode("latestRequestSeconds", value)
            Unit
        }

    fun requestIfNeeded(
        activity: Activity,
        navigator: Navigator,
    ) {
        debugLog("$TAG registerRequesterIfNeeded")

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return

        val currentNode = navigator.currentKey
        val currentInSplashScreen =
            currentNode is SplashNode
                    || currentNode is GuideActivityRecognitionPermissionNode
                    || currentNode is GuideNotificationPermissionScreenNode

        val currentInNotificationPermissionRequesterDialog =
            currentNode is NotificationPermissionRequesterDialogNode


        val currentInDialog = currentNode?.tag()?.contains("dialog", ignoreCase = true) == true


        debugLog("$TAG registerRequesterIfNeeded currentInSplashScreen:$currentInSplashScreen currentInNotificationPermissionRequesterDialog:$currentInNotificationPermissionRequesterDialog currentInDialog:$currentInDialog")

        if (!currentInSplashScreen
            && !currentInNotificationPermissionRequesterDialog
            && !currentInDialog
        ) {
            tryToRequestIfNeeded(activity)
        }
    }

    fun tryToRequestIfNeeded(
        activity: Activity,
        force: Boolean = false,
    ) {
        debugLog("$TAG tryToRequestIfNeeded requestTimes:$requestTimes")

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return

        if (!force && latestRequestSeconds + 12.toDuration(DurationUnit.HOURS).inWholeSeconds > nowInstant().epochSeconds) return

        when (requestTimes) {
            0, 1 -> {
                showSystemRequester(activity)
            }

            2, 3, 4 -> {
                if (force) {
                    customRequesterOpenToNotificationSettings(activity)
                } else {
                    showCustomRequester(activity)
                }
            }

            else -> {}
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun showSystemRequester(
        activity: Activity
    ) {
        debugLog("$TAG showSystemRequester()")
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                activity,
                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                Random.nextInt(100..200)
            )

            requestCounterIncrement()
        }
    }

    fun showCustomRequester(activity: Activity) {
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            GlobalScope.launch(Dispatchers.Main) {
                sendGlobalNavigateEvent(
                    DoGlobalNavigate.NavNode(
                        NotificationPermissionRequesterDialogNode
                    )
                )

//                requestCounterIncrement()
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun customRequesterOpenToNotificationSettings(
        activity: Activity
    ) {
        debugLog("$TAG customRequesterOpenToNotificationSettings()")
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            splashHelper.doSkipSplash(true)
            activity.openNotificationSettings()
        }
    }

    fun requestCounterIncrement() {
        latestRequestSeconds = nowInstant().epochSeconds
        requestTimes++
    }
}
