/*
package dev.step.app.androidplatform.androidcomponent.notification

import android.annotation.SuppressLint
import dev.step.app.R
import dev.step.app.androidplatform.ext.time.todayStartInstant
import dev.step.app.data.pojo.remoteconfig.RepeatNotiMessageGroup
import dev.step.app.destinations.ClickNotiNavToRewardTipsDialogDestination
import dev.step.app.destinations.RewardedDialogDestination
import kotlinx.datetime.Instant
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@SuppressLint("StaticFieldLeak")
object RewardWhenSignIn3dayNotification : RemoteConfigMessageRepeatNotification() {
    override val TAG: String = "SI3DR"
    override val NOTI_ID_STARTING_NUMBER = 1200

    override val notiConfig: RepeatNotiMessageGroup?
        get() = remoteConfigHelper.getNotiSignIn3DaysReminder()

    override val notiMessageFirstPushAfterDuration: Duration
        get() = notiConfig?.messageFirstPushAfterOfMinutes?.toDuration(DurationUnit.MINUTES)
            ?: notiConfig?.messageFirstPushAfterOfHours?.toDuration(DurationUnit.HOURS)
            ?: 12.toDuration(DurationUnit.HOURS)

    override val notiRepeatIntervalOfMinutes: Int
        get() = notiConfig?.repeatIntervalOfMinutes ?: 90

    override val notiActionRoutes: List<String>
        get() {
            val targetNavRoute = RewardedDialogDestination(
                from = "custom_notification",
                coins = notiConfig?.coins ?: 1500,
                times = notiConfig?.coinRewardedTimes ?: 4
            ).route

            val wrapperNavRoute = ClickNotiNavToRewardTipsDialogDestination(
                from = "reward3",
                navRoute = targetNavRoute
            ).route

            return listOf(wrapperNavRoute)
        }

    override val notiActionEventRecords = listOf("reward3")

    override val notiActionImageResList: List<Int> = listOf(
        R.drawable.img_noti_present_box_blue
    )


    private val KEY_LAST_IMMEDIATELY_NOTI_INSTANT_SECONDS =
        TAG + "_last_immediately_noti_instant_seconds"

    override fun tryToShowNotification(
        instant: Instant,
        immediately: Boolean,
        foldNotiViewCreateBlock: CreateNotiViewBlock?,
        expandNotiViewCreateBlock: CreateNotiViewBlock?
    ) {
        if (immediately) {
            val lastImmediatelyNotiInstantSeconds =
                mmkv?.decodeLong(KEY_LAST_IMMEDIATELY_NOTI_INSTANT_SECONDS) ?: 0
            val lastImmediatelyNotiInstant =
                Instant.fromEpochSeconds(lastImmediatelyNotiInstantSeconds)

            if (instant.todayStartInstant() > lastImmediatelyNotiInstant.todayStartInstant()) {
                super.tryToShowNotification(instant, immediately, null, null)

                mmkv?.encode(KEY_LAST_IMMEDIATELY_NOTI_INSTANT_SECONDS, instant.epochSeconds)
            }


        } else {
            super.tryToShowNotification(instant, immediately, null, null)
        }
    }
}*/
