package dev.step.app.androidplatform.biz.ad

import dev.step.app.androidplatform.biz.FirebaseRemoteConfigHelper
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.withId
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

@Single
class FullscreenAdManager(
  private val remoteConfig: FirebaseRemoteConfigHelper,
  private val userOperateDataKv: UserOperateDataKv,
) {
  companion object {
    private const val TAG = "FullscreenAdManager"
    private const val KEY_LATEST_SHOW_AD_SUCCESS_INSTANT = "latest_show_ad_success_instant"
  }

  private val mmkv by lazy { AppMMKV.withId(TAG) }

  private val adShowIntervalSeconds: Int
    get() {
      return if (userOperateDataKv.tenjinAttr.isOrganic()) {
        remoteConfig.getInterstitialAdInterval().for_o_user
      } else {
        remoteConfig.getInterstitialAdInterval().for_p_user
      }
    }

  var latestShowAdSuccessInstant: Instant
    get() = mmkv.decodeLong(KEY_LATEST_SHOW_AD_SUCCESS_INSTANT, 0L).let(Instant::fromEpochSeconds)
    set(value) {
      mmkv.encode(KEY_LATEST_SHOW_AD_SUCCESS_INSTANT, value.epochSeconds)
    }

  fun isAdShowTimeInShowInterval(): Boolean {
    return nowInstant().epochSeconds - adShowIntervalSeconds < latestShowAdSuccessInstant.epochSeconds
  }
}