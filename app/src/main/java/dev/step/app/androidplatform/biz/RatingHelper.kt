package dev.step.app.androidplatform.biz

import android.app.Activity
import android.content.Context
import com.google.android.play.core.review.ReviewManagerFactory
import dev.step.app.DoGlobalNavigate
import dev.step.app.RatingDialogNode
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.sendGlobalNavigateEvent
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlin.time.DurationUnit
import kotlin.time.toDuration

class RatingHelper(
    private val context: Context,
    private val userOperateDataKv: UserOperateDataKv,
) {

    private val reviewManager by lazy { ReviewManagerFactory.create(context) }

    fun tryToOpenReviewDialog() {
        GlobalScope.launch {
            val openReviewCount = userOperateDataKv.openRatingDialogTimes
            val firstTimeLaunchInstant = userOperateDataKv.firstTimeLaunchAppInstant
                ?: Instant.fromEpochSeconds(0)

            val now = nowInstant()

            debugLog("RatingHelper firstTimeLaunchInstant $firstTimeLaunchInstant")

//            val isOverOneDay = firstTimeLaunchInstant < now.minus(1.toDuration(DurationUnit.DAYS))
            val isFirstDay = firstTimeLaunchInstant < now
            val isOver3days = firstTimeLaunchInstant < now.minus(3.toDuration(DurationUnit.DAYS))
            val isOver7days =
                firstTimeLaunchInstant < now.minus(7.toDuration(DurationUnit.DAYS))

//            debugLog("RatingHelper isOverOneDay $isOverOneDay")
            debugLog("RatingHelper isFirstDay $isFirstDay")
            debugLog("RatingHelper isOverOneWeek $isOver3days")
            debugLog("RatingHelper isOverOneMonth $isOver7days")

            debugLog("RatingHelper openReviewCount $openReviewCount")

            val needToReview = when {
//                openReviewCount == 0 && isOverOneDay -> true
                openReviewCount == 0 && isFirstDay -> true
                openReviewCount == 1 && isOver3days -> true
                openReviewCount == 2 && isOver7days -> true
                else -> false
            }


            if (needToReview) {
                delay(200)

                if (userOperateDataKv.tenjinAttr.isOrganic()) {
                    openGooglePlayInAppReviews(globalMainActivity!!)
                } else {
                    openSelfReviews()
                }

                userOperateDataKv.setOpenRatingDialogTimes(openReviewCount + 1)

                logEventRecord("rate_dialog_show")
            }

        }
    }

    fun openGooglePlayInAppReviews(activity: Activity) {
        reviewManager.requestReviewFlow().addOnCompleteListener { task ->
            debugLog("RatingHelper requestReviewFlow task.isSuccessful: ${task.isSuccessful}")
            runCatching {
                debugLog("RatingHelper requestReviewFlow task.result: ${task.result}")
            }
            if (task.isSuccessful) {
                runCatching {
                    reviewManager.launchReviewFlow(activity, task.result)
                        .addOnCompleteListener {
                            debugLog("RatingHelper launchReviewFlow it.isSuccessful: ${it.isSuccessful}")
                            debugLog("RatingHelper launchReviewFlow it.result: ${it.result}")
                            userOperateDataKv.setOpenRatingDialogTimes(3)

                            logEventRecord("rate_dialog_show_by_gp")
                        }
                }
            }
        }
    }


    private suspend fun openSelfReviews() {
        sendGlobalNavigateEvent(
            DoGlobalNavigate.NavNode(RatingDialogNode)
        )
    }
}