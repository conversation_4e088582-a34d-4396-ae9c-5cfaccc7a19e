package dev.step.app.androidplatform.androidcomponent.notification

import android.annotation.SuppressLint
import dev.step.app.data.pojo.remoteconfig.PollingMessage
import dev.step.app.data.pojo.remoteconfig.PollingMessageConfig

@SuppressLint("StaticFieldLeak")
object MonoConfigPollingMessageNotification : RemoteConfigPollingMessageNotification() {
    override val tag: String = "MCPMN"
    override val notiId: Int = 0x320

    override val pushStrategyConfig: PollingMessageConfig
        get() = remoteConfigHelper.monoMessagesStrategyConfig(userOperateDataKv.tenjinAttr.isOrganic())
            ?: PollingMessageConfig(120,60)
    override val messages: List<PollingMessage>
        get() = remoteConfigHelper.monoMessages(userOperateDataKv.tenjinAttr.isOrganic())
            ?: emptyList()

}