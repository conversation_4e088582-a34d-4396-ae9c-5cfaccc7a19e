package dev.step.app.androidplatform

import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume
import kotlin.time.DurationUnit
import kotlin.time.toDuration

suspend inline fun <T> suspendCoroutineWithTimeout(
    timeoutDuration: kotlin.time.Duration = 5.toDuration(DurationUnit.SECONDS),
    crossinline block: (CancellableContinuation<T>) -> Unit
) = withTimeoutOrNull(timeoutDuration) {
    suspendCancellableCoroutine(block = block)
}

fun <T> CancellableContinuation<T>.resumeIfActive(value: T) {
    if(isActive) {
        resume(value)
    }
}
