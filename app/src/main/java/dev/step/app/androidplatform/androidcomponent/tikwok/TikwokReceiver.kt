package dev.step.app.androidplatform.androidcomponent.tikwok

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.analytics.logEventRecord

class TikwokReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        val action = intent?.action
        debugLog("RegisterReceiver onReceive,action=$action")

        //android.intent.action.BOOT_COMPLETED

        runCatching {
            val shortAction = action
                ?.replace("android.net.conn.", "")
                ?.replace("android.intent.action.", "")

            debugLog("RegisterReceiver onReceive,shortAction=$shortAction")
            logEventRecord("twr_a=$shortAction")
        }.onFailure {
            it.printStackTrace()
        }
    }
}