@file:Suppress("ObjectPropertyName")

package dev.step.app.androidplatform

import androidx.annotation.Keep
import dev.step.app.androidplatform.ext.ktserialization.toObjOrNull
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.todayStartInstant
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import java.net.URL

private const val _TAOBAO_GET_TIMESTAMP_API_URL =
    "https://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp"

@Keep
@Serializable
private data class TaobaoInternetTime(
    val api: String,
    val data: Data,
    val ret: List<String>,
    val v: String
) {
    fun timeMills(): Long = try {
        data.t.toLong()
    } catch (e: Exception) {
        nowInstant().epochSeconds * 1000
    }
}

@Keep
@Serializable
private data class Data(
    val t: String,
)

@Suppress("BlockingMethodInNonBlockingContext")
suspend fun nowNetTimeInstant() = withContext(Dispatchers.IO) {
    try {
        URL(_TAOBAO_GET_TIMESTAMP_API_URL)
            .readText()
            .toObjOrNull<TaobaoInternetTime>()
            ?.timeMills()
            ?.let(Instant::fromEpochMilliseconds)
            ?: nowInstant()
    } catch (e: Exception) {
        nowInstant()
    }
}

private const val userRealNetWorkInstant: Boolean = false

private val nowNetTimeTodayStartInstantFlow = MutableStateFlow<Instant?>(null)
suspend fun nowNetTimeTodayStartInstant(): Instant {
    val instantCache = nowNetTimeTodayStartInstantFlow.first()

    GlobalScope.launch {
        val nowNetTimeInstant = if (userRealNetWorkInstant) nowNetTimeInstant().todayStartInstant() else nowInstant().todayStartInstant()
        nowNetTimeTodayStartInstantFlow.update { nowNetTimeInstant }
    }

    val deviceTodayStartInstant = nowInstant().todayStartInstant()
    return if (instantCache != null && instantCache == deviceTodayStartInstant) {
        instantCache
    } else {
        val nowNetTimeInstant = if (userRealNetWorkInstant) nowNetTimeInstant().todayStartInstant() else nowInstant().todayStartInstant()
        nowNetTimeTodayStartInstantFlow.update { nowNetTimeInstant }
        nowNetTimeInstant
    }
}
