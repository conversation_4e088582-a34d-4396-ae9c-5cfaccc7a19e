package dev.step.app.androidplatform.memorystore

import android.os.Handler
import dev.step.app.androidplatform.androidcomponent.sensor.AccelerometerSensor
import dev.step.app.androidplatform.androidcomponent.sensor.SensorManagerWrapper
import dev.step.app.androidplatform.androidcomponent.sensor.StepDetectorSensor
import dev.step.app.data.db.dao.StepsTrackRecordDao
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single


@Module
class MemoryStoreModule {
    @Single
    fun provideStepTrackMmkvStore() = StepTrackMmkvStore()

    @Single
    fun provideStepTrackingRecorder(
        stepTrackMmkvStore: StepTrackMmkvStore,
        stepsTrackRecordDao: StepsTrackRecordDao,
    ) = StepTrackingRecorder(stepTrackMmkvStore, stepsTrackRecordDao)

    @Single
    fun provideStepTrackingDetectorSession(
        sensorManagerWrapper: SensorManagerWrapper,
        stepDetectorSensor: StepDetector<PERSON>ensor,
        stepTrackingRecorder: StepTrackingRecorder,
        mainThreadHandler: Handler,
    ) = StepTrackingDetectorSession(
        sensorManagerWrapper,
        stepDetectorSensor,
        stepTrackingRecorder,
        mainThreadHandler
    )

    @Single
    fun provideStepTrackingAccelerometerSession(
        sensorManagerWrapper: SensorManagerWrapper,
        accelerometerSensor: AccelerometerSensor,
        stepTrackingRecorder: StepTrackingRecorder,
        mainThreadHandler: Handler,
    ) = StepTrackingAccelerometerSession(
        sensorManagerWrapper,
        accelerometerSensor,
        stepTrackingRecorder,
        mainThreadHandler
    )

    @Single
    fun provideStepTrackingSession(
        stepTrackingDetectorSession: StepTrackingDetectorSession,
        stepTrackingAccelerometerSession: StepTrackingAccelerometerSession,
        stepDetectorSensor: StepDetectorSensor,
        accelerometerSensor: AccelerometerSensor,
    ) = StepTrackingSession(
        stepTrackingDetectorSession,
        stepTrackingAccelerometerSession,
        stepDetectorSensor,
        accelerometerSensor
    )
}
