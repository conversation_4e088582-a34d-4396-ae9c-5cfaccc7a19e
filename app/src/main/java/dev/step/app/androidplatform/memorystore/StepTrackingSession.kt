@file:Suppress("ObjectPropertyName")

package dev.step.app.androidplatform.memorystore

import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Handler
import com.tencent.mmkv.MMKV
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalContext
import dev.step.app.androidplatform.androidcomponent.notification.ActiveStepTrackNotificationHelper
import dev.step.app.androidplatform.androidcomponent.sensor.AccelerometerSensor
import dev.step.app.androidplatform.androidcomponent.sensor.AccelerometerStepDetector
import dev.step.app.androidplatform.androidcomponent.sensor.SensorManagerWrapper
import dev.step.app.androidplatform.androidcomponent.sensor.StepDetectorSensor
import dev.step.app.androidplatform.androidcomponent.sensor.SystemSensorWrapper
import dev.step.app.androidplatform.androidcomponent.service.ActiveStepTrackService
import dev.step.app.androidplatform.biz.ActivityRecognitionPermissionRequester
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.time.lessMinutesAndSeconds
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.withId
import dev.step.app.data.db.dao.StepsTrackRecordDao
import dev.step.app.data.pojo.StepsTrackRecord
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import java.util.*


class StepTrackMmkvStore {
    companion object {
        const val TAG = "step_track_mmkv_store"

        const val LAST_ACTIVE_HOUR_INSTANT_EPOCH_SECONDS =
            TAG + "_last_active_hour_instant_epoch_seconds"
        const val LAST_ACTIVE_HOUR_STEPS = TAG + "_last_active_hour_steps"
    }

    private val mmkv: MMKV = AppMMKV.withId(TAG)

    fun updateLastActiveHourInstantEpochSeconds(epochSeconds: Long) {
        mmkv.encode(LAST_ACTIVE_HOUR_INSTANT_EPOCH_SECONDS, epochSeconds)
    }

    val lastActiveHourInstantEpochSeconds: Long
        get() = mmkv.decodeLong(LAST_ACTIVE_HOUR_INSTANT_EPOCH_SECONDS, 0)

    fun updateLastActiveHourSteps(updateSteps: Int) {
        mmkv.encode(LAST_ACTIVE_HOUR_STEPS, updateSteps)
    }

    val lastActiveHourSteps: Int
        get() = mmkv.decodeInt(LAST_ACTIVE_HOUR_STEPS, 0)
}

class StepTrackingRecorder(
    private val stepTrackMmkvStore: StepTrackMmkvStore,
    private val stepsTrackRecordDao: StepsTrackRecordDao,
) {

    fun record() {
        val now = nowInstant()
        val lastInstantEpochSeconds = stepTrackMmkvStore.lastActiveHourInstantEpochSeconds

        val currentInstantLessMinutesAndSeconds =
            now.lessMinutesAndSeconds(TimeZone.currentSystemDefault())

        when (lastInstantEpochSeconds) {
            0L -> {
                debugLog("record() 1st time update mmkv and store init step to db")

                stepTrackMmkvStore.apply {
                    updateLastActiveHourInstantEpochSeconds(currentInstantLessMinutesAndSeconds.epochSeconds)
                    updateLastActiveHourSteps(0 + 1)
                }

                // every hour 1st step should be store to db :)
                GlobalScope.launch(Dispatchers.Main) {
                    stepsTrackRecordDao.insertOrReplaceRecords(
                        listOf(
                            StepsTrackRecord(
                                instant = Instant.fromEpochSeconds(stepTrackMmkvStore.lastActiveHourInstantEpochSeconds),
                                steps = stepTrackMmkvStore.lastActiveHourSteps
                            ).asStepsTrackRecordEntity()
                        )
                    )
                }
            }

            currentInstantLessMinutesAndSeconds.epochSeconds -> {
                // update mmkv steps data only
                debugLog("record() update mmkv steps data only")

                stepTrackMmkvStore.updateLastActiveHourSteps(stepTrackMmkvStore.lastActiveHourSteps + 1)
            }

            else -> {
                debugLog("record() upsert steps data to db and clear/reset mmkv steps data")

                GlobalScope.launch(Dispatchers.Main) {
                    stepsTrackRecordDao.insertOrReplaceRecords(
                        listOf(
                            StepsTrackRecord(
                                instant = Instant.fromEpochSeconds(lastInstantEpochSeconds),
                                steps = stepTrackMmkvStore.lastActiveHourSteps
                            ).asStepsTrackRecordEntity(),
                            StepsTrackRecord(
                                instant = currentInstantLessMinutesAndSeconds,
                                steps = 1
                            ).asStepsTrackRecordEntity(),
                        )
                    )

                    stepTrackMmkvStore.apply {
                        updateLastActiveHourInstantEpochSeconds(currentInstantLessMinutesAndSeconds.epochSeconds)
                        updateLastActiveHourSteps(0 + 1)
                    }
                }
            }
        }

    }
}

data class StepEvent(
    val eventId: String = UUID.randomUUID().toString(),
    val sensorEvent: SensorEvent? = null
) {
    companion object {
        fun newEvent(sensorEvent: SensorEvent? = null) = StepEvent(sensorEvent = sensorEvent)
    }
}

private val _stepEventFlow = MutableStateFlow<StepEvent?>(null)

private val _activeFlow = MutableStateFlow(false)


class StepTrackingDetectorSession(
    private val sensorManagerWrapper: SensorManagerWrapper,
    private val stepDetectorSensor: StepDetectorSensor,
    private val stepTrackingRecorder: StepTrackingRecorder,
    private val mainThreadHandler: Handler,
) {

    val activeFlow: StateFlow<Boolean> get() = _activeFlow

    private val onStepEventTrack = object : SensorEventListener {
        override fun onSensorChanged(event: SensorEvent?) {
            _stepEventFlow.update {
                StepEvent.newEvent(event)
            }

            event?.let { stepTrackingRecorder.record() }
        }

        override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {

        }

    }

    fun startTracking(isFromBackground: Boolean = false) {

        globalContext.let { context ->
            runCatching {
                if (isFromBackground) throw IllegalStateException("startTrack from background")

//                val intent = (Intent(context, ActiveStepTrackService::class.java))
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                    mainThreadHandler.post {
//                        context.startForegroundService(intent)
//                    }
//                } else {
//                    context.startService(intent)
//                }
                ActiveStepTrackService.startService(context)
            }.onFailure {
                it.printStackTrace()
                ActiveStepTrackNotificationHelper.tryToStartNotification(null)
            }
        }

        stopTracking()

        stepDetectorSensor.sensor?.let { sensor ->
            sensorManagerWrapper.sensorManager?.registerListener(
                onStepEventTrack,
                sensor,
                SensorManager.SENSOR_DELAY_NORMAL
            )
        }
    }

    fun stopTracking() {
        runCatching {
            stepDetectorSensor.sensor?.let { sensor ->
//            _activeFlow.update { false }
                sensorManagerWrapper.sensorManager?.unregisterListener(
                    onStepEventTrack,
                    sensor
                )
            }
        }.onFailure { it.printStackTrace() }
    }
}


class StepTrackingAccelerometerSession(
    private val sensorManagerWrapper: SensorManagerWrapper,
    private val accelerometerSensor: AccelerometerSensor,
    private val stepTrackingRecorder: StepTrackingRecorder,
    private val mainThreadHandler: Handler,
) : AccelerometerStepDetector.StepListener, AccelerometerStepDetector.ActiveListener {

    private var accelerometerActive: Boolean = true

    private val accelerometerStepDetector = AccelerometerStepDetector().apply {
        registerListener(this@StepTrackingAccelerometerSession)
        registerActiveListener(this@StepTrackingAccelerometerSession)
    }

    private val accelerometerEventListener = object : SensorEventListener {
        override fun onSensorChanged(event: SensorEvent?) {
            if (event?.sensor?.type == Sensor.TYPE_ACCELEROMETER) {
                accelerometerStepDetector.updateAccelerometer(
                    event.timestamp,
                    event.values[0],
                    event.values[1],
                    event.values[2]
                )
            }
        }

        override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {

        }
    }

    fun startTracking(isFromBackground: Boolean = false) {
        globalContext.let { context ->
            runCatching {
                if (isFromBackground) throw IllegalStateException("startTrack from background")

//                val intent = (Intent(context, ActiveStepTrackService::class.java))
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                    mainThreadHandler.post {
//                        context.startForegroundService(intent)
//                    }
//                } else {
//                    context.startService(intent)
//                }
                ActiveStepTrackService.startService(context)
            }.onFailure {
                it.printStackTrace()
                ActiveStepTrackNotificationHelper.tryToStartNotification(null)
            }
        }

        stopTracking()

        accelerometerSensor.sensor?.let { sensor ->
            sensorManagerWrapper.sensorManager?.registerListener(
                accelerometerEventListener,
                sensor,
                SensorManager.SENSOR_DELAY_GAME
            )
        }
    }

    fun stopTracking() {
        runCatching {
            accelerometerSensor.sensor?.let { sensor ->
                sensorManagerWrapper.sensorManager?.unregisterListener(
                    accelerometerEventListener,
                    sensor
                )
            }
        }
    }

    override fun onStep() {
        stepTrackingRecorder.record()

        _stepEventFlow.update {
            StepEvent.newEvent(null)
        }
    }

    override fun onAccelerometerActiveChange(isActive: Boolean) {
        accelerometerSensor.sensor ?: return

        if (this.accelerometerActive != isActive && isActive) {
            sensorManagerWrapper.sensorManager?.unregisterListener(
                accelerometerEventListener,
                accelerometerSensor.sensor
            )

            sensorManagerWrapper.sensorManager?.registerListener(
                accelerometerEventListener,
                accelerometerSensor.sensor,
                SensorManager.SENSOR_DELAY_GAME
            )
        } else if (this.accelerometerActive != isActive && !isActive) {
            sensorManagerWrapper.sensorManager?.unregisterListener(
                accelerometerEventListener,
                accelerometerSensor.sensor
            )

            sensorManagerWrapper.sensorManager?.registerListener(
                accelerometerEventListener,
                accelerometerSensor.sensor,
                SensorManager.SENSOR_DELAY_NORMAL
            )
        }

        this.accelerometerActive = isActive
    }

}

class StepTrackingSession(
    private val stepTrackingDetectorSession: StepTrackingDetectorSession,
    private val stepTrackingAccelerometerSession: StepTrackingAccelerometerSession,
    private val stepDetectorSensor: StepDetectorSensor,
    private val accelerometerSensor: AccelerometerSensor,
) {

    val stepEventFlow: StateFlow<StepEvent?> get() = _stepEventFlow

    val currentUseSensor: SystemSensorWrapper?
        get() = when {
            stepDetectorSensor.sensor != null -> stepDetectorSensor
            accelerometerSensor.sensor != null -> accelerometerSensor
            else -> null
        }

    fun startTracking(isFromBackground: Boolean = false) {
        if (ActivityRecognitionPermissionRequester.hasPermission(globalContext).not()) return

        when (currentUseSensor) {
            is StepDetectorSensor -> {
                stepTrackingAccelerometerSession.stopTracking()
                stepTrackingDetectorSession.startTracking(isFromBackground)
            }

            is AccelerometerSensor -> {
                stepTrackingDetectorSession.stopTracking()
                stepTrackingAccelerometerSession.startTracking(isFromBackground)
            }

            else -> {}
        }
    }

    fun stopTracking() {
        stepTrackingDetectorSession.stopTracking()
        stepTrackingAccelerometerSession.stopTracking()
    }
}
