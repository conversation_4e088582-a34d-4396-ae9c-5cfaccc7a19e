package dev.step.app.androidplatform.androidcomponent.notification

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.os.Build
import android.os.Handler
import android.widget.RemoteViews
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.app.NotificationCompat
import dev.step.app.R
import dev.step.app.androidplatform.androidcomponent.PendingIntentPassedToIntentExtra
import dev.step.app.androidplatform.androidcomponent.global.DeviceInfo
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.service.ActiveStepTrackService
import dev.step.app.androidplatform.calculateKcal
import dev.step.app.androidplatform.ext.scale
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.toHHmm
import dev.step.app.androidplatform.humanAvgSecondsPerSteps
import dev.step.app.androidplatform.kmToMile
import dev.step.app.androidplatform.memorystore.StepTrackingSession
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.data.repo.StepsTrackRecordRepo
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@JvmInline
value class NotiManagerWrapper(
    val notificationManager: NotificationManager?
)

data class ActiveStepTrackNotiViewState(
    val mus: MeasurementUnit = MeasurementUnit.Imperial,
    val steps: Int = 0,
    val kcal: Float = 0f,
    val distanceMi: Float = 0f,
    val distanceKm: Float = 0f,
    val duration: Duration = Duration.ZERO,
) {
    companion object {
        val Empty = ActiveStepTrackNotiViewState()
    }
}


private const val ActiveStepTrackServiceId = 0x2
private const val ActiveStepTrackNotificationChannelId = "StepTrackingService"

@SuppressLint("StaticFieldLeak")
object ActiveStepTrackNotificationHelper : KoinComponent{

    private val context: Context by inject()
    private val notiManagerWrapper: NotiManagerWrapper by inject()
    private val stepTrackingSession: StepTrackingSession by inject()
    private val stepsTrackRecordRepo: StepsTrackRecordRepo by inject()
    private val userSettingsDataKv: UserSettingsDataKv by inject()

    private val scope = GlobalScope

    private val notiViewStateFlow =
        MutableStateFlow(ActiveStepTrackNotiViewState.Empty)

    private var notiHasCreated: Boolean = false

    private val clickNotiIntent = PendingIntentPassedToIntentExtra
        .createIntent(context, "fixed_noti")

    private val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
    } else {
        PendingIntent.FLAG_UPDATE_CURRENT
    }

    private val pendingIntent = PendingIntent.getActivity(
        context,
        ActiveStepTrackServiceId,
        clickNotiIntent,
        pendingIntentFlags
    )

    private fun startEmptyNotification(service: ActiveStepTrackService) {
        if (!context.checkNotificationResIdAvailable()) return

        val notificationBuilder =
            NotificationCompat.Builder(
                context,
                ActiveStepTrackNotificationChannelId
            ).apply {
                setSmallIcon(R.drawable.ic_round_walk)
                setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                setContentTitle(service.getString(R.string.app_name))
//                setOngoing(true)
            }

        service.startForeground(ActiveStepTrackServiceId, notificationBuilder.build())
    }

    fun tryToStartNotification(service: ActiveStepTrackService?) {
        val startEmptyNoti =
            Build.VERSION.SDK_INT in Build.VERSION_CODES.O until Build.VERSION_CODES.S

        val stopEmptyNotiForeground: Boolean

        @Suppress("LiftReturnOrAssignment")
        if (service != null && startEmptyNoti) {
            startEmptyNotification(service)
            stopEmptyNotiForeground = true
        } else {
            stopEmptyNotiForeground = false
        }

        if (!notiHasCreated) {

            configureNotiView()

            notiViewStateFlow.onEach {
                notiViewUpdate(it, false, null)
            }.launchIn(scope)

            stepTrackingSession.stepEventFlow.onEach {
                onStepEventUpdate(it != null)
            }.launchIn(scope)

            notiHasCreated = true
        } else {
            onStepEventUpdate(false)
        }

        val nvs = notiViewStateFlow.value

        debugLog("ActiveStepTrackService ready to noti")

        createChannelIfNeeded()
        notiViewUpdate(nvs, stopEmptyNotiForeground, service)
    }

    private fun configureNotiView() {
        scope.launch(Dispatchers.Default) {
            while (true) {
                val now = nowInstant()

                val mus =
                    userSettingsDataKv.measurementUnit ?: MeasurementUnit.Imperial

                val todayStepsData = stepsTrackRecordRepo.todayStepsData(now)

                val stepLengthCm = userSettingsDataKv.stepLengthCm
                val distanceKm = (stepLengthCm * todayStepsData.steps) / 100f / 1000f
                val distanceMi = kmToMile(distanceKm)

                val weightKg = userSettingsDataKv.bodyWeightKg

                val stepsDuration =
                    (todayStepsData.steps * humanAvgSecondsPerSteps).toInt()
                        .toDuration(DurationUnit.SECONDS)
                val kcal = calculateKcal(stepsDuration, weightKg)

                notiViewStateFlow.update {
                    it.copy(
                        mus = mus,
                        steps = todayStepsData.steps,
                        kcal = kcal.scale(3),
                        distanceMi = distanceMi.scale(3),
                        distanceKm = distanceKm.scale(3),
                        duration = stepsDuration,
                    )
                }

                delay(1.toDuration(DurationUnit.HOURS))
            }
        }
    }

    private fun notiViewUpdate(
        notiViewState: ActiveStepTrackNotiViewState,
        stopEmptyNotiForeground: Boolean,
        service: ActiveStepTrackService? = null
    ) {
        if (!context.checkNotificationResIdAvailable()) return

        val isFromService = service != null

        val notiFoldView = createNotiViewFold(notiViewState)
        val notiExpandView =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                createNotiViewExpand(notiViewState)
            else null

        val notificationBuilder =
            NotificationCompat.Builder(
                context,
                ActiveStepTrackNotificationChannelId
            ).apply {
                setSmallIcon(R.drawable.ic_round_walk)
                setCustomContentView(notiFoldView)
                notiExpandView?.let { setCustomBigContentView(notiExpandView) }

                setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                setContentIntent(pendingIntent)
//                setOngoing(true)
            }

        if (isFromService) {
            if (stopEmptyNotiForeground) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    service?.stopForeground(Service.STOP_FOREGROUND_DETACH)
                }
            }

//            notificationBuilder.setOngoing(true)
            service?.startForeground(ActiveStepTrackServiceId, notificationBuilder.build().apply {
                flags = flags or 0x00000002
            })
            debugLog("ActiveStepTrackService service?.startForeground")
        } else {
            notiManagerWrapper.notificationManager?.notify(
                ActiveStepTrackServiceId,
                notificationBuilder.build().apply { flags = flags or 0x00000002 }
            )
        }

    }

    fun onStepEventUpdate(receiverNewStepEvent: Boolean = true) {
        scope.launch(Dispatchers.Default) {
            val state = notiViewStateFlow.first()

            val mus =
                userSettingsDataKv.measurementUnit ?: MeasurementUnit.Imperial

            val stepLengthCm = userSettingsDataKv.stepLengthCm
            val weightKg = userSettingsDataKv.bodyWeightKg

            val steps = state.steps + if (receiverNewStepEvent) 1 else 0
            val stepsDuration =
                (steps * humanAvgSecondsPerSteps).toInt().toDuration(DurationUnit.SECONDS)

            val distanceKm = (stepLengthCm * steps) / 100f / 1000f
            val distanceMi = kmToMile(distanceKm)

            notiViewStateFlow.update {
                it.copy(
                    mus = mus,
                    steps = steps,
                    duration = stepsDuration,
                    distanceKm = distanceKm.scale(3),
                    distanceMi = distanceMi.scale(3),
                    kcal = calculateKcal(stepsDuration, weightKg).scale(3)
                )
            }

//            if (steps == 1000) {
//                launch(Dispatchers.Main) {
//                    OneKStepsNotification.tryToShowNotification(nowInstant(), immediately = true)
//                }
//            }
        }
    }

    private var hasCreateChannel: Boolean = false
    private fun createChannelIfNeeded() {
        if (hasCreateChannel) return

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = notiManagerWrapper.notificationManager

            val serviceChannel = NotificationChannel(
                ActiveStepTrackNotificationChannelId,
                context.getString(R.string.app_name),
                NotificationManager.IMPORTANCE_LOW
            )

            notificationManager?.createNotificationChannel(serviceChannel).apply {
                hasCreateChannel = true
            }
        }
    }

    private fun createNotiViewFold(
        viewState: ActiveStepTrackNotiViewState
    ): RemoteViews {
        return RemoteViews(
            context.packageName,
            R.layout.layout_pedometer_notification_fold
        ).tryToConfigure(viewState).apply {
            if (DeviceInfo.isDarkMode(context)) {
                val textColor = Color.White.toArgb()
                setTextColor(R.id.tv_steps_content, textColor)
                setTextColor(R.id.tv_steps_title, textColor)

                setTextColor(R.id.tv_kcal_content, textColor)
                setTextColor(R.id.tv_kcal_title, textColor)

                setTextColor(R.id.tv_distance_content, textColor)
                setTextColor(R.id.tv_distance_mus, textColor)

                setTextColor(R.id.tv_duration_content, textColor)
                setTextColor(R.id.tv_duration_title, textColor)
            }
        }
    }

    private fun createNotiViewExpand(
        viewState: ActiveStepTrackNotiViewState
    ): RemoteViews {
        return RemoteViews(
            context.packageName,
            R.layout.layout_pedometer_notification_expand
        ).tryToConfigure(viewState)
    }

    private fun RemoteViews.tryToConfigure(viewState: ActiveStepTrackNotiViewState): RemoteViews {
        return this.apply {
            setTextViewText(R.id.tv_steps_content, viewState.steps.toString())
            setTextViewText(R.id.tv_kcal_content, viewState.kcal.toString())
            when (viewState.mus) {
                MeasurementUnit.Imperial -> {
                    setTextViewText(R.id.tv_distance_content, viewState.distanceMi.toString())
                    setTextViewText(R.id.tv_distance_mus, "mi")
                }

                MeasurementUnit.Metric -> {
                    setTextViewText(R.id.tv_distance_content, viewState.distanceKm.toString())
                    setTextViewText(R.id.tv_distance_mus, "km")
                }
            }
            setTextViewText(R.id.tv_duration_content, viewState.duration.toHHmm())
        }
    }
}
