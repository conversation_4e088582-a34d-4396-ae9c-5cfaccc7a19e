package dev.step.app.androidplatform.androidcomponent.sensor

import android.hardware.Sensor
import kotlin.math.abs
import kotlin.math.min
import kotlin.math.sqrt

@JvmInline
value class AccelerometerSensor(val sensor: Sensor?) : SystemSensorWrapper

private object AccelerometerSensorFilter {

    fun sum(array: FloatArray): Float {
        var retval = 0f
        for (i in array.indices) {
            retval += array[i]
        }
        return retval
    }

    fun cross(arrayA: FloatArray, arrayB: FloatArray): FloatArray {
        val retArray = FloatArray(3)
        retArray[0] = arrayA[1] * arrayB[2] - arrayA[2] * arrayB[1]
        retArray[1] = arrayA[2] * arrayB[0] - arrayA[0] * arrayB[2]
        retArray[2] = arrayA[0] * arrayB[1] - arrayA[1] * arrayB[0]
        return retArray
    }

    fun norm(array: FloatArray): Float {
        var retval = 0f
        for (i in array.indices) {
            retval += array[i] * array[i]
        }
        return sqrt(retval.toDouble()).toFloat()
    }


    fun dot(a: FloatArray, b: FloatArray): Float {
        return a[0] * b[0] + a[1] * b[1] + a[2] * b[2]
    }

    fun normalize(a: FloatArray): FloatArray {
        val retval = FloatArray(a.size)
        val norm = norm(a)
        for (i in a.indices) {
            retval[i] = a[i] / norm
        }
        return retval
    }
}

@Suppress("PrivatePropertyName", "LocalVariableName")
class AccelerometerStepDetector {

    private val ACCEL_RING_SIZE = 50
    private val VEL_RING_SIZE = 10

    // change this threshold according to your sensitivity preferences
    private val STEP_THRESHOLD = 25f

    private val STEP_DELAY_NS = 300000000

    private val INVALID_TIMES = 600

    private var accelRingCounter = 0
    private val accelRingX = FloatArray(ACCEL_RING_SIZE)
    private val accelRingY = FloatArray(ACCEL_RING_SIZE)
    private val accelRingZ = FloatArray(ACCEL_RING_SIZE)
    private var velRingCounter = 0
    private val velRing = FloatArray(VEL_RING_SIZE)
    private var lastStepTimeNs: Long = 0
    private var oldVelocityEstimate = 0f

    private var stepListener: StepListener? = null
    private var activeListener: ActiveListener? = null

    fun registerListener(listener: StepListener) {
        this.stepListener = listener
    }

    fun registerActiveListener(listener: ActiveListener) {
        this.activeListener = listener
    }

    private var invalidStepRecord: Int = 0
    fun updateAccelerometer(timeNs: Long, x: Float, y: Float, z: Float) {
        val currentAccel = FloatArray(3)
        currentAccel[0] = x
        currentAccel[1] = y
        currentAccel[2] = z

        // First step is to update our guess of where the global z vector is.
        accelRingCounter++
        accelRingX[accelRingCounter % ACCEL_RING_SIZE] = currentAccel[0]
        accelRingY[accelRingCounter % ACCEL_RING_SIZE] = currentAccel[1]
        accelRingZ[accelRingCounter % ACCEL_RING_SIZE] = currentAccel[2]

        val worldZ = FloatArray(3)
        worldZ[0] =
            AccelerometerSensorFilter.sum(accelRingX) / min(accelRingCounter, ACCEL_RING_SIZE)
        worldZ[1] =
            AccelerometerSensorFilter.sum(accelRingY) / min(accelRingCounter, ACCEL_RING_SIZE)
        worldZ[2] =
            AccelerometerSensorFilter.sum(accelRingZ) / min(accelRingCounter, ACCEL_RING_SIZE)

        val normalization_factor = AccelerometerSensorFilter.norm(worldZ)

        worldZ[0] = worldZ[0] / normalization_factor
        worldZ[1] = worldZ[1] / normalization_factor
        worldZ[2] = worldZ[2] / normalization_factor

        val currentZ = AccelerometerSensorFilter.dot(worldZ, currentAccel) - normalization_factor
        velRingCounter++
        velRing[velRingCounter % VEL_RING_SIZE] = currentZ

        val velocityEstimate = abs(AccelerometerSensorFilter.sum(velRing))

        if (velocityEstimate >= STEP_THRESHOLD
            && oldVelocityEstimate >= STEP_THRESHOLD
            && timeNs - lastStepTimeNs > STEP_DELAY_NS
        ) {
            activeListener?.onAccelerometerActiveChange(true)
            invalidStepRecord = 0

            stepListener?.onStep()
            lastStepTimeNs = timeNs
        } else if (velocityEstimate >= 50) {
            activeListener?.onAccelerometerActiveChange(true)
            invalidStepRecord = 0
        } else if (velocityEstimate <= 10) {
            invalidStepRecord++
            if (invalidStepRecord >= INVALID_TIMES) { // 600 times about 10 seconds
                activeListener?.onAccelerometerActiveChange(false)
            }
        }
        oldVelocityEstimate = velocityEstimate
    }

    fun interface StepListener {
        fun onStep()
    }

    fun interface ActiveListener {
        fun onAccelerometerActiveChange(isActive: Boolean)
    }
}
