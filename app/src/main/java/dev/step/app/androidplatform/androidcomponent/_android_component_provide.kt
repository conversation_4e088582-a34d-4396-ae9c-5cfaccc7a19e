package dev.step.app.androidplatform.androidcomponent

import android.app.NotificationManager
import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import coil.ImageLoader
import coil.decode.GifDecoder
import coil.decode.ImageDecoderDecoder
import dev.step.app.androidplatform.androidcomponent.notification.NotiManagerWrapper
import dev.step.app.androidplatform.androidcomponent.sensor.AccelerometerSensor
import dev.step.app.androidplatform.androidcomponent.sensor.SensorManagerWrapper
import dev.step.app.androidplatform.androidcomponent.sensor.StepDetectorSensor
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single

@Module
class AndroidComponentModule {
    @Single
    fun provideSensorManagerWrapper(context: Context) =
        SensorManagerWrapper(context.getSystemService(Context.SENSOR_SERVICE) as? SensorManager)

    @Single
    fun provideStepDetectorSensor(sensorManagerWrapper: SensorManagerWrapper) =
        StepDetectorSensor(sensorManagerWrapper.sensorManager?.getDefaultSensor(Sensor.TYPE_STEP_DETECTOR))

    @Single
    fun provideAccelerometerSensor(sensorManagerWrapper: SensorManagerWrapper) =
        AccelerometerSensor(sensorManagerWrapper.sensorManager?.getDefaultSensor(Sensor.TYPE_ACCELEROMETER))

    @Single
    fun provideNotificationWrapper(context: Context): NotiManagerWrapper {
        val notificationManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            context.getSystemService(NotificationManager::class.java)
        } else {
            null
        }

        return NotiManagerWrapper(notificationManager)
    }

    @Single
    fun provideHandler() = Handler(Looper.getMainLooper())

    @Single
    fun provideIgnoringBatteryOptimizationRequester() = IgnoringBatteryOptimizationRequester()

    @Single
    fun provideCoilImageLoader(context: Context) = ImageLoader.Builder(context)
        .crossfade(true)
        .crossfade(400)
        .components {
            if (Build.VERSION.SDK_INT >= 28) {
                add(ImageDecoderDecoder.Factory())
            } else {
                add(GifDecoder.Factory())
            }
        }
        .build()
}

