package dev.step.app.androidplatform.biz.ad

enum class NativeAdPlace(val named: String) {
    Test("test"),
    Dialog("dialog"),

    Profile("profile"),
    Wallet("wallet"),
    RedeemCash("redeem_cash"),
    RedeemCoupon("redeem_coupon"),
    RedeemPicture("redeem_pic"),
    ManagePermissions("manage_permi"),
    Withdraw("withdraw"),
}

private val dialogPlace: List<NativeAdPlace> = listOf(NativeAdPlace.Dialog)

fun NativeAdPlace.isDialogPlace(): Boolean {
    return this in dialogPlace
}