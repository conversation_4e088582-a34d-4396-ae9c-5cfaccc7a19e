package dev.step.app.androidplatform.androidcomponent.global

import android.os.Build
import android.widget.Toast
import androidx.core.text.HtmlCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


fun showToast(text: String?) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        showToastApi30(text)
    } else {
        Toast.makeText(globalContext, text, Toast.LENGTH_SHORT).show()
    }
}

private fun showToastApi30(text: String?) {
    GlobalScope.launch(Dispatchers.Main) {
        Toast.makeText(
            globalContext,
            HtmlCompat.fromHtml(
                "<font color='#FF303030'>$text</font>",
                HtmlCompat.FROM_HTML_MODE_LEGACY
            ),
            Toast.LENGTH_SHORT
        ).show()
    }
}
