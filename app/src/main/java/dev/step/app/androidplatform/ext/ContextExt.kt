package dev.step.app.androidplatform.ext

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.statusBars
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.Dp

val Context.statusBarHeight: Dp
    @Composable get() {
        return WindowInsets.statusBars.asPaddingValues().calculateTopPadding()
    }
val Context.navigationBarHeight: Dp
    @Composable get() {
        return WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding()
    }

fun Context.findActivity(): Activity {
    var context = this
    while (context is ContextWrapper) {
        if (context is Activity) return context
        context = context.baseContext
    }
    throw IllegalStateException("no activity")
}
