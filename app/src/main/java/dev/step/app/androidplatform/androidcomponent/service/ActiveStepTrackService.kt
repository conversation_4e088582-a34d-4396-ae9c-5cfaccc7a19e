@file:Suppress("ObjectPropertyName")

package dev.step.app.androidplatform.androidcomponent.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
//import com.google.firebase.crashlytics.ktx.crashlytics
//import com.google.firebase.ktx.Firebase
import dev.step.app.androidplatform.androidcomponent.global.DeviceInfo
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.notification.ActiveStepTrackNotificationHelper

private var _activeTracking: Boolean = false

class ActiveStepTrackService : Service() {

    //    private val notiHelper: ActiveStepTrackNotificationHelper by inject()
    private val notiHelper: ActiveStepTrackNotificationHelper = ActiveStepTrackNotificationHelper

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        debugLog("ActiveStepTrackService onCreate")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {

//        notiHelper.tryToStartEmptyNotification(this)
        debugLog("ActiveStepTrackService onStartCommand")

        notiHelper.tryToStartNotification(this)

        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) START_STICKY else START_NOT_STICKY
    }

    override fun onDestroy() {
        _activeTracking = false
        super.onDestroy()
    }

    companion object {
        private const val TAG = "ActiveStepTrackService"

        private val _mainThreadHandler: Handler = Handler(Looper.getMainLooper())

        fun startService(context: Context) {
            if (_activeTracking) return

            val intent = Intent(context, ActiveStepTrackService::class.java)

            if (DeviceInfo.isSamsungDevice && Build.VERSION.SDK_INT == Build.VERSION_CODES.O_MR1) {
                runCatching {
                    context.startService(intent)
                }.onSuccess {
                    _activeTracking = true
                    debugLog("$TAG samsung android 8.1.0 startService success")
                }.onFailure {
                    debugLog("$TAG samsung android 8.1.0 startService failure")
//                    Firebase.crashlytics.recordException(Exception("samsung android 8.1.0 startService failure"))
                    ActiveStepTrackNotificationHelper.tryToStartNotification(null)
                }
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                _mainThreadHandler.post {
                    runCatching {
                        context.startForegroundService(intent)
                    }.onSuccess {
                        _activeTracking = true
                        debugLog("$TAG startForegroundService success")
                    }.onFailure {
                        debugLog("$TAG startForegroundService failure")
//                        Firebase.crashlytics.recordException(Exception("startForegroundService failure"))
                        ActiveStepTrackNotificationHelper.tryToStartNotification(null)
                    }
                }
            } else {
                runCatching {
                    context.startService(intent)
                }.onSuccess {
                    _activeTracking = true
                    debugLog("$TAG startService success")
                }.onFailure {
                    debugLog("$TAG startService failure")
//                    Firebase.crashlytics.recordException(Exception("startService failure"))
                    ActiveStepTrackNotificationHelper.tryToStartNotification(null)
                }
            }
        }
    }

}
