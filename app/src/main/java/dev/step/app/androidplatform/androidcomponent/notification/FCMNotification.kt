/*
package dev.step.app.androidplatform.androidcomponent.notification

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.moriatsushi.koject.lazyInject
import dev.step.app.*
import dev.step.app.androidplatform.androidcomponent.PendingIntentPassedToIntentExtra
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.destinations.FreeSpinsDestination
import dev.step.app.destinations.HomeDestination
import dev.step.app.destinations.LuckyScratchDestination
import dev.step.app.destinations.SuperWheelDestination
import dev.step.app.destinations.WithdrawDestination
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


object FCMNotification {

    const val EXTRA_KEY_FCM_NOTI_TYPE = "noti_type"

    private const val TAG = "FCMNotification"

    private const val NOTI_ID_STARTING_NUMBER = 10000

    private val notiManagerWrapper: NotiManagerWrapper by lazyInject()

    private val splashHelper: SplashHelper by lazyInject()

    fun notify(
        context: Context,
        title: String,
        content: String,
//        channelId: String,
        fcmNotiType: Int,
    ) {
        if (!context.checkNotificationResIdAvailable()) return

        debugLog("$TAG notify() fcmNotiType: $fcmNotiType")

        val notificationId = NOTI_ID_STARTING_NUMBER + fcmNotiType
        val channelId = TAG

        val navIntent = PendingIntentPassedToIntentExtra
            .createIntent(context, "notification_firebase")
            .apply {
                putExtra(EXTRA_KEY_FCM_NOTI_TYPE, fcmNotiType.toString())
            }

        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            notificationId,
            navIntent,
            pendingIntentFlags
        )

        val notificationBuilder = NotificationCompat.Builder(
            context, channelId
        ).apply {
            setSmallIcon(R.drawable.ic_round_walk)
            setContentTitle(title)
            setContentText(content)
            setContentIntent(pendingIntent)
        }

        createNotificationChannel(context, channelId)

        notiManagerWrapper.notificationManager?.notify(notificationId, notificationBuilder.build())
    }

    fun handleIntent(
        intent: Intent? = globalMainActivity?.intent,
        isActivityOnStart: Boolean = false
    ) {
        var fcmNotiType = -1
        runCatching {
            fcmNotiType = intent?.extras?.getString(EXTRA_KEY_FCM_NOTI_TYPE)?.toIntOrNull() ?: -1
        }

        debugLog("$TAG handleIntent() fcmNotiType: $fcmNotiType")

        if (fcmNotiType == -1) {
            notiManagerWrapper.notificationManager?.cancel(NOTI_ID_STARTING_NUMBER + fcmNotiType)
            return
        }

        val route = when (fcmNotiType) {
            1 -> HomeDestination(tabIndex = 3) // reports
            2 -> HomeDestination(tabIndex = 1) // steps
            3 -> SuperWheelDestination(navFromNotification = true) // super wheel
            4 -> FreeSpinsDestination(navFromNotification = true) // free spins
            5 -> LuckyScratchDestination // lucky scratch
            6 -> WithdrawDestination // withdraw
            7 -> HomeDestination(tabIndex = 2) // wallet
            else -> HomeDestination(tabIndex = 1) // steps
        }.route

        if (isActivityOnStart) {
            splashHelper.doSkipSplash(true)
        }

        notiManagerWrapper.notificationManager?.cancel(NOTI_ID_STARTING_NUMBER + fcmNotiType)

        GlobalScope.launch(Dispatchers.Main) {
            if (route.contains("home?tabIndex=", true)) {
                sendGlobalNavigateEvent(DoGlobalNavigate.NavBlock {
                    popBackStack(route, false)
                    popBackStack()
                    navigate(route)
                })
            } else {
                sendGlobalNavigateEvent(DoGlobalNavigate.NavRoute(route))
            }
        }
    }

    private fun createNotificationChannel(
        context: Context,
        channelId: String
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = notiManagerWrapper.notificationManager

            val serviceChannel = NotificationChannel(
                channelId,
                context.getString(R.string.app_name),
                NotificationManager.IMPORTANCE_HIGH
            )

            notificationManager?.createNotificationChannel(serviceChannel)
        }
    }
}*/
