package dev.step.app.androidplatform.androidcomponent.notification

import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.View
import android.widget.RemoteViews
import androidx.annotation.DrawableRes
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.app.NotificationCompat
import com.roudikk.guia.extensions.popToRoot
import com.roudikk.guia.extensions.replaceLast
import dev.step.app.*
import dev.step.app.androidplatform.androidcomponent.PendingIntentPassedToIntentExtra
import dev.step.app.androidplatform.androidcomponent.global.DeviceInfo
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.global.globalContext
import dev.step.app.androidplatform.androidcomponent.global.globalMainActivity
import dev.step.app.androidplatform.androidcomponent.receiver.HandleRemoteViewsEventReceiver
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.data.kvstore.UserOperateDataKv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject


private const val EXTRA_KEY_NOTI_NAV_ID = "extra_key_noti_nav_destination"
private const val EXTRA_KEY_NOTI_ID = "extra_key_noti_id"
private const val EXTRA_KEY_CLICK_FOR_EVENT_RECORDS = "extra_key_click_for_event_records"

@SuppressLint("StaticFieldLeak")
object HandsUpNotification : KoinComponent {

    private val context: Context = globalContext

    private val notiManagerWrapper: NotiManagerWrapper by inject()

    private val splashHelper: SplashHelper by inject()
    private val userOperateDataKv: UserOperateDataKv by inject()

    @Suppress("LocalVariableName")
    fun notify(
        context: Context,
        title: String,
        content: String,
        channelId: String,
        notificationId: Int,
        navId: Int,
        @DrawableRes imageRes: Int,
        clickEventRecord: ArrayList<String>?,
        useFullScreenIntent: Boolean = false,
        notiExpandView: RemoteViews? = null,
    ) {
        if (!context.checkNotificationResIdAvailable()) return

        val navIntent = PendingIntentPassedToIntentExtra
            .createIntent(context, "cus_noti")
            .apply {
                putExtra(EXTRA_KEY_NOTI_NAV_ID, navId)
                putExtra(EXTRA_KEY_NOTI_ID, notificationId)
                clickEventRecord?.let {
                    putExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORDS, clickEventRecord)
                }
            }

        debugLog("navIntent send notiId ${navIntent.getIntExtra(EXTRA_KEY_NOTI_ID, -100)}")

        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }

        val fullScreenPendingIntent = if (useFullScreenIntent) {
            PendingIntent.getActivity(
                context,
                notificationId,
                Intent(),
                pendingIntentFlags
            )
        } else null


        val clickNotiPendingIntent = PendingIntent.getActivity(
            context,
            notificationId,
            navIntent,
            pendingIntentFlags
        )

        val isApiGreaterThanOrEqual31 = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

        val _notiFoldView = createNotiView(
            notificationId = notificationId,
            title = title,
            content = content,
            imageRes = imageRes,
            isExpand = false
        )
        var _notiExpandView: RemoteViews? = notiExpandView

        if (_notiExpandView == null) { // not invoke notiExpandView, try to create default style noti
            if (isApiGreaterThanOrEqual31) {
                _notiExpandView = createNotiView(
                    notificationId = notificationId,
                    title = title,
                    content = content,
                    imageRes = imageRes,
                    isExpand = true
                )
            } else {
//                val closeNotiIntent =
//                    HandleRemoteViewsEventReceiver.CloseNotification.createIntent(notificationId)
//
//                val closeNotiPendingIntent = PendingIntent
//                    .getBroadcast(context, notificationId, closeNotiIntent, pendingIntentFlags)
//
//                _notiFoldView.setOnClickPendingIntent(R.id.iv_noti_close, closeNotiPendingIntent)
            }
        } else {
            // if notiExpandView from function invoke, do not configure anything
        }


        val notificationBuilder = NotificationCompat.Builder(
            context, channelId
        ).apply {
            setSmallIcon(R.drawable.ic_round_walk)
//            setContentTitle(title)
//            setContentText(content)
            _notiExpandView?.let { setCustomBigContentView(_notiExpandView) }
            setCustomContentView(_notiFoldView)
            setCustomHeadsUpContentView(_notiFoldView)
            setContentIntent(clickNotiPendingIntent)
            fullScreenPendingIntent?.let {
                setFullScreenIntent(it, true)
            }

            priority = NotificationCompat.PRIORITY_HIGH
        }

        createNotificationChannel(context, channelId)

        notiManagerWrapper.notificationManager?.notify(notificationId, notificationBuilder.build())

        debugLog("Notification notify()")
    }


    fun handleNotificationIntent(
        intent: Intent? = globalMainActivity?.intent,
        isActivityOnStart: Boolean = false
    ) {
        val navId = intent?.getIntExtra(EXTRA_KEY_NOTI_NAV_ID, -1)
        val notiId = intent?.getIntExtra(EXTRA_KEY_NOTI_ID, -1) ?: -1
        val clickEventRecords = intent?.getStringArrayListExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORDS)
        if (navId == null || notiId == -1) {
            splashHelper.doSkipSplash(false)
            return
        }

        if (isActivityOnStart) {
            splashHelper.doSkipSplash(true)
        }

        GlobalScope.launch(Dispatchers.Main) {

            debugLog("navIntent nav to route: $navId")
            debugLog("navIntent receiver notiId $notiId")

            notiManagerWrapper.notificationManager?.cancel(notiId)

            if (!clickEventRecords.isNullOrEmpty()) {
                clickEventRecords.forEach { event ->
                    event?.let { _ -> logEventRecord(event) }
                }

//                @Suppress("LocalVariableName")
//                val _1stEventRecord = clickEventRecords.getOrNull(0)
//                if (_1stEventRecord != null) {
//                    EarnCoinsRepeatNotification.notiActionEventRecords.forEach { earnCoinEventRecordSuffix ->
//                        if (_1stEventRecord.endsWith(earnCoinEventRecordSuffix)) {
//                            clickEarnCoinNotiEventFlow.send(Unit)
//                        }
//                    }
//                }
            }

//            if (destinationSid.contains("home?tabIndex=", true)) {
//                sendGlobalNavigateEvent(DoGlobalNavigate.NavBlock {
//                    popBackStack(destinationSid, false)
//                    popBackStack()
//                    navigate(destinationSid)
//                })
//            } else {
//                sendGlobalNavigateEvent(DoGlobalNavigate.NavRoute(destinationSid))
//            }

            val isOrganic = userOperateDataKv.tenjinAttr.isOrganic()
            val navDestination = NavActionDestination
                .destinationNodeFromNavId(
                    navId = navId,
                    useWithdraw = !isOrganic
                )

            if (navDestination is HomeNode) {
                sendGlobalNavigateEvent(DoGlobalNavigate.NavBlock {
                    popToRoot()
                    replaceLast(navDestination)
                })
            } else {
                sendGlobalNavigateEvent(DoGlobalNavigate.NavNode(navDestination))
            }

            intent.putExtra(EXTRA_KEY_NOTI_NAV_ID, "")
            intent.putExtra(EXTRA_KEY_NOTI_ID, -1)

            debugLog(
                "navIntent receiver & handle finish notiId ${
                    intent.getIntExtra(
                        EXTRA_KEY_NOTI_ID,
                        -100
                    )
                }"
            )
        }
    }

    private fun createNotificationChannel(
        context: Context,
        channelId: String
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = notiManagerWrapper.notificationManager

            val serviceChannel = NotificationChannel(
                channelId,
                context.getString(R.string.app_name),
                NotificationManager.IMPORTANCE_HIGH
            )

            notificationManager?.createNotificationChannel(serviceChannel)
        }
    }

    private fun createNotiView(
        notificationId: Int,
        title: String,
        content: String,
        @DrawableRes imageRes: Int,
        isExpand: Boolean
    ): RemoteViews {

        val isApiGreaterThanOrEqual31 = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

        val remoteViewsLayoutRes = when {
            isApiGreaterThanOrEqual31 && !isExpand -> R.layout.layout_polling_event_message_notification
            isApiGreaterThanOrEqual31 && isExpand -> R.layout.layout_polling_event_message_notification_expand
            !isApiGreaterThanOrEqual31 && !isExpand -> R.layout.layout_polling_event_message_notification_for_no_decorated
            !isApiGreaterThanOrEqual31 && isExpand -> R.layout.layout_polling_event_message_notification_expand_for_no_decorated
            else -> R.layout.layout_polling_event_message_notification
        }

        return RemoteViews(
            context.packageName,
            remoteViewsLayoutRes
        ).apply {
            setTextViewText(R.id.tv_noti_title, title)
            setTextViewText(R.id.tv_noti_content, content)
            setImageViewResource(R.id.iv_noti_image, imageRes)

            val goBtnVisible = if (isExpand) View.VISIBLE else View.GONE
            setViewVisibility(R.id.fl_go_btn_parent, goBtnVisible)

            // configure close noti btn
            if (isApiGreaterThanOrEqual31) {
                setViewVisibility(R.id.iv_noti_close, View.GONE)
            } else {
                setViewVisibility(R.id.iv_noti_close, View.VISIBLE)

                val pendingIntentFlags = PendingIntent.FLAG_UPDATE_CURRENT

                val closeNotiIntent =
                    HandleRemoteViewsEventReceiver.CloseNotification.createIntent(notificationId)

                val closeNotiPendingIntent = PendingIntent
                    .getBroadcast(context, notificationId, closeNotiIntent, pendingIntentFlags)

                setOnClickPendingIntent(R.id.iv_noti_close, closeNotiPendingIntent)
            }

            if (DeviceInfo.isDarkMode(context)) {
                val textColor = Color.White.toArgb()
                setTextColor(R.id.tv_noti_title, textColor)
                setTextColor(R.id.tv_noti_content, textColor)
            }
        }
    }
}
