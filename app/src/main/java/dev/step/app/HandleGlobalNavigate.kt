@file:Suppress("ObjectPropertyName")

package dev.step.app

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

typealias NavigateAction = Navigator.() -> Unit

val Navigator.previousKey: NavigationKey?
    get() = backstack.getOrNull(backstack.lastIndex - 1)?.navigationKey

var globalNavigator: Navigator? = null

private val _globalNavigateEventFlow = MutableSharedFlow<DoGlobalNavigate>(0)
val globalNavigateEventFlow: Flow<DoGlobalNavigate> get() = _globalNavigateEventFlow

sealed interface DoGlobalNavigate {
    data class NavNode(val node: NavigationKey.WithNode<*>) : DoGlobalNavigate
    data class NavBlock(val block: Navigator.() -> Unit) : DoGlobalNavigate
}

suspend fun sendGlobalNavigateEvent(globalNavigate: DoGlobalNavigate) {
    _globalNavigateEventFlow.emit(globalNavigate)
}

@Composable
fun HandleGlobalNavigate(
    navigator: Navigator
) {
    debugLog("register HandleGlobalNavigate")
    LaunchedEffect(Unit) {
        globalNavigateEventFlow.onEach {
            when (it) {

                is DoGlobalNavigate.NavNode -> navigator.push(it.node)

                is DoGlobalNavigate.NavBlock -> {
                    it.block(navigator)
                }
            }
        }.launchIn(this)
    }
}
