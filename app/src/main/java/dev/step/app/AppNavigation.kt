package dev.step.app

import android.os.Build
import android.os.Parcelable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.Screen
import com.roudikk.guia.core.rememberNavigator
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.localDialog
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.removeAll
import com.roudikk.guia.extensions.replaceLast
import com.roudikk.guia.extensions.requireLocalNavigator
import dev.step.app.androidplatform.androidcomponent.sensor.StepDetectorSensor
import dev.step.app.androidplatform.biz.game.GameADT
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.UserSettingsDataKv
import dev.step.app.ui.dialog.adloading.InterAdLoadingDialog
import dev.step.app.ui.dialog.exchangecoins.ExchangeCoinsDialog
import dev.step.app.ui.dialog.game.GameNoRemainingTimeDialog
import dev.step.app.ui.dialog.game.GameRewardedDialog
//import dev.step.app.ui.dialog.clicknotinavtorewardtips.ClickNotiNavToRewardTipsDialog
//import dev.step.app.ui.dialog.adloading.AdLoadingDialog
//import dev.step.app.ui.dialog.game.GameADT
import dev.step.app.ui.dialog.genderchange.GenderChangeDialog
import dev.step.app.ui.dialog.heightchange.HeightChangeDialog
import dev.step.app.ui.dialog.measureunitchange.MeasureUnitChangeDialog
import dev.step.app.ui.dialog.motionsensorsensitivitychange.MotionSensorSensitivityChangeDialog
import dev.step.app.ui.dialog.newuserrewarded.NewUserRewardedDialog
import dev.step.app.ui.dialog.noenough.NoEnoughCoinsDialog
import dev.step.app.ui.dialog.noenough.NoEnoughStepsDialog
//import dev.step.app.ui.dialog.redeemed.CashRedeemedSuccessfullyDialog
//import dev.step.app.ui.dialog.redeemed.CouponRedeemedSuccessfullyDialog
//import dev.step.app.ui.dialog.reviews.RateOnFeedbackDialog
//import dev.step.app.ui.dialog.reviews.RateOnGpDialog
//import dev.step.app.ui.dialog.rewarded.RewardedDialog
import dev.step.app.ui.dialog.stepgoalchange.StepGoalChangeDialog
import dev.step.app.ui.dialog.steplengthchange.StepLengthChangeDialog
//import dev.step.app.ui.dialog.steprewarded.*
//import dev.step.app.ui.dialog.game.GameNoRemainingTimeDialog
//import dev.step.app.ui.dialog.game.GameRewardedDialog
import dev.step.app.ui.dialog.notificationpermissionrequester.NotificationPermissionRequesterDialog
import dev.step.app.ui.dialog.rating.RateOnFeedbackDialog
import dev.step.app.ui.dialog.rating.RatingDialog
import dev.step.app.ui.dialog.redeemed.CashRedeemedSuccessDialog
import dev.step.app.ui.dialog.redeemed.CouponRedeemedSuccessDialog
import dev.step.app.ui.dialog.rewarded.RewardedDialog
import dev.step.app.ui.dialog.rewardederror.RewardedErrorDialog
import dev.step.app.ui.dialog.rewardedloading.RewardedLoadingDialog
import dev.step.app.ui.dialog.signinrewarded.SignInRewardedDialog
import dev.step.app.ui.dialog.stage1kstepsrewarded.StageOneRewardedDialog
import dev.step.app.ui.dialog.stage5kstepsrewarded.StageTwoRewardedDialog
import dev.step.app.ui.dialog.weightchange.WeightDialog
import dev.step.app.ui.screen.game1.Game1
import dev.step.app.ui.screen.game2.Game2
import dev.step.app.ui.screen.game3.Game3
import dev.step.app.ui.screen.guidepermissions.GuideActivityRecognitionPermissionNode
import dev.step.app.ui.screen.guidepermissions.GuideNotificationPermissionScreenNode
//import dev.step.app.ui.screen.freespins.FreeSpinsScreen
import dev.step.app.ui.screen.home.HomeScreen
import dev.step.app.ui.screen.redeemedcash.RedeemedCashScreen
import dev.step.app.ui.screen.redeemedcoupon.RedeemedCouponScreen
//import dev.step.app.ui.screen.luckyscratch.LuckyScratchScreen
//import dev.step.app.ui.screen.redeemedcash.RedeemedCashScreen
//import dev.step.app.ui.screen.redeemedcoupon.RedeemedCouponScreen
import dev.step.app.ui.screen.splash.SplashScreen
import dev.step.app.ui.screen.withdraw.RedeemedPrize
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
//import dev.step.app.ui.screen.superwheel.SuperWheelScreen
import dev.step.app.ui.screen.withdraw.WithdrawScreen
import kotlinx.coroutines.delay

abstract class ScreenDestinationNode : NavigationKey.WithNode<Screen> {

    override fun navigationNode() = Screen {
        val navigator = requireLocalNavigator()

        Content(
            navigator = navigator,
        )
    }

    @Composable
    abstract fun Content(
        navigator: Navigator,
    )
}


@Parcelize
data class HomeNode(val args: HomeArgs = HomeArgs()) : ScreenDestinationNode() {

    @Parcelize
    data class HomeArgs(
        val tabIndex: Int = 1
    ) : Parcelable

    @Composable
    override fun Content(navigator: Navigator) {
        HomeScreen(
            args = args,
            navigator = navigator,
            openStepGoalChangeDialog = {
                navigator.push(StepGoalChangeDialogNode)
            },
            openMotionSensorSensitivityAdjustmentDialog = {
                navigator.push(
                    MotionSensorSensitivityChangeDialogNode
                )
            },
            openWeightDialog = {
                navigator.push(WeightChangeDialogNode)
            },
            openHeightDialog = {
                navigator.push(HeightChangeDialogNode)
            },
            openGenderDialog = {
                navigator.push(GenderChangeDialogNode)
            },
            openStepLengthDialog = {
                navigator.push(StepLengthChangeDialogNode)
            },
            openMeasureUnitDialog = {
                navigator.push(MeasureUnitChangeDialogNode)
            },
            openWithdraw = {
                navigator.push(WithdrawNode)
            },
            openGame1 = {
                navigator.push(Game1Node())
            },
            openGame2 = {
                navigator.push(Game2Node())
            },
            openGame3 = {
                navigator.push(Game3Node)
            },
            openSignInRewardedDialog = {
                navigator.push(SignInRewardedDialogNode)
            },
            openExchangeCoinsRewardedDialog = {
                navigator.push(ExchangeCoinsDialogNode)
            },
            openNewUserRewardedDialog = { adBizEnable ->
                navigator.push(NewUserRewardedDialogNode(adBizEnable = adBizEnable))
            },
            openNoEnoughStepsDialog = {
                navigator.push(NoEnoughStepsDialogNode)
            },
            openStageOneRewardedDialog = {
                navigator.push(StageOneRewardedDialogNode)
            },
            openStageTwoRewardedDialog = {
                navigator.push(StageTwoRewardedDialogNode)
            },
            openRewardedDialog = { from: String, coins: Int, times: Int ->
                navigator.push(
                    RewardedDialogNode(
                        RewardedDialogNode.RewardedDialogArgs(
                            from = from,
                            coins = coins,
                            times = times
                        )
                    )
                )
            },
        )
    }
}

@Parcelize
data class SplashNode(
    val isColdStart: Boolean = true
) : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
//        val userOperateDataKv: UserOperateDataKv = koinInject()
//        val stepDetectorSensor: StepDetectorSensor = koinInject()

        SplashScreen(
            isColdStart = isColdStart,
            navUp = navigator::pop,
            onNext = {
//                if (userOperateDataKv.hasBeenGuided || Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
//                    navigator.replaceLast(HomeNode())
//                } else if (stepDetectorSensor.sensor != null) {
//                    navigator.replaceLast(GuideActivityRecognitionPermissionNode)
//                } else {
//                    navigator.replaceLast(GuideNotificationPermissionScreenNode(canGoBack = false))
//                }
                navigator.replaceLast(HomeNode())
            }
        )
    }
}

@Parcelize
object WithdrawNode : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        WithdrawScreen(
            navUp = navigator::pop,
            openRedeemedCoupon = {
                navigator.push(RedeemedCouponNode(coupon = it))
            },
            openRedeemedCash = {
                navigator.push(RedeemedCashNode(it.coinPrice, it.amount))
            },
            openNoEnoughCoinsDialog = {
                navigator.push(NoEnoughCoinsDialogNode)
            }
        )
    }
}

@Parcelize
data class RedeemedCouponNode(
    val coupon: RedeemedPrize.Coupon
) : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        RedeemedCouponScreen(
            coupon = coupon,
            navUp = navigator::pop,
            openSuccessfullyDialog = {
                navigator.replaceLast(CouponRedeemedSuccessDialogNode)
            }
        )
    }
}


@Parcelize
data class RedeemedCashNode(
    val coinPrice: Int = 0,
    val amount: Int = 0,
) : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        RedeemedCashScreen(
            coinPrice = coinPrice,
            amount = amount,
            navUp = navigator::pop,
            openSuccessfullyDialog = { cashText, accountAddress ->
                navigator.replaceLast(
                    CashRedeemedSuccessDialogNode(cashText, accountAddress)
                )
            }
        )
    }
}

@Parcelize
data class Game1Node(
    val args: Game1Args = Game1Args(),
) : ScreenDestinationNode() {

    @Parcelize
    data class Game1Args(
        val navFromNotification: Boolean = false,
    ) : Parcelable

    @Composable
    override fun Content(navigator: Navigator) {
        Game1(
            args = args,
            navigator = navigator
        )
    }
}

@Parcelize
object Game3Node : ScreenDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator) {
        Game3(
            navUp = navigator::pop,
            openNoRemainingTimeDialog = { from ->
                navigator.push(GameNoRemainingTimeDialogNode(gameADT = from))
            },
            openRewardedDialog = { gameADT, coins, times ->
                navigator.push(
                    GameRewardedDialogNode(args = GameRewardedArgs(gameADT, coins, times))
                )
            }
        )
    }
}

@Parcelize
data class Game2Node(
    val args: Game2Args = Game2Args(),
) : ScreenDestinationNode() {

    @Parcelize
    data class Game2Args(
        val navFromNotification: Boolean = false,
    ) : Parcelable

    @Composable
    override fun Content(navigator: Navigator) {
        Game2(
            args = args,
            navUp = navigator::pop,
            openNoRemainingTimeDialog = { from ->
                navigator.push(GameNoRemainingTimeDialogNode(gameADT = from))
            },
            openRewardedDialog = { gameADT, coins, times ->
                navigator.push(
                    GameRewardedDialogNode(
                        args = GameRewardedArgs(
                            gameADT = gameADT,
                            coins = coins,
                            times = times,
                        )
                    )
                )
            }
        )
    }
}

// -----------------------------------------------------------------------------------------------

abstract class DialogDestinationNode(
    private val dialogOptions: Dialog.DialogOptions = Dialog.DialogOptions()
) : NavigationKey.WithNode<Dialog> {

    override fun navigationNode() = Dialog {
        val dialog = localDialog()
        LaunchedEffect(dialog) {
            dialog?.dialogOptions = dialogOptions
        }

        val navigator = requireLocalNavigator()

        Content(
            navigator = navigator,
            dialog = dialog
        )
    }

    @Composable
    abstract fun Content(
        navigator: Navigator,
        dialog: Dialog?,
    )
}


@Parcelize
object StepGoalChangeDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        StepGoalChangeDialog(navUp = navigator::pop)
    }

}

@Parcelize
object MotionSensorSensitivityChangeDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        MotionSensorSensitivityChangeDialog(navUp = navigator::pop)
    }
}

@Parcelize
object WeightChangeDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        WeightDialog(navUp = navigator::pop)
    }
}

@Parcelize
object HeightChangeDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        HeightChangeDialog(navUp = navigator::pop)
    }
}

@Parcelize
object GenderChangeDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        GenderChangeDialog(
            navUp = navigator::pop,
        )
    }
}

@Parcelize
object StepLengthChangeDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        StepLengthChangeDialog(
            navUp = navigator::pop,
        )
    }
}

@Parcelize
object MeasureUnitChangeDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        MeasureUnitChangeDialog(
            navUp = navigator::pop,
        )
    }
}

@Parcelize
object RatingDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        RatingDialog(
            navUp = navigator::pop,
            toRateOnFeedback = {
                navigator.replaceLast(RateOnFeedbackDialogNode)
            }
        )
    }
}

@Parcelize
object RateOnFeedbackDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        RateOnFeedbackDialog(
            navUp = navigator::pop
        )
    }
}

@Parcelize
data class NewUserRewardedDialogNode(
    val adBizEnable: Boolean = true
) : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        NewUserRewardedDialog(
            adBizEnable = adBizEnable,
            navUp = navigator::pop
        )
    }
}

@Parcelize
object SignInRewardedDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        SignInRewardedDialog(
            navUp = navigator::pop,
        )
    }
}

@Parcelize
object ExchangeCoinsDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        ExchangeCoinsDialog(
            navUp = navigator::pop
        )
    }
}

@Parcelize
object StageOneRewardedDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        StageOneRewardedDialog(
            navUp = navigator::pop,
        )
    }
}

@Parcelize
object StageTwoRewardedDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        StageTwoRewardedDialog(
            navUp = navigator::pop,
        )
    }
}

@Parcelize
object NoEnoughCoinsDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        NoEnoughCoinsDialog(
            navUp = navigator::pop
        )
    }
}

@Parcelize
object NoEnoughStepsDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        NoEnoughStepsDialog(
            navUp = navigator::pop
        )
    }
}

@Parcelize
data class CashRedeemedSuccessDialogNode(
    val cashText: String = "$0",
    val accountAddress: String = ""
) : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        CashRedeemedSuccessDialog(
            cashText = cashText,
            accountAddress = accountAddress,
            navUp = navigator::pop
        )
    }
}

@Parcelize
object CouponRedeemedSuccessDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        CouponRedeemedSuccessDialog(
            navUp = navigator::pop
        )
    }
}

@Parcelize
data class GameNoRemainingTimeDialogNode(
    val gameADT: GameADT,
) : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        GameNoRemainingTimeDialog(
            gameADT = gameADT,
            navUp = navigator::pop
        )
    }
}

@Parcelize
data class GameRewardedArgs(
    val gameADT: GameADT = GameADT.Unknown,
    val coins: Int = 0,
    val times: Int = 1,
) : Parcelable

@Parcelize
data class GameRewardedDialogNode(
    val args: GameRewardedArgs = GameRewardedArgs()
) : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        GameRewardedDialog(
            args = args,
            navUp = navigator::pop,
        )
    }
}

@Parcelize
data class RewardedDialogNode(
    val rewardedDialogArgs: RewardedDialogArgs = RewardedDialogArgs()
) : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Parcelize
    data class RewardedDialogArgs(
        val from: String = "",
        val coins: Int = 0,
        val times: Int = 1,
    ) : Parcelable

    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        RewardedDialog(
            rewardedDialogArgs = rewardedDialogArgs,
            navUp = navigator::pop,
        )
    }
}

@Parcelize
data class RewardedLoadingDialogNode(
    val instantlyLoad: Boolean = false,
) : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        RewardedLoadingDialog(
            instantlyLoad = instantlyLoad,
            navUp = navigator::pop,
            popBackStack = {
                navigator.removeAll {
                    it is RewardedLoadingDialogNode
                }
            },
            replaceToRewardedErrorDialog = {
                navigator.replaceLast(RewardedErrorDialogNode)
            }
        )
    }
}

@Parcelize
object RewardedErrorDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        RewardedErrorDialog(
            navUp = navigator::pop,
            popBackStack = {
                navigator.removeAll {
                    it == RewardedErrorDialogNode
                }
            }
        )
    }
}

@Parcelize
object ClickNotiNavToRewardTipsDialogNode : DialogDestinationNode(
    dialogOptions = Dialog.DialogOptions(
        dismissOnClickOutside = false,
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
//    ClickNotiNavToRewardTipsDialog(
//        navigator = navigator,
//        from = from,
//        navRoute = navRoute
//    )
    }
}

@Parcelize
object NotificationPermissionRequesterDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        NotificationPermissionRequesterDialog(
            navUp = navigator::pop,
        )
    }
}

@Parcelize
object InterAdLoadingDialogNode : DialogDestinationNode() {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        InterAdLoadingDialog()

        LaunchedEffect(Unit) {
            delay(5000)
            if (navigator.currentKey is InterAdLoadingDialogNode) {
                navigator.pop()
            }
        }
    }
}

@Composable
internal fun appNavigation(): Navigator {
    val userSettingsDataKv: UserSettingsDataKv = koinInject()

    val userGender = userSettingsDataKv.gender

    val startDestination = when {
        (userGender == null) -> {
            userSettingsDataKv.configureDefaultSettings()

            SplashNode()
        }

        else -> SplashNode()
    }

    val navigator = rememberNavigator(initialKey = startDestination) {
        // Provide a default transition between all keys
        defaultTransition { -> MaterialSharedAxisTransitionX }

        // Provide a transtion based on what the previous key and the new key are.
        // Add some logic, this returns EnterExitTransition.
        defaultTransition { previousKey, newKey, isPop ->
            if (isPop) {
                MaterialSharedAxisTransitionX.popEnterExit
            } else {
                MaterialSharedAxisTransitionX.enterExit
            }
        }
    }

    return navigator
}
