package dev.step.app

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import androidx.core.view.WindowCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.findViewTreeViewModelStoreOwner
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.findViewTreeSavedStateRegistryOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import dev.step.app.androidplatform.OnLifecycleEvent
import dev.step.app.androidplatform.androidcomponent.IgnoringBatteryOptimizationRequester
import dev.step.app.androidplatform.androidcomponent.PendingIntentPassedToIntentExtra
import dev.step.app.androidplatform.androidcomponent.global.configureGlobalMainActivity
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.notification.HandsUpNotification
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import dev.step.app.androidplatform.send
import dev.step.app.ui.screen.home.Home
import dev.step.app.ui.theme.AppTheme
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

class MainActivity : ComponentActivity() {

    private var lifecycleEvent: Lifecycle.Event? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        configureGlobalMainActivity(this)
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            Content()

            OnLifecycleEvent(onEvent = { _, event ->
                lifecycleEvent = event
            })
        }
        setOwners()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(newIntent: Intent?) {
        runCatching {
            newIntent?.let { _ ->
                debugLog("MainActivity $lifecycleEvent")
                HandsUpNotification.handleNotificationIntent(
                    newIntent,
                    lifecycleEvent == Lifecycle.Event.ON_START
                )
//
//                FCMNotification.handleIntent(
//                    newIntent,
//                    lifecycleEvent == Lifecycle.Event.ON_START
//                )

                if (lifecycleEvent == Lifecycle.Event.ON_START) {
                    PendingIntentPassedToIntentExtra.handleIntent(
                        newIntent,
                        isColdStart = false
                    )
                }
            }
        }
    }

    @Composable
    private fun Content() {
        AppTheme(
            darkTheme = false
        ) {
            Home()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == IgnoringBatteryOptimizationRequester.REQUEST_CODE) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager?
                val isIgnoringBatteryOptimizations =
                    powerManager?.isIgnoringBatteryOptimizations(packageName)

                IgnoringBatteryOptimizationRequester.ignoringEventFlow.send(isIgnoringBatteryOptimizations)

                if (isIgnoringBatteryOptimizations == true) {
                    logEventRecord("battery_permission_request_success")
                    debugLog("battery_permission_request_success")
                } else {
                    logEventRecord("battery_permission_request_fail")
                    debugLog("battery_permission_request_fail")
                }
            }
        }
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        windowFocusChangedFlow.update { hasFocus }
        super.onWindowFocusChanged(hasFocus)
        debugLog("onWindowFocusChanged hasFocus: $hasFocus")
    }

    companion object {
        val windowFocusChangedFlow = MutableStateFlow<Boolean?>(null)
    }
}


private fun ComponentActivity.setOwners() {
    val decorView = window.decorView
    if (decorView.findViewTreeLifecycleOwner() == null) {
        decorView.setViewTreeLifecycleOwner(this)
    }
    if (decorView.findViewTreeViewModelStoreOwner() == null) {
        decorView.setViewTreeViewModelStoreOwner(this)
    }
    if (decorView.findViewTreeSavedStateRegistryOwner() == null) {
        decorView.setViewTreeSavedStateRegistryOwner(this)
    }
}
