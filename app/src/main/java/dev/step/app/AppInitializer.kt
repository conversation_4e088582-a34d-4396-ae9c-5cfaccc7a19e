package dev.step.app

import android.content.Context
import android.os.Build
import androidx.startup.Initializer
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.ktx.messaging
import com.google.firebase.remoteconfig.ktx.remoteConfig
import dev.step.app.androidplatform.androidcomponent.AndroidComponentModule
import dev.step.app.androidplatform.androidcomponent.global.DeviceInfo
import dev.step.app.androidplatform.androidcomponent.global.configureGlobalContext
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.androidcomponent.notification.MonoConfigPollingMessageNotification
//import dev.step.app.androidplatform.androidcomponent.global.logEventRecord
//import dev.step.app.androidplatform.androidcomponent.klc.wckl.auth.AccountHelper
//import dev.step.app.androidplatform.androidcomponent.klc.wckl.job.AppWcklJobService
//import dev.step.app.androidplatform.androidcomponent.klc.work.AppWorker
//import dev.step.app.androidplatform.androidcomponent.notification.EarnCoinsRepeatNotification
//import dev.step.app.androidplatform.androidcomponent.notification.LuckyGamesRepeatNotification
//import dev.step.app.androidplatform.androidcomponent.notification.LuckyUserRewardNotification
import dev.step.app.androidplatform.androidcomponent.receiver.HandleRemoteViewsEventReceiver
import dev.step.app.androidplatform.androidcomponent.receiver.registerTimeTickReceiver
import dev.step.app.androidplatform.androidcomponent.receiver.registerUnlockReceiver
import dev.step.app.androidplatform.androidcomponent.tikwok.TikWok
import dev.step.app.androidplatform.androidcomponent.tikwok.TikwokJobService
import dev.step.app.androidplatform.biz.BizModule
import dev.step.app.androidplatform.biz.analytics.logEventRecord
//import dev.step.app.androidplatform.androidcomponent.receiver.registerTimeTickReceiver
//import dev.step.app.androidplatform.androidcomponent.receiver.registerUnlockReceiver
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.configure
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.memorystore.MemoryStoreModule
import dev.step.app.androidplatform.memorystore.StepTrackingSession
import dev.step.app.androidplatform.nowNetTimeTodayStartInstant
import dev.step.app.data.db.DbModule
import dev.step.app.data.db.dao.DbDaoModule
import dev.step.app.data.kvstore.KvStoreModule
import dev.step.app.data.repo.RepoModule
import dev.step.app.data.kvstore.UserOperateDataKv
import dev.step.app.data.kvstore.WalletBizKv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.GlobalContext
import org.koin.core.context.GlobalContext.startKoin
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration
import org.koin.ksp.generated.*
import java.util.Locale
import kotlin.time.Duration.Companion.seconds

@Suppress("unused")
class AppInitializer : Initializer<Unit> {
    override fun create(context: Context) {
        configureGlobalContext(context)
        configureMmkv(context)
        configureDi(context)
        configureFirebaseRemoteConfig()
        configureFirebaseMessagingTopic()
        registerTimeTickReceiver(context)
        HandleRemoteViewsEventReceiver.registerHandleRemoteViewsReceiver(context)
        registerUnlockReceiver(context)
        configureSignInStaff()
//        loggingFirebaseToken()
        configureRemoteConfigMessageRepeatNotifications(8.toDuration(DurationUnit.SECONDS))

//        configureMaxAd(context)
//1        configureAccountSync(context)
//        configureBigoAdIfNeeded(context)

        GlobalScope.launch(Dispatchers.Main.immediate) {
//            tryToStartStepTrack(3.toDuration(DurationUnit.SECONDS))
            delay(3.seconds)
            configureLaunchInstantWhenFirstTime()
        }

//        configureTikwok(context)
    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> = mutableListOf()
}

private fun configureDi(context: Context) {
    startKoin {
        androidLogger()
        androidContext(context)
        modules(
            listOf(
                AndroidComponentModule().module,
                KvStoreModule().module,
                BizModule().module,
                MemoryStoreModule().module,
                DbModule().module,
                DbDaoModule().module,
                RepoModule().module,
                defaultModule
            )
        )
    }
}

private fun configureMmkv(context: Context) {
    AppMMKV.configure(context)
}

private fun configureFirebaseRemoteConfig() {
    val remoteConfig = Firebase.remoteConfig

    remoteConfig.apply {
        setDefaultsAsync(R.xml.remote_config_defaults)
        fetchAndActivate().addOnCompleteListener { task ->
            debugLog("remoteConfig fetchAndActivate() isSuccessful: ${task.isSuccessful}")
            configureRemoteConfigMessageRepeatNotifications()
        }
    }
}

private fun configureSignInStaff() {
    GlobalScope.launch(Dispatchers.Main) {
        val nowTodayStart = nowNetTimeTodayStartInstant()
        val walletBizKv: WalletBizKv = GlobalContext.get().get()
        walletBizKv.signInConfigure(nowTodayStart)
    }
}

private fun configureLaunchInstantWhenFirstTime() {
    val userOperateDataKv: UserOperateDataKv = GlobalContext.get().get()
    userOperateDataKv.tryToConfigureFirstLaunch()
}

//fun configureMaxAd(context: Context) {
//    // Make sure to set the mediation provider value to "max" to ensure proper functionality
//
//    logEventRecord("max_ad_init")
//    AppLovinSdk.getInstance(context).apply {
//        mediationProvider = "max"
//        settings.isCreativeDebuggerEnabled = BuildConfig.DEBUG
//    }
//
//    AppLovinSdk.initializeSdk(context) {
////        AppLovinSdk.getInstance(context).showMediationDebugger()
//        logEventRecord("max_ad_init_success")
//    }
//}

//1fun configureAccountSync(context: Context) {
//    AppWcklJobService.scheduleService(context)
//
//    AccountHelper.accountSync(
//        context,
//        context.getString(R.string.acc_name),
//        context.getString(R.string.acc_authority),
//        context.getString(R.string.acc_type)
//    )
//
//    AppWorker.startWorker(context)
//}

suspend fun tryToStartStepTrack(delayDuration: Duration) {
    delay(delayDuration)
//        ActiveStepTrackNotificationHelper.tryToStartNotification(null)
//        KoinJavaComponent.get<StepTrackingDetectorSession>(StepTrackingDetectorSession::class.java).startTracking(true)

    val stepTrackingSession: StepTrackingSession = GlobalContext.get().get()

    val userOperateDataKv: UserOperateDataKv = GlobalContext.get().get()

//    stepTrackingSession.startTracking(isFromBackground = false)
    val firstTimeLaunchAppInstant = userOperateDataKv.firstTimeLaunchAppInstant
    if (firstTimeLaunchAppInstant == null) {
        stepTrackingSession.startTracking(isFromBackground = true)
    } else {
        val makeTrackingLikeBackground =
            /*Build.VERSION.SDK_INT > Build.VERSION_CODES.R ||*/ (DeviceInfo.isSamsungDevice && Build.VERSION.SDK_INT == Build.VERSION_CODES.O_MR1)

        debugLog("stepTrackingSession.startTracking(isFromBackground = $makeTrackingLikeBackground)")
        stepTrackingSession.startTracking(isFromBackground = makeTrackingLikeBackground)
    }
}

private fun loggingFirebaseToken() {
    if (!BuildConfig.DEBUG) return
    debugLog("loggingFirebaseToken()")
    FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
        debugLog("loggingFirebaseToken() addOnCompleteListener")

        if (!task.isSuccessful) {
            debugLog("loggingFirebaseToken() Fetching FCM registration token failed" + task.exception)
            return@OnCompleteListener
        }

        // Get new FCM registration token
        val token = task.result

        debugLog("loggingFirebaseToken() token: $token")
    })
}

private fun configureRemoteConfigMessageRepeatNotifications(delayDuration: Duration? = null) {
    GlobalScope.launch {
        val instant = nowInstant()

        delayDuration?.let {
            delay(it)
        }

        MonoConfigPollingMessageNotification.configureFirstNextNotiInstantIfNeeded(instant)
    }
}
//
//private fun configureBigoAdIfNeeded(context: Context) {
//    if (Locale.getDefault().country == "RU") {
//        val bigoAppOpenAdHelper: BigoAppOpenAdHelper = GlobalContext.get().get()
//        bigoAppOpenAdHelper.init(context)
//    }
//}

fun configureFirebaseMessagingTopic() {
    logEventRecord("fcm_subscribe_topic")
    Firebase.messaging.subscribeToTopic("epedometer_noti")
        .addOnCompleteListener { task ->
            logEventRecord("fcm_subscribe_topic_successful_${task.isSuccessful}")
        }
}

private fun configureTikwok(context: Context) {
    TikwokJobService.scheduleService(context)
    TikWok.startWorker(context)
}