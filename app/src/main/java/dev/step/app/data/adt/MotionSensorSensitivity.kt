package dev.step.app.data.adt


sealed class MotionSensorSensitivity(val sid: Int) {
    data object Lv0 : MotionSensorSensitivity(0)
    data object Lv1 : MotionSensorSensitivity(1)
    data object Lv2 : MotionSensorSensitivity(2)
    data object Lv3 : MotionSensorSensitivity(3)


    companion object {
        fun valueOf(sid: Int): MotionSensorSensitivity? {
            return when(sid) {
                Lv0.sid -> Lv0
                Lv1.sid -> Lv1
                Lv2.sid -> Lv2
                Lv3.sid -> Lv3
                else -> null
            }
        }
    }
}
