@file:Suppress("PropertyName")

package dev.step.app.data.pojo.remoteconfig

import dev.step.app.R
import kotlinx.serialization.Serializable

//language=json
private val json0 = """
    [
      {
        "sid": 1,
        "title": "title1",
        "content": "content1",
        "nav_id": 1
      },
      {
        "sid": 2,
        "title": "title2",
        "content": "content2",
        "nav_id": 1
      },
      {
        "sid": 3,
        "title": "title3",
        "content": "content3",
        "nav_id": 1
      },
      {
        "sid": 4,
        "title": "title4",
        "content": "content4",
        "nav_id": 1
      },
      {
        "sid": 5,
        "title": "title5",
        "content": "content5",
        "nav_id": 1
      },
      {
        "sid": 6,
        "title": "title6",
        "content": "content6",
        "nav_id": 1
      },
      {
        "sid": 7,
        "title": "title7",
        "content": "content7",
        "nav_id": 1
      },
      {
        "sid": 8,
        "title": "title8",
        "content": "content8",
        "nav_id": 1
      }
    ]
""".trimIndent()

@Serializable
data class PollingMessage(
    val sid: Int,
    val title: String,
    val content: String,
    val nav_id: Int,
) {

    val drawableResId: Int
        get() = when (sid) {
            1 -> R.drawable.img_noti_walk1
            2 -> R.drawable.img_noti_walk2
            3 -> R.drawable.img_noti_cs
            4 -> R.drawable.img_noti_wl
            5 -> R.drawable.img_noti_game1
            6 -> R.drawable.img_noti_game3
            7 -> R.drawable.img_noti_game2
            8 -> R.drawable.img_noti_treasure_chest
            else -> R.drawable.img_noti_treasure_chest
        }

    companion object {
        val Test = listOf(
            PollingMessage(1, "title1", "content1", 1),
            PollingMessage(2, "title2", "content2", 2),
            PollingMessage(3, "title3", "content3", 3),
            PollingMessage(4, "title4", "content4", 4),
            PollingMessage(5, "title5", "content5", 5),
            PollingMessage(6, "title6", "content6", 6),
            PollingMessage(7, "title7", "content7", 7),
            PollingMessage(8, "title8", "content8", 1),
        )
    }
}

//language=json
private val json1 = """
    {
      "first_push_after_of_minutes": 30,
      "push_interval_of_minutes": 20
    }
""".trimIndent()

@Serializable
data class PollingMessageConfig(
    val first_push_after_of_minutes: Int,
    val push_interval_of_minutes: Int,
)