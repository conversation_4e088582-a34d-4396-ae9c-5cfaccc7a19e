//package dev.step.app.data.pojo.remoteconfig
//
//import kotlinx.serialization.SerialName
//import kotlinx.serialization.Serializable
//
//@Serializable
//data class RepeatNotiMessageGroup(
//    @SerialName("message_first_push") val messageFirstPushAfterOfHours: Int? = null,
//    @SerialName("message_first_push_min") val messageFirstPushAfterOfMinutes: Int? = null,
//    @SerialName("time_interval_min") val repeatIntervalOfMinutes: Int? = null,
//    private val message_title: String? = null,
//    private val message_text: String? = null,
//    private val message1_title: String? = null,
//    private val message1_text: String? = null,
//    private val message2_title: String? = null,
//    private val message2_text: String? = null,
//    private val message3_title: String? = null,
//    private val message3_text: String? = null,
//
//    @SerialName("times") val coinRewardedTimes: Int? = null,
//    val coins: Int? = null,
//) {
//    val messageGroup: List<NotiSingleMessage>
//        get() {
//            val messages = mutableListOf<NotiSingleMessage>()
//            if (!message_title.isNullOrEmpty() && !message_text.isNullOrEmpty()) {
//                messages.add(NotiSingleMessage(message_title, message_text))
//            }
//
//            if (!message1_title.isNullOrEmpty() && !message1_text.isNullOrEmpty()) {
//                messages.add(NotiSingleMessage(message1_title, message1_text))
//            }
//
//            if (!message2_title.isNullOrEmpty() && !message2_text.isNullOrEmpty()) {
//                messages.add(NotiSingleMessage(message2_title, message2_text))
//            }
//
//            if (!message3_title.isNullOrEmpty() && !message3_text.isNullOrEmpty()) {
//                messages.add(NotiSingleMessage(message3_title, message3_text))
//            }
//
//            return messages
//        }
//
//}
//
//@Serializable
//data class NotiSingleMessage(
//    val title: String,
//    val content: String,
//)