package dev.step.app.data.pojo.wallet

import android.os.Parcelable
import dev.step.app.R
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class SignIn(
    val sun: <PERSON>olean = false,
    val mon: <PERSON><PERSON><PERSON> = false,
    val tue: <PERSON><PERSON><PERSON> = false,
    val wed: <PERSON><PERSON><PERSON> = false,
    val thu: <PERSON><PERSON><PERSON> = false,
    val fri: <PERSON><PERSON><PERSON> = false,
    val sat: <PERSON><PERSON><PERSON> = false,
) : Parcelable

@Parcelize
data class GameRemaining(
    val dayStartInstantSeconds: Long = 0,
    val remainingTimes: Int = 0,
) : Parcelable

@Parcelize
data class RedeemPictureData(
    val id: Int,
    val unlock: <PERSON><PERSON><PERSON> = false,
) : Parcelable {

    @IgnoredOnParcel
    val drawableRes: Int
        get() = when (id) {
            1 -> R.drawable.img_redeem_picture_1
            2 -> R.drawable.img_redeem_picture_2
            3 -> R.drawable.img_redeem_picture_3
            4 -> R.drawable.img_redeem_picture_4
            5 -> R.drawable.img_redeem_picture_5
            6 -> R.drawable.img_redeem_picture_6
            7 -> R.drawable.img_redeem_picture_7
            8 -> R.drawable.img_redeem_picture_8
            else -> R.drawable.img_redeem_picture_8
        }
}

@Parcelize
data class RedeemPictures(
    val list: List<RedeemPictureData>
) : Parcelable {
    companion object {
        val Default = RedeemPictures(
            listOf(
                RedeemPictureData(1),
                RedeemPictureData(2),
                RedeemPictureData(3),
                RedeemPictureData(4),
                RedeemPictureData(5),
                RedeemPictureData(6),
                RedeemPictureData(7),
                RedeemPictureData(8),
            )
        )
    }
}