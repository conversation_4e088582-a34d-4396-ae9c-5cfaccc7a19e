package dev.step.app.data.adt

sealed class MeasurementUnit(val sid: Int) {
    data object Metric : MeasurementUnit(1)
    data object Imperial : MeasurementUnit(2)

    companion object {
        fun valueOf(sid: Int): MeasurementUnit? {
            return when (sid) {
                Metric.sid -> Metric
                Imperial.sid -> Imperial
                else -> null
            }
        }
    }
}
