package dev.step.app.data.pojo.remoteconfig

import kotlinx.serialization.Serializable

//language=json
private val json= """
    {
      "sign_in_rewarded_coins": 1000,
      "sign_in_rewarded_multiplier": 3,
      
      "new_user_rewarded_coins": 1000,
      "new_user_rewarded_multiplier": 3,
      
      "s1_steps": 1000,
      "s1_rewarded_coins": 1000,
      "s1_rewarded_multiplier": 3,
      
      "s2_steps": 5000,
      "s2_rewarded_coins": 1000,
      "s2_rewarded_multiplier": 3
    }
""".trimIndent()

@Serializable
data class DailyTask(
    val sign_in_rewarded_coins: Int,
    val sign_in_rewarded_multiplier: Int,

    val new_user_rewarded_coins: Int,
    val new_user_rewarded_multiplier: Int,

    val s1_steps: Int,
    val s1_rewarded_coins: Int,
    val s1_rewarded_multiplier: Int,

    val s2_steps: Int,
    val s2_rewarded_coins: Int,
    val s2_rewarded_multiplier: Int
)