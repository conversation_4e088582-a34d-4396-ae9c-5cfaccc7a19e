package dev.step.app.data.pojo.remoteconfig

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable


//language=json
private val json = """
{
  "b1_rewarded_coins": 2000,
  "b1_rewarded_multiplier": 4,
  "b2_rewarded_coins": 1000,
  "b2_rewarded_multiplier": 4,
  "b3_rewarded_coins": 1500,
  "b3_rewarded_multiplier": 4,
  "b4_rewarded_coins": 1500,
  "b4_rewarded_multiplier": 4
}
""".trimIndent()

@Serializable
data class RewardedBubbles(
    private var b1_rewarded_coins: Int = 2000,
    private var b1_rewarded_multiplier: Int = 4,
    private var b2_rewarded_coins: Int = 1000,
    private var b2_rewarded_multiplier: Int = 4,
    private var b3_rewarded_coins: Int = 1500,
    private var b3_rewarded_multiplier: Int = 4,
    private var b4_rewarded_coins: Int = 1500,
    private var b4_rewarded_multiplier: Int = 4,
) {
    fun get(bubbleId: Int): Pair<Int?, Int?>? {
        return when (bubbleId) {
            1 -> b1_rewarded_coins to b1_rewarded_multiplier
            2 -> b2_rewarded_coins to b2_rewarded_multiplier
            3 -> b3_rewarded_coins to b3_rewarded_multiplier
            4 -> b4_rewarded_coins to b4_rewarded_multiplier
            else -> null
        }
    }

    companion object {
        val Default = RewardedBubbles()
    }
}

@Parcelize
data class RewardBubblesShowState(
    val bubble1NeedShow: Boolean,
    val bubble2NeedShow: Boolean,
    val bubble3NeedShow: Boolean,
    val bubble4NeedShow: Boolean,
) : Parcelable
