package dev.step.app.data.repo

import dev.step.app.androidplatform.memorystore.StepTrackMmkvStore
import dev.step.app.data.db.dao.StepsTrackRecordDao
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single


@Module
class RepoModule {

    @Single
    fun provideStepsTrackRecordRepo(
        stepsTrackRecordDao: StepsTrackRecordDao,
        stepTrackMmkvStore: StepTrackMmkvStore,
    ) = StepsTrackRecordRepo(stepsTrackRecordDao, stepTrackMmkvStore)
}