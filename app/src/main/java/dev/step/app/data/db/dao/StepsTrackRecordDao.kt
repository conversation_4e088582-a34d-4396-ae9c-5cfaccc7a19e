package dev.step.app.data.db.dao

import androidx.room.*
import dev.step.app.data.db.model.StepsTrackRecordEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface StepsTrackRecordDao {

    @Query("SELECT * FROM steps_track_record")
    fun fetchAllRecordsFlow(): Flow<List<StepsTrackRecordEntity>>

    @Query(
        """
            SELECT * FROM steps_track_record
            WHERE instant = :epochSeconds
        """
    )
    suspend fun getRecordByEpochSeconds(epochSeconds: Long): StepsTrackRecordEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceRecords(authorEntities: List<StepsTrackRecordEntity>): List<Long>

    @Update
    suspend fun updateRecords(entities: List<StepsTrackRecordEntity>)

    @Query(
        """
            SELECT * FROM steps_track_record 
            WHERE instant BETWEEN :startEpochSeconds AND :endEpochSeconds
        """
    )
    suspend fun fetchRecords(
        startEpochSeconds: Long,
        endEpochSeconds: Long
    ): List<StepsTrackRecordEntity>

}
