@file:Suppress("PrivatePropertyName")

package dev.step.app.data.kvstore

import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.time.thisWeekStartDateTime
import dev.step.app.androidplatform.ext.withId
import dev.step.app.data.pojo.remoteconfig.RewardBubblesShowState
import dev.step.app.data.pojo.wallet.GameRemaining
import dev.step.app.data.pojo.wallet.RedeemPictures
import dev.step.app.data.pojo.wallet.SignIn
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant

private const val TAG: String = "WalletBizKv"

class WalletBizKv {

    private val mmkv = AppMMKV.withId(TAG)

    private val key_coin_balance = TAG + "_key_coin_balance"
    fun getCoinBalance() = mmkv.decodeInt(key_coin_balance, 0)
    fun setCoinBalance(coins: Int) = mmkv.encode(key_coin_balance, coins)

    private val key_sign_in = TAG + "_key_sign_in"
    fun getSignIn(dayOfWeek: DayOfWeek) =
        if (dayOfWeek == DayOfWeek.SUNDAY) {
            signInConfigure(nowInstant()) // refresh sign in data when SUNDAY
            mmkv.decodeParcelable(key_sign_in, SignIn::class.java) ?: SignIn()
        } else {
            mmkv.decodeParcelable(key_sign_in, SignIn::class.java) ?: SignIn()
        }

    fun setSignIn(signIn: SignIn) = mmkv.encode(key_sign_in, signIn)

    private val key_last_sign_in_week_start_seconds = TAG + "_key_last_sign_in_week_start_seconds"
    fun getLastSignInWeekStartInstant() = mmkv.decodeLong(key_last_sign_in_week_start_seconds, 0L)
        .let { Instant.fromEpochSeconds(it) }

    fun signInConfigure(currentInstant: Instant) {
        val tz = TimeZone.currentSystemDefault()

        val weekStartInstant = currentInstant.thisWeekStartDateTime(tz).toInstant(tz)
        val lastSignInWeekStartInstant = getLastSignInWeekStartInstant()

        if (weekStartInstant.epochSeconds != lastSignInWeekStartInstant.epochSeconds) { // not same week
            mmkv.encode(key_last_sign_in_week_start_seconds, weekStartInstant.epochSeconds)

            setSignIn(SignIn())
        }
    }

    private val key_sign_in_times = TAG + "_key_sign_in_times"
    private fun setSignInTimes(times: Int) = mmkv.encode(key_sign_in_times, times)
    fun getSignInTimes() = mmkv.decodeInt(key_sign_in_times, 0)

    fun doSignIn(dayOfWeek: DayOfWeek) {
        val signInTimes = getSignInTimes()
        setSignInTimes(signInTimes + 1)

        val signIn = getSignIn(dayOfWeek)

        when (dayOfWeek) {
            DayOfWeek.SUNDAY -> {
                setSignIn(SignIn(sun = true))
            }

            DayOfWeek.MONDAY -> {
                setSignIn(signIn.copy(mon = true))
            }

            DayOfWeek.TUESDAY -> {
                setSignIn(signIn.copy(tue = true))
            }

            DayOfWeek.WEDNESDAY -> {
                setSignIn(signIn.copy(wed = true))
            }

            DayOfWeek.THURSDAY -> {
                setSignIn(signIn.copy(thu = true))
            }

            DayOfWeek.FRIDAY -> {
                setSignIn(signIn.copy(fri = true))
            }

            DayOfWeek.SATURDAY -> {
                setSignIn(signIn.copy(sat = true))
            }
        }
    }

// ---------------------------------------------------------------------------------------------


    private val key_stage_one_task_done_instant = TAG + "_key_stage_one_task_done_instant"
    private val key_stage_two_task_done_instant = TAG + "_key_stage_two_task_done_instant"

    fun doneStageOneTask(instant: Instant) {
        mmkv.encode(key_stage_one_task_done_instant, instant.epochSeconds)
    }

    fun getStageOneTaskInstant(): Instant {
        return mmkv.decodeLong(key_stage_one_task_done_instant, 0L)
            .let { Instant.fromEpochSeconds(it) }
    }

    fun doneStageTwoTask(instant: Instant) {
        mmkv.encode(key_stage_two_task_done_instant, instant.epochSeconds)
    }

    fun getStageTwoTaskInstant(): Instant {
        return mmkv.decodeLong(key_stage_two_task_done_instant, 0L)
            .let { Instant.fromEpochSeconds(it) }
    }

// ---------------------------------------------------------------------------------------------

    private val key_is_new_user = TAG + "_key_is_new_user"

    fun isNewUser() = mmkv.decodeBool(key_is_new_user, true)
    fun setNewUserState(isNewUser: Boolean) = mmkv.encode(key_is_new_user, isNewUser)


    private val key_is_new_user_dialog_has_show = TAG + "_key_is_new_user_dialog_has_show"

    fun isNewUserDialogHasShow() = mmkv.decodeBool(key_is_new_user_dialog_has_show, false)
    fun setNewUserDialogHasShow(hasShow: Boolean) =
        mmkv.encode(key_is_new_user_dialog_has_show, hasShow)

// ---------------------------------------------------------------------------------------------

    //
    private val key_game1_remaining = TAG + "_key_game1_remaining"

    fun getGame1Remaining(todayStartInstantSeconds: Long): GameRemaining {
        val remaining =
            mmkv.decodeParcelable(key_game1_remaining, GameRemaining::class.java)
                ?: GameRemaining()

        debugLog("$TAG getSuperWheelRemaining remaining: $remaining")

        return if (todayStartInstantSeconds > remaining.dayStartInstantSeconds) {
            val newRemaining = remaining.copy(
                dayStartInstantSeconds = todayStartInstantSeconds,
                remainingTimes = 8
            )
            mmkv.encode(key_game1_remaining, newRemaining)
            newRemaining
        } else {
            remaining
        }
    }

    fun spendOnceGame1() {
        val remaining =
            mmkv.decodeParcelable(key_game1_remaining, GameRemaining::class.java)
                ?: return

        if (remaining.remainingTimes > 0) {
            mmkv.encode(
                key_game1_remaining,
                remaining.copy(remainingTimes = remaining.remainingTimes - 1)
            )
        }
    }

    private val key_game2_remaining = TAG + "_key_game2_remaining"

    fun getGame2Remaining(
        todayStartInstantSeconds: Long,
        remainingLimit: Int
    ): GameRemaining {
        val remaining =
            mmkv.decodeParcelable(key_game2_remaining, GameRemaining::class.java)
                ?: GameRemaining()


        return if (todayStartInstantSeconds > remaining.dayStartInstantSeconds) {
            val newRemaining = remaining.copy(
                dayStartInstantSeconds = todayStartInstantSeconds,
                remainingTimes = remainingLimit
            )
            mmkv.encode(key_game2_remaining, newRemaining)
            newRemaining
        } else {
            remaining
        }
    }

    fun spendOnceGame2Remaining() {
        val remaining =
            mmkv.decodeParcelable(key_game2_remaining, GameRemaining::class.java)
                ?: return

        if (remaining.remainingTimes > 0) {
            mmkv.encode(
                key_game2_remaining,
                remaining.copy(remainingTimes = remaining.remainingTimes - 1)
            )
        }
    }

    private val key_game3_remaining = TAG + "_key_game3_remaining"

    fun getGame3Remaining(
        todayStartInstantSeconds: Long,
        freeSpinsRemainingLimit: Int
    ): GameRemaining {
        val remaining =
            mmkv.decodeParcelable(key_game3_remaining, GameRemaining::class.java)
                ?: GameRemaining()


        return if (todayStartInstantSeconds > remaining.dayStartInstantSeconds) {
            val newRemaining = remaining.copy(
                dayStartInstantSeconds = todayStartInstantSeconds,
                remainingTimes = freeSpinsRemainingLimit
            )
            mmkv.encode(key_game3_remaining, newRemaining)
            newRemaining
        } else {
            remaining
        }
    }

    fun spendOnceGame3Remaining() {
        val remaining =
            mmkv.decodeParcelable(key_game3_remaining, GameRemaining::class.java)
                ?: return

        if (remaining.remainingTimes > 0) {
            mmkv.encode(
                key_game3_remaining,
                remaining.copy(remainingTimes = remaining.remainingTimes - 1)
            )
        }
    }
    // ----------------------------------------------------------------------------------------

    // ------------------------------------------------------------------------------------------

    private val key_reward_bubbles_reset_instant = TAG + "_key_reward_bubbles_reset_instant"

    fun getRewardBubblesResetInstant(): Instant {
        return mmkv.decodeLong(key_reward_bubbles_reset_instant, 0).let(Instant::fromEpochSeconds)
    }

    private val key_reward_bubbles_show_state = TAG + "_key_reward_bubbles_show_state"

    fun rewardBubbleGone(bubbleId: Int): RewardBubblesShowState {
        var rewardBubblesShowState = getRewardBubbleShowState()

        when (bubbleId) {
            1 -> rewardBubblesShowState = rewardBubblesShowState.copy(bubble1NeedShow = false)
            2 -> rewardBubblesShowState = rewardBubblesShowState.copy(bubble2NeedShow = false)
            3 -> rewardBubblesShowState = rewardBubblesShowState.copy(bubble3NeedShow = false)
            4 -> rewardBubblesShowState = rewardBubblesShowState.copy(bubble4NeedShow = false)
        }

        mmkv.encode(key_reward_bubbles_show_state, rewardBubblesShowState)

        return rewardBubblesShowState
    }

    fun resetRewardBubbleShowState(instant: Instant): RewardBubblesShowState {
        val reset = RewardBubblesShowState(true, true, true, true)
        mmkv.encode(key_reward_bubbles_show_state, reset)
        mmkv.encode(key_reward_bubbles_reset_instant, instant.epochSeconds)
        return reset
    }

    fun getRewardBubbleShowState(): RewardBubblesShowState {
        return mmkv.decodeParcelable(
            key_reward_bubbles_show_state,
            RewardBubblesShowState::class.java
        ) ?: RewardBubblesShowState(true, true, true, true)
    }

    // -------------------------------------------------------------------------------------------

    private val key_redeem_pictures = TAG + "_key_redeem_pictures"

    fun redeemPictures(): RedeemPictures {
        return mmkv.decodeParcelable(key_redeem_pictures, RedeemPictures::class.java)
            ?: RedeemPictures.Default
    }

    fun unlockRedeemPicture(id: Int) {
        val list = redeemPictures().list.toMutableList()

        val index = list.indexOfFirst { id == it.id }
        list[index] = list[index].copy(unlock = true)

        val newRps = RedeemPictures(list.sortedBy { it.id })

        mmkv.encode(key_redeem_pictures, newRps)
    }

    // -------------------------------------------------------------------------------------------

}
