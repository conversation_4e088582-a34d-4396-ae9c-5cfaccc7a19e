@file:Suppress("PrivatePropertyName")

package dev.step.app.data.kvstore

import android.content.Context
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import dev.step.app.androidplatform.biz.TenjinAttribution
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.ktserialization.toJsonString
import dev.step.app.androidplatform.ext.ktserialization.toObjOrNull
import dev.step.app.androidplatform.ext.time.nowInstant
import dev.step.app.androidplatform.ext.withId
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant

private const val TAG = "UserOperateDataKv"

class UserOperateDataKv(
    private val context: Context,
) {

    private val kv = AppMMKV.withId(TAG)

    //attribution
    private val TENJIN_ATTR = "key_tenjin_attr"

    val tenjinAttr
        get() = kv.decodeString(TENJIN_ATTR, null)?.toObjOrNull<TenjinAttribution>()
            ?: TenjinAttribution.Empty

    fun saveTenjinAttribution(tenjinAttr: TenjinAttribution) {
        kv.encode(TENJIN_ATTR, tenjinAttr.toJsonString())
    }

    private val LAST_TENJIN_INIT_SUCCESS_INSTANT = "key_last_tenjin_init_success_instant"

    val lastTenjinInitSuccessInstant
        get() = kv.decodeLong(LAST_TENJIN_INIT_SUCCESS_INSTANT, 0).let(Instant::fromEpochSeconds)

    fun storeTenjinInitSuccessInstant(instant: Instant) {
        kv.encode(LAST_TENJIN_INIT_SUCCESS_INSTANT, instant.epochSeconds)
    }

    private val FIRST_TIME_LAUNCH_APP_INSTANT = "key_first_time_launch_app_instant"

    val firstTimeLaunchAppInstant
        get() = kv.decodeLong(FIRST_TIME_LAUNCH_APP_INSTANT, 0).takeIf { it > 0 }
            ?.let(Instant::fromEpochSeconds)

    private fun firstTimeLaunchAppInstant(instant: Instant) {
        kv.encode(FIRST_TIME_LAUNCH_APP_INSTANT, instant.epochSeconds)
    }

    fun tryToConfigureFirstLaunch() {
        val firstLaunchInstant = firstTimeLaunchAppInstant
        if (firstLaunchInstant == null) {
            firstTimeLaunchAppInstant(nowInstant())
        }
    }

    private val OPEN_RATING_DIALOG_TIMES = "key_open_rating_dialog_times"

    val openRatingDialogTimes get() = kv.decodeInt(OPEN_RATING_DIALOG_TIMES, 0)

    fun setOpenRatingDialogTimes(count: Int) {
        kv.encode(OPEN_RATING_DIALOG_TIMES, count)
    }

    // -----------------------------------------------------------------------------------------

    private val IS_TENJIN_REQUESTED = "key_is_tenjin_requested"
    private val isTenjinRequested get() = kv.decodeBool(IS_TENJIN_REQUESTED, false)
    fun doingTenjinRequestForEventRecord(block: () -> Unit) {
        if (!isTenjinRequested) {
            block()
            kv.encode(IS_TENJIN_REQUESTED, true)
        }
    }

    // -----------------------------------------------------------------------------------------

    private val IS_TENJIN_REQUEST_RESULT = "key_is_tenjin_request_result"
    private val isTenjinRequestResult get() = kv.decodeBool(IS_TENJIN_REQUEST_RESULT, false)
    fun doneTenjinRequestResultForEventRecord(block: () -> Unit) {
        if (!isTenjinRequestResult) {
            block()
            kv.encode(IS_TENJIN_REQUEST_RESULT, true)
        }
    }

    // -----------------------------------------------------------------------------------------

    private val HAS_BEEN_GUIDED = "key_has_been_guided"
    var hasBeenGuided
        get() = kv.decodeBool(HAS_BEEN_GUIDED, false)
        set(value) {
            kv.encode(HAS_BEEN_GUIDED, value)
        }

    // -----------------------------------------------------------------------------------------

    suspend fun gaid() = withContext(Dispatchers.IO) {
        val key = "key_gaid"

        val adId = kv.decodeString(key, null)

        return@withContext if (adId.isNullOrEmpty()) {
            try {
                val resultAdId = AdvertisingIdClient.getAdvertisingIdInfo(context).id
                if (resultAdId != null) {
                    kv.encode(key, resultAdId)
                }

                resultAdId
            } catch (e: Exception) {
                null
            }
        } else {
            adId
        }
    }
}

