package dev.step.app.data.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import dev.step.app.data.db.dao.StepsTrackRecordDao
import dev.step.app.data.db.model.StepsTrackRecordEntity
import dev.step.app.data.db.util.InstantConverter

@Database(
    entities = [
        StepsTrackRecordEntity::class
    ],
    version = 1,
    exportSchema = true,
)
@TypeConverters(
    InstantConverter::class,
)
abstract class StepAppDB : RoomDatabase() {

    abstract fun stepsTrackRecordDao(): StepsTrackRecordDao

}
