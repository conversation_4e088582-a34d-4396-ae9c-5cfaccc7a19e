package dev.step.app.data.db.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import dev.step.app.data.pojo.StepsTrackRecord
import kotlinx.datetime.Instant

@Entity(
    tableName = "steps_track_record",
    indices = [Index(value = ["instant"], unique = true)]
)
data class StepsTrackRecordEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val instant: Instant, // per hours
    val steps: Int,
)

fun StepsTrackRecordEntity.asStepsTrackRecord() = StepsTrackRecord(
    instant = instant,
    steps = steps,
)
