package dev.step.app.data.adt

sealed class GenderSetting(val sid: Int) {
    data object Others : GenderSetting(0)
    data object Female : GenderSetting(1)
    data object Male : GenderSetting(2)

    companion object {
        fun valueOf(sid: Int): GenderSetting? {
            return when (sid) {
                Others.sid -> Others
                Female.sid -> Female
                Male.sid -> Male
                else -> null
            }
        }
    }
}
