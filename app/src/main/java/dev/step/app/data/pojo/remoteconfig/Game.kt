@file:Suppress("PropertyName")

package dev.step.app.data.pojo.remoteconfig

import kotlinx.serialization.Serializable

//language=json
private val json0 by lazy {
    """
{
  "award1_coins": 1000,
  "award1_percentage": 90,
  "award2_coins": 2000,
  "award2_percentage": 5,
  "award3_coins": 3000,
  "award3_percentage": 4,
  "award4_coins": 5000,
  "award4_percentage": 1,
  "award_multiplier": 4,
  "play_times": 8
}
""".trimIndent()
}

@Serializable
data class Game1(
    val award1_coins: Int,
    val award1_percentage: Int,
    val award2_coins: Int,
    val award2_percentage: Int,
    val award3_coins: Int,
    val award3_percentage: Int,
    val award4_coins: Int,
    val award4_percentage: Int,
    val award_multiplier: Int,
    val play_times: Int
) {
    companion object {
        val Default = Game1(
            1000,
            90,
            2000,
            5,
            3000,
            4,
            5000,
            1,
            4,
            8
        )
    }
}

//language=json
private val json1 by lazy {
    """
{
  "award_coins": 1000,
  "award_multiplier": 4,
  "play_times": 5
}
""".trimIndent()
}

@Serializable
data class Game2(
    val award_coins: Int,
    val award_multiplier: Int,
    val play_times: Int,
) {
    companion object {
        val Default = Game2(
            award_coins = 1000,
            award_multiplier = 4,
            play_times = 5,
        )
    }
}

//language=json
private val json2 by lazy {
    """
{
  "award_coins": 1500,
  "award_multiplier": 4,
  "play_times": 10
}
""".trimIndent()
}

@Serializable
data class Game3(
    val award_coins: Int,
    val award_multiplier: Int,
    val play_times: Int,
) {
    companion object {
        val Default = Game3(
            award_coins = 1500,
            award_multiplier = 4,
            play_times = 10,
        )
    }
}