package dev.step.app.data.repo

import dev.step.app.androidplatform.ext.time.*
import dev.step.app.androidplatform.memorystore.StepTrackMmkvStore
import dev.step.app.data.db.dao.StepsTrackRecordDao
import dev.step.app.data.db.model.asStepsTrackRecord
import dev.step.app.data.pojo.DayReportsData
import dev.step.app.data.pojo.DayStepsData
import dev.step.app.data.pojo.StepsTrackRecord
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant

class StepsTrackRecordRepo(
    private val stepsTrackRecordDao: StepsTrackRecordDao,
    private val stepTrackMmkvStore: StepTrackMmkvStore,
) {

    suspend fun dayRecords(dayInstant: Instant): List<StepsTrackRecord> {
        val deviceTimeZone = TimeZone.currentSystemDefault()

        val dayStartInstant = dayInstant.todayStartInstant(deviceTimeZone)
        val dayEndInstant = dayInstant.todayEndInstant(deviceTimeZone)

        return stepsTrackRecordDao.fetchRecords(
            dayStartInstant.epochSeconds,
            dayEndInstant.epochSeconds
        ).map {
            if (it.instant.epochSeconds == stepTrackMmkvStore.lastActiveHourInstantEpochSeconds) {
                it.copy(steps = it.steps + stepTrackMmkvStore.lastActiveHourSteps)
                    .asStepsTrackRecord()
            } else {
                it.asStepsTrackRecord()
            }
        }
    }

    suspend fun weekRecords(dayInstant: Instant): List<StepsTrackRecord> {
        val deviceTimeZone = TimeZone.currentSystemDefault()

        val thisWeekStartDateTime = dayInstant.thisWeekStartDateTime(deviceTimeZone)
        val thisWeekEndDateTime = dayInstant.thisWeekEndDateTime(deviceTimeZone)

        val thisWeekStartInstant = thisWeekStartDateTime.toInstant(deviceTimeZone)
        val thisWeekEndInstant = thisWeekEndDateTime.toInstant(deviceTimeZone)

        return stepsTrackRecordDao.fetchRecords(
            thisWeekStartInstant.epochSeconds,
            thisWeekEndInstant.epochSeconds
        ).map {
            if (it.instant.epochSeconds == stepTrackMmkvStore.lastActiveHourInstantEpochSeconds) {
                it.copy(steps = it.steps + stepTrackMmkvStore.lastActiveHourSteps)
                    .asStepsTrackRecord()
            } else {
                it.asStepsTrackRecord()
            }
        }
    }

    suspend fun thisMonthRecords(dayInstant: Instant): List<StepsTrackRecord> {
        val deviceTimeZone = TimeZone.currentSystemDefault()

        val thisMonthStartInstant = dayInstant.thisMonthStartInstant(deviceTimeZone)
        val thisMonthEndInstant = dayInstant.thisMonthEndInstant(deviceTimeZone)

        return stepsTrackRecordDao.fetchRecords(
            thisMonthStartInstant.epochSeconds,
            thisMonthEndInstant.epochSeconds
        ).map {
            if (it.instant.epochSeconds == stepTrackMmkvStore.lastActiveHourInstantEpochSeconds) {
                it.copy(steps = it.steps + stepTrackMmkvStore.lastActiveHourSteps)
                    .asStepsTrackRecord()
            } else {
                it.asStepsTrackRecord()
            }
        }
    }

    suspend fun todayStepsData(instant: Instant): DayStepsData {
        val deviceTimeZone = TimeZone.currentSystemDefault()

        val todayRecords = dayRecords(instant)
        val todayInstant = instant.todayStartDateTime(deviceTimeZone).toInstant(deviceTimeZone)

        var stepsSum = 0
        todayRecords.forEach { (_, steps) ->
            stepsSum += steps
        }

        return DayStepsData(todayInstant, stepsSum)
    }

    suspend fun weekStepsData(dayInstant: Instant): List<DayStepsData> {
        val deviceTimeZone = TimeZone.currentSystemDefault()

        val thisWeekRecords = weekRecords(dayInstant)

        val weekDateMap = linkedMapOf<Long, Int>()

        dayInstant.thisWeekInstants(deviceTimeZone).forEach { instant ->
            weekDateMap[instant.epochSeconds] = 0
        }

        thisWeekRecords.forEach { (instant, steps) ->
            val queryIns = instant.todayStartInstant()
            weekDateMap[queryIns.epochSeconds] =
                weekDateMap.getOrDefault(queryIns.epochSeconds, 0) + steps
        }

        return weekDateMap.entries.map { DayStepsData(Instant.fromEpochSeconds(it.key), it.value) }
    }

    suspend fun monthStepsData(dayInstant: Instant): List<DayStepsData> {
        val deviceTimeZone = TimeZone.currentSystemDefault()

        val thisMonthRecords = thisMonthRecords(dayInstant)

        val monthDateMap = linkedMapOf<Long, Int>()

        dayInstant.thisMonthInstants(deviceTimeZone).forEach { instant ->
            monthDateMap[instant.epochSeconds] = 0
        }

        thisMonthRecords.forEach { (instant, steps) ->
            val queryIns = instant.todayStartInstant()
            monthDateMap[queryIns.epochSeconds] =
                monthDateMap.getOrDefault(queryIns.epochSeconds, 0) + steps
        }

        return monthDateMap.entries.map { DayStepsData(Instant.fromEpochSeconds(it.key), it.value) }
    }

    suspend fun dayReportsData(dayInstant: Instant): DayReportsData {
        val deviceTimeZone = TimeZone.currentSystemDefault()

        val records = dayRecords(dayInstant)

        val hourOfDayDataMap = linkedMapOf<Long, Int>()

        dayInstant.todayInstants(deviceTimeZone).forEach { instant ->
            hourOfDayDataMap[instant.epochSeconds] = 0
        }

        records.forEach { (instant, steps) ->
            hourOfDayDataMap[instant.epochSeconds] = hourOfDayDataMap.getOrDefault(instant.epochSeconds, 0) + steps
        }

        return DayReportsData(
            dayInstant.todayStartInstant(deviceTimeZone),
            hourOfDayDataMap.entries.map { StepsTrackRecord(Instant.fromEpochSeconds(it.key), it.value) }
        )
    }

}
