package dev.step.app.data.kvstore

import dev.step.app.androidplatform.cmToFtIn
import dev.step.app.androidplatform.ext.AppMMKV
import dev.step.app.androidplatform.ext.withId
import dev.step.app.androidplatform.ftInToCm
import dev.step.app.androidplatform.kgToLb
import dev.step.app.androidplatform.lbToKg
import dev.step.app.androidplatform.stepLengthByBodyHeight
import dev.step.app.data.adt.GenderSetting
import dev.step.app.data.adt.MeasurementUnit
import dev.step.app.data.adt.MotionSensorSensitivity
import dev.step.app.ui.screen.guideprofilesettings.GuideProfileSettingsViewState
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.Locale

private const val TAG = "UserSettingsDataKv"

@Suppress("PrivatePropertyName", "LocalVariableName")
class UserSettingsDataKv {
    private val kv = AppMMKV.withId(TAG)

    private val KEY_GENDER = "KEY_GENDER"

    val gender
        get() = kv.decodeInt(KEY_GENDER, -1).let {
            GenderSetting.valueOf(it)
        }


    fun changeGender(genderSetting: GenderSetting) {
        kv.encode(KEY_GENDER, genderSetting.sid)
    }

    // ------------------------------------------------------------------------------------------

    private val KEY_STEPS_GOAL = "KEY_STEPS_GOAL"

    val stepsGoal
        get() = kv.decodeInt(KEY_STEPS_GOAL, 6000)

    fun changeStepsGoal(steps: Int) {
        kv.encode(KEY_STEPS_GOAL, steps)
    }

    // ------------------------------------------------------------------------------------------

    private val KEY_MOTION_SENSOR_SENSITIVITY = "KEY_MOTION_SENSOR_SENSITIVITY"

    val motionSensorSensitivity
        get() = kv.decodeInt(KEY_MOTION_SENSOR_SENSITIVITY, MotionSensorSensitivity.Lv1.sid).let {
            MotionSensorSensitivity.valueOf(it)
        }

    fun changeMotionSensorSensitivity(sensitivity: MotionSensorSensitivity) {
        kv.encode(KEY_MOTION_SENSOR_SENSITIVITY, sensitivity.sid)
    }

    // ------------------------------------------------------------------------------------------
    private val KEY_BODY_WEIGHT_LB = "KEY_BODY_WEIGHT_LB"
    private val KEY_BODY_WEIGHT_KG = "KEY_BODY_WEIGHT_KG"

    val bodyWeightLb get() = kv.decodeFloat(KEY_BODY_WEIGHT_LB)
    val bodyWeightKg get() = kv.decodeFloat(KEY_BODY_WEIGHT_KG)

    private fun changeBodyWeightLb(lb: Float) {
        kv.encode(KEY_BODY_WEIGHT_LB, lb)
    }

    private fun changeBodyWeightKg(kg: Float) {
        kv.encode(KEY_BODY_WEIGHT_KG, kg)
    }

    fun changeBodyWeight(
        lb: Float? = null,
        kg: Float? = null,
        mus: MeasurementUnit
    ) {
        when (mus) {
            MeasurementUnit.Metric -> {
                requireNotNull(kg)
                changeBodyWeightKg(kg)

                val _lb = kgToLb(kg)
                changeBodyWeightLb(_lb)
            }

            MeasurementUnit.Imperial -> {
                requireNotNull(lb)
                changeBodyWeightLb(lb)

                val _kg = lbToKg(lb)
                changeBodyWeightKg(_kg)
            }
        }
    }

    // ------------------------------------------------------------------------------------------
    private val KEY_BODY_HEIGHT_FT = "KEY_BODY_HEIGHT_FT"
    private val KEY_BODY_HEIGHT_IN = "KEY_BODY_HEIGHT_IN"
    private val KEY_BODY_HEIGHT_CM = "KEY_BODY_HEIGHT_CM"

    val bodyHeightFtIn
        get(): Pair<Int, Int> {
            return kv.decodeInt(KEY_BODY_HEIGHT_FT) to kv.decodeInt(KEY_BODY_HEIGHT_IN)
        }
    val bodyHeightCm get() = kv.decodeFloat(KEY_BODY_HEIGHT_CM)

    private fun changeBodyHeight(ft: Int, `in`: Int) {
        kv.encode(KEY_BODY_HEIGHT_FT, ft)
        kv.encode(KEY_BODY_HEIGHT_IN, `in`)
    }

    private fun changeBodyHeight(cm: Float) {
        kv.encode(KEY_BODY_HEIGHT_CM, cm)
    }

    fun changeBodyHeight(
        ft: Int? = null,
        `in`: Int? = null,
        cm: Float? = null,
        mus: MeasurementUnit
    ) {
        when (mus) {
            MeasurementUnit.Metric -> {
                requireNotNull(cm)
                changeBodyHeight(cm)

                val (_ft, _in) = cmToFtIn(cm)
                changeBodyHeight(_ft, _in)
            }

            MeasurementUnit.Imperial -> {
                requireNotNull(ft)
                requireNotNull(`in`)
                changeBodyHeight(ft, `in`)

                val _cm = ftInToCm(ft to `in`)

                changeBodyHeight(_cm)
            }
        }
    }


    // ------------------------------------------------------------------------------------------

    private val KEY_STEP_LENGTH_FT = "KEY_STEP_LENGTH_FT"
    private val KEY_STEP_LENGTH_IN = "KEY_STEP_LENGTH_IN"
    private val KEY_STEP_LENGTH_CM = "KEY_STEP_LENGTH_CM"

    val stepLengthFtIn: Pair<Int?, Int?>
        get() {
            return Pair(
                kv.decodeInt(KEY_STEP_LENGTH_FT, -1).takeIf { it != -1 },
                kv.decodeInt(KEY_STEP_LENGTH_IN, -1).takeIf { it != -1 }
            )
        }
    val stepLengthCm get() = kv.decodeFloat(KEY_STEP_LENGTH_CM, stepLengthByBodyHeight(bodyHeightCm))

    private fun changeStepLength(ft: Int, `in`: Int) {
        kv.encode(KEY_STEP_LENGTH_FT, ft)
        kv.encode(KEY_STEP_LENGTH_IN, `in`)
    }

    private fun changeStepLength(cm: Float) {
        kv.encode(KEY_STEP_LENGTH_CM, cm)
    }

    fun changeStepLength(
        ft: Int? = null,
        `in`: Int? = null,
        cm: Float? = null,
        mus: MeasurementUnit
    ) {
        when (mus) {
            MeasurementUnit.Metric -> {
                requireNotNull(cm)
                changeStepLength(cm)

                val (_ft, _in) = cmToFtIn(cm)
                changeStepLength(_ft, _in)
            }

            MeasurementUnit.Imperial -> {
                requireNotNull(ft)
                requireNotNull(`in`)
                changeStepLength(ft, `in`)

                val _cm = ftInToCm(ft to `in`)

                changeStepLength(_cm)
            }
        }
    }

    // ------------------------------------------------------------------------------------------

    private val KEY_MEASUREMENT_UNIT = "KEY_MEASUREMENT_UNIT"

    val measurementUnit
        get() = kv.decodeInt(KEY_MEASUREMENT_UNIT).let {
            MeasurementUnit.valueOf(it)
        }

    fun changeMeasurementUnit(measurementUnitSetting: MeasurementUnit) {
        kv.encode(KEY_MEASUREMENT_UNIT, measurementUnitSetting.sid)
    }

    // ------------------------------------------------------------------------------------------

    fun configureDefaultSettings() {
        GlobalScope.launch {
            val region = Locale.getDefault().country.uppercase()

            val mus = if (region == "US" || region == "GB") {
                MeasurementUnit.Imperial
            } else {
                MeasurementUnit.Metric
            }

            val othersDefaultState = GuideProfileSettingsViewState.OthersDefaultState

            changeGender(othersDefaultState.gender)

            changeMeasurementUnit(mus)
            changeBodyWeight(
                lb = othersDefaultState.bodyWeightLb,
                kg = othersDefaultState.bodyWeightKg,
                mus = mus
            )
            changeBodyHeight(
                ft = othersDefaultState.bodyHeightFt,
                `in` = othersDefaultState.bodyHeightIn,
                cm = othersDefaultState.bodyHeightCm.toFloat(),
                mus = mus
            )
        }
    }

}
