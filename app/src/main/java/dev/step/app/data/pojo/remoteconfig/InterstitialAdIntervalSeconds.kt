package dev.step.app.data.pojo.remoteconfig

import kotlinx.serialization.Serializable

//language=json
private val json= """
    {
      "for_o_user": 300,
      "for_p_user": 150
    }
""".trimIndent()
@Serializable
data class InterstitialAdIntervalSeconds(
    var for_o_user: Int = 300,
    var for_p_user: Int = 150,
) {
    companion object {
        val Default = InterstitialAdIntervalSeconds()
    }
}