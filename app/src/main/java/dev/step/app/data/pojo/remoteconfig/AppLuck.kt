@file:Suppress("PropertyName")

package dev.step.app.data.pojo.remoteconfig

import kotlinx.serialization.Serializable

//language=json
private val json0 = """
    {
      "url1": "",
      "url2": "",
      "url3": "",
      "url4": "",
      "url5": ""
    }
""".trimIndent()

@Serializable
data class AppLuckUrls(
    val url1: String = "",
    val url2: String = "",
    val url3: String = "",
    val url4: String = "",
    val url5: String = "",
)

//language=json
private val json1 = """
    {
      "enabled": false,
      "enable_after_of_minutes": 30
    }
""".trimIndent()

@Serializable
data class AppLuckSwitch(
    val enabled: Boolean = false,
    val enable_after_of_minutes: Int = 30
)