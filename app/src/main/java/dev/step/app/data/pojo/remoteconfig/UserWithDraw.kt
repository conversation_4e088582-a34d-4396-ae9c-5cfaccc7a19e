package dev.step.app.data.pojo.remoteconfig

import kotlinx.serialization.Serializable

//language=json
private val OUserWithDrawJSON = """
{
  "amount1_coins": 1000,
  "amount1_off": 11,
  "amount2_coins": 2000,
  "amount2_off": 12,
  "amount3_coins": 3000,
  "amount3_off": 13,
  "amount4_coins": 4000,
  "amount4_off": 14,
  "one_unit_exchange_coins": 100,
  "currency_unit_size": 1,
  "val currency_text": "$",
  "currency_text_placement_is_start": true
}
""".trimIndent()

@Serializable
data class OUserWithDraw(
    val amount1_coins: Int,
    val amount1_off: Int,
    val amount2_coins: Int,
    val amount2_off: Int,
    val amount3_coins: Int,
    val amount3_off: Int,
    val amount4_coins: Int,
    val amount4_off: Int,
    val one_unit_exchange_coins: Int,
    val currency_unit_size: Int,
    val currency_text: String,
    val currency_text_placement_is_start: Boolean
) {
    val coinsExchangeUnit
        get() = CoinsExchangeUnit(
            one_unit_exchange_coins,
            currency_unit_size,
            currency_text,
            currency_text_placement_is_start
        )
}


//language=json
private val PUserWithDrawJSON = """
{
  "amount1_coins": 1000,
  "amount1_amount": 1000,
  "amount2_coins": 2000,
  "amount2_amount": 2000,
  "amount3_coins": 3000,
  "amount3_amount": 3000,
  "amount4_coins": 4000,
  "amount4_amount": 4000,
  "one_unit_exchange_coins": 100,
  "currency_unit_size": 1,
  "currency_text": "$",
  "currency_text_placement_is_start": true
}
""".trimIndent()
@Serializable
data class PUserWithDraw(
    val amount1_coins: Int,
    val amount1_amount: Int,
    val amount2_coins: Int,
    val amount2_amount: Int,
    val amount3_coins: Int,
    val amount3_amount: Int,
    val amount4_coins: Int,
    val amount4_amount: Int,
    val one_unit_exchange_coins: Int,
    val currency_unit_size: Int,
    val currency_text: String,
    val currency_text_placement_is_start: Boolean
) {
    val coinsExchangeUnit
        get() = CoinsExchangeUnit(
            one_unit_exchange_coins,
            currency_unit_size,
            currency_text,
            currency_text_placement_is_start,
        )
}

data class CoinsExchangeUnit(
    val one_unit_exchange_coins: Int,
    val currency_unit_size: Int,
    val currency_text: String,
    val currency_text_placement_is_start: Boolean
)